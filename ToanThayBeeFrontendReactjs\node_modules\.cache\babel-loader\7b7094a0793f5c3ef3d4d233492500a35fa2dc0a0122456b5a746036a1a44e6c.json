{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\card\\\\ExamCard.jsx\",\n  _s = $RefreshSig$();\nimport ExamDefaultImage from \"../../assets/images/defaultExamImage.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { saveExamForUser } from \"../../features/exam/examSlice\";\nimport React from \"react\";\nimport { Clock, Calendar, BookOpen, GraduationCap, Bookmark, CheckCircle, Lock, Play, Eye, ChevronRight, Tag } from \"lucide-react\";\nimport { motion } from \"framer-motion\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formatDate = dateString => {\n  if (!dateString) return \"\";\n  const date = new Date(dateString);\n  return date.toLocaleDateString(\"vi-VN\", {\n    day: \"2-digit\",\n    month: \"2-digit\",\n    year: \"numeric\"\n  });\n};\nconst ExamCard = _ref => {\n  _s();\n  var _codes$chapter, _codes$chapter$find, _codes$examType2, _codes$examType2$find;\n  let {\n    exam,\n    codes,\n    horizontal = false\n  } = _ref;\n  const {\n    name,\n    typeOfExam,\n    class: examClass,\n    chapter,\n    testDuration,\n    createdAt,\n    imageUrl,\n    id,\n    isSave,\n    isDone,\n    acceptDoExam = true\n  } = exam;\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const handleClicked = () => navigate(\"/practice/exam/\".concat(id));\n  const handleSaveExam = e => {\n    e.stopPropagation();\n    dispatch(saveExamForUser({\n      examId: id\n    }));\n  };\n\n  // Status icon component\n  const StatusIcon = () => {\n    // If the exam is already done, show the completed icon regardless of acceptDoExam\n    if (isDone) {\n      return /*#__PURE__*/_jsxDEV(CheckCircle, {\n        size: 16,\n        className: \"text-green-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 20\n      }, this);\n    }\n\n    // If the exam cannot be taken, show a lock icon\n    if (!acceptDoExam) {\n      return /*#__PURE__*/_jsxDEV(Lock, {\n        size: 16,\n        className: \"text-orange-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 20\n      }, this);\n    }\n\n    // Default: exam can be taken but hasn't been completed yet\n    return /*#__PURE__*/_jsxDEV(Clock, {\n      size: 16,\n      className: \"text-sky-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 16\n    }, this);\n  };\n\n  // Exam details items\n  const examDetails = [{\n    icon: /*#__PURE__*/_jsxDEV(GraduationCap, {\n      size: 16,\n      className: \"md:mr-2 mr-[0.1rem] text-gray-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 19\n    }, this),\n    label: \"Lớp:\",\n    value: examClass\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(BookOpen, {\n      size: 16,\n      className: \"md:mr-2 mr-[0.1rem] text-gray-500 min-w-[16px]\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 19\n    }, this),\n    label: \"Chương:\",\n    value: chapter ? ((_codes$chapter = codes['chapter']) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || chapter : 'Không có'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Clock, {\n      size: 16,\n      className: \"md:mr-2 mr-[0.1rem] text-gray-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 19\n    }, this),\n    label: \"Thời gian:\",\n    value: testDuration ? testDuration + ' phút' : 'Không có'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Calendar, {\n      size: 16,\n      className: \"md:mr-2 mr-[0.1rem] text-gray-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 19\n    }, this),\n    label: \"Ngày đăng:\",\n    value: formatDate(createdAt)\n  }];\n\n  // Bookmark button\n  const BookmarkButton = () => /*#__PURE__*/_jsxDEV(motion.button, {\n    whileHover: {\n      scale: 1.1\n    },\n    whileTap: {\n      scale: 0.9\n    },\n    onClick: handleSaveExam,\n    className: \"p-2 rounded-full transition-all duration-200 \".concat(isSave ? 'text-sky-600 bg-sky-50 hover:bg-sky-100' : 'text-gray-400 hover:text-sky-600 hover:bg-sky-50'),\n    title: isSave ? \"Đã lưu đề thi\" : \"Lưu đề thi\",\n    children: /*#__PURE__*/_jsxDEV(Bookmark, {\n      size: 16,\n      className: isSave ? 'fill-current' : ''\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 9\n  }, this);\n\n  // Status indicator\n  const StatusIndicator = () => {\n    // Determine the background color based on the exam status\n    let bgColor = 'bg-sky-50'; // Default: can be taken\n    let textColor = 'text-sky-600';\n    if (isDone) {\n      bgColor = 'bg-green-50'; // Completed\n      textColor = 'text-green-600';\n    } else if (!acceptDoExam) {\n      bgColor = 'bg-orange-50'; // Cannot be taken\n      textColor = 'text-orange-500';\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 rounded-full \".concat(bgColor, \" \").concat(textColor),\n      children: /*#__PURE__*/_jsxDEV(StatusIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 13\n    }, this);\n  };\n\n  // Action button\n  const ActionButton = () => {\n    // Determine button style and text based on exam status\n    let buttonStyle = '';\n    let buttonText = '';\n    if (isDone) {\n      // Completed exam\n      buttonStyle = 'bg-green-600 hover:bg-green-700';\n      buttonText = 'Xem lại bài làm';\n    } else if (!acceptDoExam) {\n      // Cannot take exam\n      buttonStyle = 'bg-orange-500 hover:bg-orange-600';\n      buttonText = 'Không thể làm bài';\n    } else {\n      // Can take exam\n      buttonStyle = 'bg-cyan-600 hover:bg-cyan-700';\n      buttonText = 'Bắt đầu làm bài';\n    }\n    return /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"\".concat(buttonStyle, \" text-white py-1.5 sm:py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200 flex items-center justify-center px-4\"),\n      onClick: e => {\n        e.stopPropagation();\n        handleClicked();\n      },\n      disabled: !acceptDoExam && !isDone,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: buttonText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        className: \"ml-2\",\n        children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n          points: \"9 18 15 12 9 6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 13\n    }, this);\n  };\n\n  // Render horizontal layout\n  if (horizontal) {\n    var _codes$examType, _codes$examType$find;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer\",\n      onClick: handleClicked,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 flex flex-col md:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              title: name,\n              className: \"text-base font-semibold font-bevietnam text-black\",\n              children: [name, /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-600 ml-2\",\n                children: codes && ((_codes$examType = codes['exam type']) === null || _codes$examType === void 0 ? void 0 : (_codes$examType$find = _codes$examType.find(c => c.code === typeOfExam)) === null || _codes$examType$find === void 0 ? void 0 : _codes$examType$find.description) || typeOfExam || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 md:hidden\",\n              children: [/*#__PURE__*/_jsxDEV(BookmarkButton, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 grid grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-4\",\n            children: examDetails.map((detail, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-sm text-gray-600\",\n              children: [detail.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [detail.label, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-800\",\n                  children: detail.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 58\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-row md:flex-col items-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(BookmarkButton, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render vertical layout (original)\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer flex flex-col h-full\",\n    onClick: handleClicked,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 sm:p-4 flex-1 flex flex-col\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              title: name,\n              className: \"text-sm font-semibold font-bevietnam text-black flex-1\",\n              children: (name === null || name === void 0 ? void 0 : name.length) > 30 ? (name === null || name === void 0 ? void 0 : name.slice(0, 30)) + \"...\" : name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs font-medium text-gray-800\",\n              children: codes && ((_codes$examType2 = codes['exam type']) === null || _codes$examType2 === void 0 ? void 0 : (_codes$examType2$find = _codes$examType2.find(c => c.code === typeOfExam)) === null || _codes$examType2$find === void 0 ? void 0 : _codes$examType2$find.description) || typeOfExam || ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"items-center sm:flex hidden gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(BookmarkButton, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-center sm:hidden flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(BookmarkButton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-px w-full bg-gray-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap items-center text-xs sm:text-sm text-gray-600 gap-x-2 gap-y-1\",\n          children: examDetails.map((detail, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [index > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-300\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 47\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center shrink-0\",\n              children: [detail.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [detail.label, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-800\",\n                  children: detail.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 58\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 33\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 9\n  }, this);\n};\n_s(ExamCard, \"ZaVe+Vo7W9FMoQ/aTgBrV7UvA04=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = ExamCard;\nexport default ExamCard;\nvar _c;\n$RefreshReg$(_c, \"ExamCard\");", "map": {"version": 3, "names": ["ExamDefaultImage", "useNavigate", "useDispatch", "saveExamForUser", "React", "Clock", "Calendar", "BookOpen", "GraduationCap", "Bookmark", "CheckCircle", "Lock", "Play", "Eye", "ChevronRight", "Tag", "motion", "jsxDEV", "_jsxDEV", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "day", "month", "year", "ExamCard", "_ref", "_s", "_codes$chapter", "_codes$chapter$find", "_codes$examType2", "_codes$examType2$find", "exam", "codes", "horizontal", "name", "typeOfExam", "class", "examClass", "chapter", "testDuration", "createdAt", "imageUrl", "id", "isSave", "isDone", "acceptDoExam", "navigate", "dispatch", "handleClicked", "concat", "handleSaveExam", "e", "stopPropagation", "examId", "StatusIcon", "size", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "examDetails", "icon", "label", "value", "find", "c", "code", "description", "BookmarkButton", "button", "whileHover", "scale", "whileTap", "onClick", "title", "children", "StatusIndicator", "bgColor", "textColor", "ActionButton", "buttonStyle", "buttonText", "disabled", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "points", "_codes$examType", "_codes$examType$find", "map", "detail", "index", "length", "slice", "Fragment", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/card/ExamCard.jsx"], "sourcesContent": ["import ExamDefaultImage from \"../../assets/images/defaultExamImage.png\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { saveExamForUser } from \"../../features/exam/examSlice\";\r\nimport React from \"react\";\r\nimport {\r\n    Clock,\r\n    Calendar,\r\n    BookOpen,\r\n    GraduationCap,\r\n    Bookmark,\r\n    CheckCircle,\r\n    Lock,\r\n    Play,\r\n    Eye,\r\n    ChevronRight,\r\n    Tag\r\n} from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nconst formatDate = (dateString) => {\r\n    if (!dateString) return \"\";\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString(\"vi-VN\", {\r\n        day: \"2-digit\",\r\n        month: \"2-digit\",\r\n        year: \"numeric\",\r\n    });\r\n};\r\n\r\nconst ExamCard = ({ exam, codes, horizontal = false }) => {\r\n    const { name, typeOfExam, class: examClass, chapter, testDuration, createdAt, imageUrl, id, isSave, isDone, acceptDoExam = true } = exam;\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n\r\n    const handleClicked = () => navigate(`/practice/exam/${id}`);\r\n    const handleSaveExam = (e) => {\r\n        e.stopPropagation();\r\n        dispatch(saveExamForUser({ examId: id }));\r\n    };\r\n\r\n    // Status icon component\r\n    const StatusIcon = () => {\r\n        // If the exam is already done, show the completed icon regardless of acceptDoExam\r\n        if (isDone) {\r\n            return <CheckCircle size={16} className=\"text-green-600\" />;\r\n        }\r\n\r\n        // If the exam cannot be taken, show a lock icon\r\n        if (!acceptDoExam) {\r\n            return <Lock size={16} className=\"text-orange-500\" />;\r\n        }\r\n\r\n        // Default: exam can be taken but hasn't been completed yet\r\n        return <Clock size={16} className=\"text-sky-600\" />;\r\n    };\r\n\r\n    // Exam details items\r\n    const examDetails = [\r\n        {\r\n            icon: <GraduationCap size={16} className=\"md:mr-2 mr-[0.1rem] text-gray-500\" />,\r\n            label: \"Lớp:\",\r\n            value: examClass\r\n        },\r\n        {\r\n            icon: <BookOpen size={16} className=\"md:mr-2 mr-[0.1rem] text-gray-500 min-w-[16px]\" />,\r\n            label: \"Chương:\",\r\n            value: chapter ? codes['chapter']?.find(c => c.code === chapter)?.description || chapter : 'Không có'\r\n        },\r\n        {\r\n            icon: <Clock size={16} className=\"md:mr-2 mr-[0.1rem] text-gray-500\" />,\r\n            label: \"Thời gian:\",\r\n            value: testDuration ? testDuration + ' phút' : 'Không có'\r\n        },\r\n        {\r\n            icon: <Calendar size={16} className=\"md:mr-2 mr-[0.1rem] text-gray-500\" />,\r\n            label: \"Ngày đăng:\",\r\n            value: formatDate(createdAt)\r\n        }\r\n    ];\r\n\r\n\r\n\r\n    // Bookmark button\r\n    const BookmarkButton = () => (\r\n        <motion.button\r\n            whileHover={{ scale: 1.1 }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={handleSaveExam}\r\n            className={`p-2 rounded-full transition-all duration-200 ${\r\n                isSave\r\n                    ? 'text-sky-600 bg-sky-50 hover:bg-sky-100'\r\n                    : 'text-gray-400 hover:text-sky-600 hover:bg-sky-50'\r\n            }`}\r\n            title={isSave ? \"Đã lưu đề thi\" : \"Lưu đề thi\"}\r\n        >\r\n            <Bookmark size={16} className={isSave ? 'fill-current' : ''} />\r\n        </motion.button>\r\n    );\r\n\r\n    // Status indicator\r\n    const StatusIndicator = () => {\r\n        // Determine the background color based on the exam status\r\n        let bgColor = 'bg-sky-50'; // Default: can be taken\r\n        let textColor = 'text-sky-600';\r\n\r\n        if (isDone) {\r\n            bgColor = 'bg-green-50'; // Completed\r\n            textColor = 'text-green-600';\r\n        } else if (!acceptDoExam) {\r\n            bgColor = 'bg-orange-50'; // Cannot be taken\r\n            textColor = 'text-orange-500';\r\n        }\r\n\r\n        return (\r\n            <div className={`p-2 rounded-full ${bgColor} ${textColor}`}>\r\n                <StatusIcon />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    // Action button\r\n    const ActionButton = () => {\r\n        // Determine button style and text based on exam status\r\n        let buttonStyle = '';\r\n        let buttonText = '';\r\n\r\n        if (isDone) {\r\n            // Completed exam\r\n            buttonStyle = 'bg-green-600 hover:bg-green-700';\r\n            buttonText = 'Xem lại bài làm';\r\n        } else if (!acceptDoExam) {\r\n            // Cannot take exam\r\n            buttonStyle = 'bg-orange-500 hover:bg-orange-600';\r\n            buttonText = 'Không thể làm bài';\r\n        } else {\r\n            // Can take exam\r\n            buttonStyle = 'bg-cyan-600 hover:bg-cyan-700';\r\n            buttonText = 'Bắt đầu làm bài';\r\n        }\r\n\r\n        return (\r\n            <button\r\n                className={`${buttonStyle} text-white py-1.5 sm:py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200 flex items-center justify-center px-4`}\r\n                onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    handleClicked();\r\n                }}\r\n                disabled={!acceptDoExam && !isDone}\r\n            >\r\n                <span>{buttonText}</span>\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"ml-2\">\r\n                    <polyline points=\"9 18 15 12 9 6\"></polyline>\r\n                </svg>\r\n            </button>\r\n        );\r\n    };\r\n\r\n    // Render horizontal layout\r\n    if (horizontal) {\r\n        return (\r\n            <div\r\n                className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer\"\r\n                onClick={handleClicked}\r\n            >\r\n                <div className=\"p-4 flex flex-col md:flex-row gap-4\">\r\n                    {/* Left section: Title and type */}\r\n                    <div className=\"flex-1\">\r\n                        <div className=\"flex items-start justify-between\">\r\n                            <p\r\n                                title={name}\r\n                                className=\"text-base font-semibold font-bevietnam text-black\"\r\n                            >\r\n                                {name}\r\n                                <span className=\"text-sm font-medium text-gray-600 ml-2\">\r\n                                    {codes && codes['exam type']?.find(c => c.code === typeOfExam)?.description || typeOfExam || ''}\r\n                                </span>\r\n                            </p>\r\n                            <div className=\"flex items-center gap-2 md:hidden\">\r\n                                <BookmarkButton />\r\n                                <StatusIndicator />\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Exam details */}\r\n                        <div className=\"mt-3 grid grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-4\">\r\n                            {examDetails.map((detail, index) => (\r\n                                <div key={index} className=\"flex items-center text-sm text-gray-600\">\r\n                                    {detail.icon}\r\n                                    <span>{detail.label} <span className=\"font-medium text-gray-800\">{detail.value}</span></span>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Right section: Status and action button */}\r\n                    <div className=\"flex flex-row md:flex-col items-center gap-4\">\r\n                        <div className=\"hidden md:flex items-center gap-2\">\r\n                            <BookmarkButton />\r\n                            <StatusIndicator />\r\n                        </div>\r\n\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Render vertical layout (original)\r\n    return (\r\n        <div\r\n            className=\"bg-white rounded shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer flex flex-col h-full\"\r\n            onClick={handleClicked}\r\n        >\r\n            <div className=\"p-3 sm:p-4 flex-1 flex flex-col\">\r\n                {/* Header with icon */}\r\n                <div className=\"flex-1 space-y-2\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex flex-col\">\r\n                            <p\r\n                                title={name}\r\n                                className=\"text-sm font-semibold font-bevietnam text-black flex-1\"\r\n                            >\r\n                                {name?.length > 30 ? name?.slice(0, 30) + \"...\" : name}\r\n                            </p>\r\n                            <p className=\"text-xs font-medium text-gray-800\">\r\n                                {codes && codes['exam type']?.find(c => c.code === typeOfExam)?.description || typeOfExam || ''}\r\n                            </p>\r\n                        </div>\r\n                        <div className=\"items-center sm:flex hidden gap-2\">\r\n                            <BookmarkButton />\r\n                            <StatusIndicator />\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"items-center sm:hidden flex gap-2\">\r\n                        <BookmarkButton />\r\n                        <StatusIndicator />\r\n                    </div>\r\n\r\n                    {/* Divider */}\r\n                    <div className=\"h-px w-full bg-gray-100\"></div>\r\n\r\n                    {/* Exam details */}\r\n                    <div className=\"flex flex-wrap items-center text-xs sm:text-sm text-gray-600 gap-x-2 gap-y-1\">\r\n                        {examDetails.map((detail, index) => (\r\n                            <React.Fragment key={index}>\r\n                                {index > 0 && <span className=\"text-gray-300\">|</span>}\r\n                                <div className=\"flex items-center shrink-0\">\r\n                                    {detail.icon}\r\n                                    <span>{detail.label} <span className=\"font-medium text-gray-800\">{detail.value}</span></span>\r\n                                </div>\r\n                            </React.Fragment>\r\n                        ))}\r\n                    </div>\r\n\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ExamCard;\r\n"], "mappings": ";;AAAA,OAAOA,gBAAgB,MAAM,0CAA0C;AACvE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SACIC,KAAK,EACLC,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,GAAG,EACHC,YAAY,EACZC,GAAG,QACA,cAAc;AACrB,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,UAAU,GAAIC,UAAU,IAAK;EAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACpCC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACV,CAAC,CAAC;AACN,CAAC;AAED,MAAMC,QAAQ,GAAGC,IAAA,IAAyC;EAAAC,EAAA;EAAA,IAAAC,cAAA,EAAAC,mBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAAA,IAAxC;IAAEC,IAAI;IAAEC,KAAK;IAAEC,UAAU,GAAG;EAAM,CAAC,GAAAR,IAAA;EACjD,MAAM;IAAES,IAAI;IAAEC,UAAU;IAAEC,KAAK,EAAEC,SAAS;IAAEC,OAAO;IAAEC,YAAY;IAAEC,SAAS;IAAEC,QAAQ;IAAEC,EAAE;IAAEC,MAAM;IAAEC,MAAM;IAAEC,YAAY,GAAG;EAAK,CAAC,GAAGd,IAAI;EACxI,MAAMe,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAC9B,MAAMiD,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAE9B,MAAMiD,aAAa,GAAGA,CAAA,KAAMF,QAAQ,mBAAAG,MAAA,CAAmBP,EAAE,CAAE,CAAC;EAC5D,MAAMQ,cAAc,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBL,QAAQ,CAAC/C,eAAe,CAAC;MAAEqD,MAAM,EAAEX;IAAG,CAAC,CAAC,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACrB;IACA,IAAIV,MAAM,EAAE;MACR,oBAAO7B,OAAA,CAACR,WAAW;QAACgD,IAAI,EAAE,EAAG;QAACC,SAAS,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC/D;;IAEA;IACA,IAAI,CAACf,YAAY,EAAE;MACf,oBAAO9B,OAAA,CAACP,IAAI;QAAC+C,IAAI,EAAE,EAAG;QAACC,SAAS,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzD;;IAEA;IACA,oBAAO7C,OAAA,CAACb,KAAK;MAACqD,IAAI,EAAE,EAAG;MAACC,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,CAChB;IACIC,IAAI,eAAE/C,OAAA,CAACV,aAAa;MAACkD,IAAI,EAAE,EAAG;MAACC,SAAS,EAAC;IAAmC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/EG,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE3B;EACX,CAAC,EACD;IACIyB,IAAI,eAAE/C,OAAA,CAACX,QAAQ;MAACmD,IAAI,EAAE,EAAG;MAACC,SAAS,EAAC;IAAgD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvFG,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE1B,OAAO,GAAG,EAAAX,cAAA,GAAAK,KAAK,CAAC,SAAS,CAAC,cAAAL,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK7B,OAAO,CAAC,cAAAV,mBAAA,uBAA/CA,mBAAA,CAAiDwC,WAAW,KAAI9B,OAAO,GAAG;EAC/F,CAAC,EACD;IACIwB,IAAI,eAAE/C,OAAA,CAACb,KAAK;MAACqD,IAAI,EAAE,EAAG;MAACC,SAAS,EAAC;IAAmC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvEG,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAEzB,YAAY,GAAGA,YAAY,GAAG,OAAO,GAAG;EACnD,CAAC,EACD;IACIuB,IAAI,eAAE/C,OAAA,CAACZ,QAAQ;MAACoD,IAAI,EAAE,EAAG;MAACC,SAAS,EAAC;IAAmC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1EG,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAEhD,UAAU,CAACwB,SAAS;EAC/B,CAAC,CACJ;;EAID;EACA,MAAM6B,cAAc,GAAGA,CAAA,kBACnBtD,OAAA,CAACF,MAAM,CAACyD,MAAM;IACVC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE;IAC3BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAI,CAAE;IACzBE,OAAO,EAAExB,cAAe;IACxBM,SAAS,kDAAAP,MAAA,CACLN,MAAM,GACA,yCAAyC,GACzC,kDAAkD,CACzD;IACHgC,KAAK,EAAEhC,MAAM,GAAG,eAAe,GAAG,YAAa;IAAAiC,QAAA,eAE/C7D,OAAA,CAACT,QAAQ;MAACiD,IAAI,EAAE,EAAG;MAACC,SAAS,EAAEb,MAAM,GAAG,cAAc,GAAG;IAAG;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAClB;;EAED;EACA,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAIC,OAAO,GAAG,WAAW,CAAC,CAAC;IAC3B,IAAIC,SAAS,GAAG,cAAc;IAE9B,IAAInC,MAAM,EAAE;MACRkC,OAAO,GAAG,aAAa,CAAC,CAAC;MACzBC,SAAS,GAAG,gBAAgB;IAChC,CAAC,MAAM,IAAI,CAAClC,YAAY,EAAE;MACtBiC,OAAO,GAAG,cAAc,CAAC,CAAC;MAC1BC,SAAS,GAAG,iBAAiB;IACjC;IAEA,oBACIhE,OAAA;MAAKyC,SAAS,sBAAAP,MAAA,CAAsB6B,OAAO,OAAA7B,MAAA,CAAI8B,SAAS,CAAG;MAAAH,QAAA,eACvD7D,OAAA,CAACuC,UAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAEd,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACvB;IACA,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,UAAU,GAAG,EAAE;IAEnB,IAAItC,MAAM,EAAE;MACR;MACAqC,WAAW,GAAG,iCAAiC;MAC/CC,UAAU,GAAG,iBAAiB;IAClC,CAAC,MAAM,IAAI,CAACrC,YAAY,EAAE;MACtB;MACAoC,WAAW,GAAG,mCAAmC;MACjDC,UAAU,GAAG,mBAAmB;IACpC,CAAC,MAAM;MACH;MACAD,WAAW,GAAG,+BAA+B;MAC7CC,UAAU,GAAG,iBAAiB;IAClC;IAEA,oBACInE,OAAA;MACIyC,SAAS,KAAAP,MAAA,CAAKgC,WAAW,gJAA8I;MACvKP,OAAO,EAAGvB,CAAC,IAAK;QACZA,CAAC,CAACC,eAAe,CAAC,CAAC;QACnBJ,aAAa,CAAC,CAAC;MACnB,CAAE;MACFmC,QAAQ,EAAE,CAACtC,YAAY,IAAI,CAACD,MAAO;MAAAgC,QAAA,gBAEnC7D,OAAA;QAAA6D,QAAA,EAAOM;MAAU;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACzB7C,OAAA;QAAKqE,KAAK,EAAC,4BAA4B;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,WAAW,EAAC,GAAG;QAACC,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACpC,SAAS,EAAC,MAAM;QAAAoB,QAAA,eAC9L7D,OAAA;UAAU8E,MAAM,EAAC;QAAgB;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEjB,CAAC;;EAED;EACA,IAAI3B,UAAU,EAAE;IAAA,IAAA6D,eAAA,EAAAC,oBAAA;IACZ,oBACIhF,OAAA;MACIyC,SAAS,EAAC,gHAAgH;MAC1HkB,OAAO,EAAE1B,aAAc;MAAA4B,QAAA,eAEvB7D,OAAA;QAAKyC,SAAS,EAAC,qCAAqC;QAAAoB,QAAA,gBAEhD7D,OAAA;UAAKyC,SAAS,EAAC,QAAQ;UAAAoB,QAAA,gBACnB7D,OAAA;YAAKyC,SAAS,EAAC,kCAAkC;YAAAoB,QAAA,gBAC7C7D,OAAA;cACI4D,KAAK,EAAEzC,IAAK;cACZsB,SAAS,EAAC,mDAAmD;cAAAoB,QAAA,GAE5D1C,IAAI,eACLnB,OAAA;gBAAMyC,SAAS,EAAC,wCAAwC;gBAAAoB,QAAA,EACnD5C,KAAK,MAAA8D,eAAA,GAAI9D,KAAK,CAAC,WAAW,CAAC,cAAA8D,eAAA,wBAAAC,oBAAA,GAAlBD,eAAA,CAAoB7B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKhC,UAAU,CAAC,cAAA4D,oBAAA,uBAApDA,oBAAA,CAAsD3B,WAAW,KAAIjC,UAAU,IAAI;cAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACJ7C,OAAA;cAAKyC,SAAS,EAAC,mCAAmC;cAAAoB,QAAA,gBAC9C7D,OAAA,CAACsD,cAAc;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClB7C,OAAA,CAAC8D,eAAe;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN7C,OAAA;YAAKyC,SAAS,EAAC,sDAAsD;YAAAoB,QAAA,EAChEf,WAAW,CAACmC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC3BnF,OAAA;cAAiByC,SAAS,EAAC,yCAAyC;cAAAoB,QAAA,GAC/DqB,MAAM,CAACnC,IAAI,eACZ/C,OAAA;gBAAA6D,QAAA,GAAOqB,MAAM,CAAClC,KAAK,EAAC,GAAC,eAAAhD,OAAA;kBAAMyC,SAAS,EAAC,2BAA2B;kBAAAoB,QAAA,EAAEqB,MAAM,CAACjC;gBAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAFvFsC,KAAK;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN7C,OAAA;UAAKyC,SAAS,EAAC,8CAA8C;UAAAoB,QAAA,eACzD7D,OAAA;YAAKyC,SAAS,EAAC,mCAAmC;YAAAoB,QAAA,gBAC9C7D,OAAA,CAACsD,cAAc;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClB7C,OAAA,CAAC8D,eAAe;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;;EAEA;EACA,oBACI7C,OAAA;IACIyC,SAAS,EAAC,kIAAkI;IAC5IkB,OAAO,EAAE1B,aAAc;IAAA4B,QAAA,eAEvB7D,OAAA;MAAKyC,SAAS,EAAC,iCAAiC;MAAAoB,QAAA,eAE5C7D,OAAA;QAAKyC,SAAS,EAAC,kBAAkB;QAAAoB,QAAA,gBAC7B7D,OAAA;UAAKyC,SAAS,EAAC,mCAAmC;UAAAoB,QAAA,gBAC9C7D,OAAA;YAAKyC,SAAS,EAAC,eAAe;YAAAoB,QAAA,gBAC1B7D,OAAA;cACI4D,KAAK,EAAEzC,IAAK;cACZsB,SAAS,EAAC,wDAAwD;cAAAoB,QAAA,EAEjE,CAAA1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,MAAM,IAAG,EAAE,GAAG,CAAAjE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK,GAAGlE;YAAI;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACJ7C,OAAA;cAAGyC,SAAS,EAAC,mCAAmC;cAAAoB,QAAA,EAC3C5C,KAAK,MAAAH,gBAAA,GAAIG,KAAK,CAAC,WAAW,CAAC,cAAAH,gBAAA,wBAAAC,qBAAA,GAAlBD,gBAAA,CAAoBoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKhC,UAAU,CAAC,cAAAL,qBAAA,uBAApDA,qBAAA,CAAsDsC,WAAW,KAAIjC,UAAU,IAAI;YAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7C,OAAA;YAAKyC,SAAS,EAAC,mCAAmC;YAAAoB,QAAA,gBAC9C7D,OAAA,CAACsD,cAAc;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClB7C,OAAA,CAAC8D,eAAe;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7C,OAAA;UAAKyC,SAAS,EAAC,mCAAmC;UAAAoB,QAAA,gBAC9C7D,OAAA,CAACsD,cAAc;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClB7C,OAAA,CAAC8D,eAAe;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAGN7C,OAAA;UAAKyC,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG/C7C,OAAA;UAAKyC,SAAS,EAAC,8EAA8E;UAAAoB,QAAA,EACxFf,WAAW,CAACmC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC3BnF,OAAA,CAACd,KAAK,CAACoG,QAAQ;YAAAzB,QAAA,GACVsB,KAAK,GAAG,CAAC,iBAAInF,OAAA;cAAMyC,SAAS,EAAC,eAAe;cAAAoB,QAAA,EAAC;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD7C,OAAA;cAAKyC,SAAS,EAAC,4BAA4B;cAAAoB,QAAA,GACtCqB,MAAM,CAACnC,IAAI,eACZ/C,OAAA;gBAAA6D,QAAA,GAAOqB,MAAM,CAAClC,KAAK,EAAC,GAAC,eAAAhD,OAAA;kBAAMyC,SAAS,EAAC,2BAA2B;kBAAAoB,QAAA,EAAEqB,MAAM,CAACjC;gBAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA,GALWsC,KAAK;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAClC,EAAA,CAtOIF,QAAQ;EAAA,QAEO1B,WAAW,EACXC,WAAW;AAAA;AAAAuG,EAAA,GAH1B9E,QAAQ;AAwOd,eAAeA,QAAQ;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}