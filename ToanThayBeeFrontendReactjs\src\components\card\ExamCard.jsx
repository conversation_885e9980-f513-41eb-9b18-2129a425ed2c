import ExamDefaultImage from "../../assets/images/defaultExamImage.png";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { saveExamForUser } from "../../features/exam/examSlice";
import React from "react";

const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
    });
};

const ExamCard = ({ exam, codes, horizontal = false }) => {
    const { name, typeOfExam, class: examClass, chapter, testDuration, createdAt, imageUrl, id, isSave, isDone, acceptDoExam = true } = exam;
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const handleClicked = () => navigate(`/practice/exam/${id}`);
    const handleSaveExam = (e) => {
        e.stopPropagation();
        dispatch(saveExamForUser({ examId: id }));
    };

    // Bookmark icon component
    const BookmarkIcon = ({ isSave }) => (
        isSave ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-sky-600 fill-sky-600">
                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
        ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
        )
    );

    // Status icon component
    const StatusIcon = () => {
        // If the exam is already done, show the completed icon regardless of acceptDoExam
        if (isDone) {
            return (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-600">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
            );
        }

        // If the exam cannot be taken, show a lock icon
        if (!acceptDoExam) {
            return (
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-orange-500">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg>
            );
        }

        // Default: exam can be taken but hasn't been completed yet
        return (
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-cyan-700">
                <path d="M12 8v4l3 3"></path>
                <circle cx="12" cy="12" r="10"></circle>
            </svg>
        );
    };

    // Exam details items
    const examDetails = [
        {
            icon: (
                <svg className="md:mr-2 mr-[0.1rem] text-gray-400" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" />
                </svg>
            ),
            label: "Lớp:",
            value: examClass
        },
        {
            icon: (
                <svg className="md:mr-2 mr-[0.1rem] text-gray-400 min-w-[16px]" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
                </svg>
            ),
            label: "Chương:",
            value: chapter ? codes['chapter']?.find(c => c.code === chapter)?.description || chapter : 'Không có'
        },
        {
            icon: (
                <svg className="md:mr-2 mr-[0.1rem] text-gray-400" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <polyline points="12 6 12 12 16 14" />
                </svg>
            ),
            label: "Thời gian:",
            value: testDuration ? testDuration + ' phút' : 'Không có'
        },
        {
            icon: (
                <svg className="md:mr-2 mr-[0.1rem] text-gray-400" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
            ),
            label: "Ngày đăng:",
            value: formatDate(createdAt)
        }
    ];



    // Bookmark button
    const BookmarkButton = () => (
        <button
            onClick={handleSaveExam}
            className="text-sm text-sky-600 hover:text-sky-700 hover:bg-slate-100 p-1 rounded flex items-center gap-1"
            title={isSave ? "Đã lưu đề thi" : "Lưu đề thi"}
        >
            <BookmarkIcon isSave={isSave} />
        </button>
    );

    // Status indicator
    const StatusIndicator = () => {
        // Determine the background color based on the exam status
        let bgColor = 'bg-cyan-50'; // Default: can be taken

        if (isDone) {
            bgColor = 'bg-green-50'; // Completed
        } else if (!acceptDoExam) {
            bgColor = 'bg-orange-50'; // Cannot be taken
        }

        return (
            <div className={`p-2 rounded-full ${bgColor}`}>
                <StatusIcon />
            </div>
        );
    };

    // Action button
    const ActionButton = () => {
        // Determine button style and text based on exam status
        let buttonStyle = '';
        let buttonText = '';

        if (isDone) {
            // Completed exam
            buttonStyle = 'bg-green-600 hover:bg-green-700';
            buttonText = 'Xem lại bài làm';
        } else if (!acceptDoExam) {
            // Cannot take exam
            buttonStyle = 'bg-orange-500 hover:bg-orange-600';
            buttonText = 'Không thể làm bài';
        } else {
            // Can take exam
            buttonStyle = 'bg-cyan-600 hover:bg-cyan-700';
            buttonText = 'Bắt đầu làm bài';
        }

        return (
            <button
                className={`${buttonStyle} text-white py-1.5 sm:py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200 flex items-center justify-center px-4`}
                onClick={(e) => {
                    e.stopPropagation();
                    handleClicked();
                }}
                disabled={!acceptDoExam && !isDone}
            >
                <span>{buttonText}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2">
                    <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
            </button>
        );
    };

    // Render horizontal layout
    if (horizontal) {
        return (
            <div
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer"
                onClick={handleClicked}
            >
                <div className="p-4 flex flex-col md:flex-row gap-4">
                    {/* Left section: Title and type */}
                    <div className="flex-1">
                        <div className="flex items-start justify-between">
                            <p
                                title={name}
                                className="text-base font-semibold font-bevietnam text-black"
                            >
                                {name}
                                <span className="text-sm font-medium text-gray-600 ml-2">
                                    {codes && codes['exam type']?.find(c => c.code === typeOfExam)?.description || typeOfExam || ''}
                                </span>
                            </p>
                            <div className="flex items-center gap-2 md:hidden">
                                <BookmarkButton />
                                <StatusIndicator />
                            </div>
                        </div>

                        {/* Exam details */}
                        <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-y-2 gap-x-4">
                            {examDetails.map((detail, index) => (
                                <div key={index} className="flex items-center text-sm text-gray-600">
                                    {detail.icon}
                                    <span>{detail.label} <span className="font-medium text-gray-800">{detail.value}</span></span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Right section: Status and action button */}
                    <div className="flex flex-row md:flex-col items-center gap-4">
                        <div className="hidden md:flex items-center gap-2">
                            <BookmarkButton />
                            <StatusIndicator />
                        </div>

                    </div>
                </div>
            </div>
        );
    }

    // Render vertical layout (original)
    return (
        <div
            className="bg-white rounded shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer flex flex-col h-full"
            onClick={handleClicked}
        >
            <div className="p-3 sm:p-4 flex-1 flex flex-col">
                {/* Header with icon */}
                <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                        <div className="flex flex-col">
                            <p
                                title={name}
                                className="text-sm font-semibold font-bevietnam text-black flex-1"
                            >
                                {name?.length > 30 ? name?.slice(0, 30) + "..." : name}
                            </p>
                            <p className="text-xs font-medium text-gray-800">
                                {codes && codes['exam type']?.find(c => c.code === typeOfExam)?.description || typeOfExam || ''}
                            </p>
                        </div>
                        <div className="items-center sm:flex hidden gap-2">
                            <BookmarkButton />
                            <StatusIndicator />
                        </div>
                    </div>
                    <div className="items-center sm:hidden flex gap-2">
                        <BookmarkButton />
                        <StatusIndicator />
                    </div>

                    {/* Divider */}
                    <div className="h-px w-full bg-gray-100"></div>

                    {/* Exam details */}
                    <div className="flex flex-wrap items-center text-xs sm:text-sm text-gray-600 gap-x-2 gap-y-1">
                        {examDetails.map((detail, index) => (
                            <React.Fragment key={index}>
                                {index > 0 && <span className="text-gray-300">|</span>}
                                <div className="flex items-center shrink-0">
                                    {detail.icon}
                                    <span>{detail.label} <span className="font-medium text-gray-800">{detail.value}</span></span>
                                </div>
                            </React.Fragment>
                        ))}
                    </div>


                </div>
            </div>
        </div>
    );
};

export default ExamCard;
