{"ast": null, "code": "import api from \"./api\";\nexport const joinExamApi = async examId => {\n  return await api.get(\"/v1/user/join-exam/\".concat(examId));\n};\nexport const submitAnswerApi = async _ref => {\n  let {\n    questionId,\n    answerContent,\n    type\n  } = _ref;\n  return await api.post('/v1/user/submit-answer', {\n    questionId,\n    answerContent,\n    type\n  });\n};\nexport const calculateScoreApi = async _ref2 => {\n  let {\n    attemptId,\n    answers\n  } = _ref2;\n  return await api.post(\"/v1/user/calculate-score/\".concat(attemptId), {\n    answers\n  });\n};\nexport const summitExamAPI = async _ref3 => {\n  let {\n    attemptId\n  } = _ref3;\n  return await api.post(\"/v1/user/submit-exam\", {\n    attemptId\n  });\n};\n\n// API để lấy thời gian còn lại của bài thi\nexport const getRemainingTimeApi = async _ref4 => {\n  let {\n    examId,\n    attemptId\n  } = _ref4;\n  return await api.get(\"/v1/user/exam-time/\".concat(examId, \"/\").concat(attemptId));\n};\n\n// API để gửi log hoạt động của user (thay thế socket user_log)\nexport const logUserActivityApi = async _ref5 => {\n  let {\n    examId,\n    attemptId,\n    activityType,\n    details\n  } = _ref5;\n  return await api.post('/v1/user/log-activity', {\n    examId,\n    attemptId,\n    activityType,\n    details\n  });\n};\n\n// API để submit answer với attemptId (thay thế socket select_answer)\nexport const submitAnswerWithAttemptApi = async _ref6 => {\n  let {\n    questionId,\n    answerContent,\n    type,\n    attemptId\n  } = _ref6;\n  return await api.post('/v1/user/submit-answer-attempt', {\n    questionId,\n    answerContent,\n    type,\n    attemptId\n  });\n};\n\n// API để leave exam (thay thế socket leave_exam)\nexport const leaveExamApi = async _ref7 => {\n  let {\n    examId,\n    attemptId\n  } = _ref7;\n  return await api.post('/v1/user/leave-exam', {\n    examId,\n    attemptId\n  });\n};", "map": {"version": 3, "names": ["api", "joinExamApi", "examId", "get", "concat", "submitAnswerApi", "_ref", "questionId", "answerContent", "type", "post", "calculateScoreApi", "_ref2", "attemptId", "answers", "summitExamAPI", "_ref3", "getRemainingTimeApi", "_ref4", "logUserActivityApi", "_ref5", "activityType", "details", "submitAnswerWithAttemptApi", "_ref6", "leaveExamApi", "_ref7"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/services/doExamApi.js"], "sourcesContent": ["import api from \"./api\";\r\n\r\nexport const joinExamApi = async (examId) => {\r\n    return await api.get(`/v1/user/join-exam/${examId}`);\r\n}\r\n\r\nexport const submitAnswerApi = async ({ questionId, answerContent, type }) => {\r\n    return await api.post('/v1/user/submit-answer', { questionId, answerContent, type });\r\n};\r\n\r\nexport const calculateScoreApi = async ({ attemptId, answers }) => {\r\n    return await api.post(`/v1/user/calculate-score/${attemptId}`, { answers });\r\n};\r\n\r\nexport const summitExamAPI = async ({ attemptId }) => {\r\n    return await api.post(`/v1/user/submit-exam`, { attemptId });\r\n}\r\n\r\n// API để lấy thời gian còn lại của bài thi\r\nexport const getRemainingTimeApi = async ({ examId, attemptId }) => {\r\n    return await api.get(`/v1/user/exam-time/${examId}/${attemptId}`);\r\n};\r\n\r\n// API để gửi log hoạt động của user (thay thế socket user_log)\r\nexport const logUserActivityApi = async ({ examId, attemptId, activityType, details }) => {\r\n    return await api.post('/v1/user/log-activity', {\r\n        examId,\r\n        attemptId,\r\n        activityType,\r\n        details\r\n    });\r\n};\r\n\r\n// API để submit answer với attemptId (thay thế socket select_answer)\r\nexport const submitAnswerWithAttemptApi = async ({ questionId, answerContent, type, attemptId }) => {\r\n    return await api.post('/v1/user/submit-answer-attempt', {\r\n        questionId,\r\n        answerContent,\r\n        type,\r\n        attemptId\r\n    });\r\n};\r\n\r\n// API để leave exam (thay thế socket leave_exam)\r\nexport const leaveExamApi = async ({ examId, attemptId }) => {\r\n    return await api.post('/v1/user/leave-exam', { examId, attemptId });\r\n};"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,OAAO,MAAMC,WAAW,GAAG,MAAOC,MAAM,IAAK;EACzC,OAAO,MAAMF,GAAG,CAACG,GAAG,uBAAAC,MAAA,CAAuBF,MAAM,CAAE,CAAC;AACxD,CAAC;AAED,OAAO,MAAMG,eAAe,GAAG,MAAAC,IAAA,IAA+C;EAAA,IAAxC;IAAEC,UAAU;IAAEC,aAAa;IAAEC;EAAK,CAAC,GAAAH,IAAA;EACrE,OAAO,MAAMN,GAAG,CAACU,IAAI,CAAC,wBAAwB,EAAE;IAAEH,UAAU;IAAEC,aAAa;IAAEC;EAAK,CAAC,CAAC;AACxF,CAAC;AAED,OAAO,MAAME,iBAAiB,GAAG,MAAAC,KAAA,IAAkC;EAAA,IAA3B;IAAEC,SAAS;IAAEC;EAAQ,CAAC,GAAAF,KAAA;EAC1D,OAAO,MAAMZ,GAAG,CAACU,IAAI,6BAAAN,MAAA,CAA6BS,SAAS,GAAI;IAAEC;EAAQ,CAAC,CAAC;AAC/E,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAC,KAAA,IAAyB;EAAA,IAAlB;IAAEH;EAAU,CAAC,GAAAG,KAAA;EAC7C,OAAO,MAAMhB,GAAG,CAACU,IAAI,yBAAyB;IAAEG;EAAU,CAAC,CAAC;AAChE,CAAC;;AAED;AACA,OAAO,MAAMI,mBAAmB,GAAG,MAAAC,KAAA,IAAiC;EAAA,IAA1B;IAAEhB,MAAM;IAAEW;EAAU,CAAC,GAAAK,KAAA;EAC3D,OAAO,MAAMlB,GAAG,CAACG,GAAG,uBAAAC,MAAA,CAAuBF,MAAM,OAAAE,MAAA,CAAIS,SAAS,CAAE,CAAC;AACrE,CAAC;;AAED;AACA,OAAO,MAAMM,kBAAkB,GAAG,MAAAC,KAAA,IAAwD;EAAA,IAAjD;IAAElB,MAAM;IAAEW,SAAS;IAAEQ,YAAY;IAAEC;EAAQ,CAAC,GAAAF,KAAA;EACjF,OAAO,MAAMpB,GAAG,CAACU,IAAI,CAAC,uBAAuB,EAAE;IAC3CR,MAAM;IACNW,SAAS;IACTQ,YAAY;IACZC;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMC,0BAA0B,GAAG,MAAAC,KAAA,IAA0D;EAAA,IAAnD;IAAEjB,UAAU;IAAEC,aAAa;IAAEC,IAAI;IAAEI;EAAU,CAAC,GAAAW,KAAA;EAC3F,OAAO,MAAMxB,GAAG,CAACU,IAAI,CAAC,gCAAgC,EAAE;IACpDH,UAAU;IACVC,aAAa;IACbC,IAAI;IACJI;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMY,YAAY,GAAG,MAAAC,KAAA,IAAiC;EAAA,IAA1B;IAAExB,MAAM;IAAEW;EAAU,CAAC,GAAAa,KAAA;EACpD,OAAO,MAAM1B,GAAG,CAACU,IAAI,CAAC,qBAAqB,EAAE;IAAER,MAAM;IAAEW;EAAU,CAAC,CAAC;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}