import db from "../models/index.js"
import { Op, literal } from 'sequelize'
import UserType from "../constants/UserType.js"
import * as tuitionService from "../services/tuition.service.js"

/**
 * L<PERSON><PERSON> danh sách học phí của tất cả các lớp
 */
export const getAllClassTuition = async (req, res) => {
    try {
        const search = req.query.search || ''
        const page = parseInt(req.query.page, 10) || 1
        const limit = parseInt(req.query.limit, 10) || 10
        const offset = (page - 1) * limit
        const sortOrder = req.query.sortOrder || 'DESC'
        const sortBy = req.query.sortBy || 'createdAt'
        const classId = req.query.classId || null
        const month = req.query.month || null

        // Xây dựng điều kiện tìm kiếm
        let whereClause = {}

        // Tìm kiếm theo lớp nếu có
        if (classId) {
            whereClause.classId = classId
        }

        // Tìm kiếm theo tháng nếu có
        if (month) {
            whereClause.month = month
        }

        // Tìm kiếm theo từ khóa
        if (search.trim() !== '') {
            whereClause = {
                ...whereClause,
                [Op.or]: [
                    literal(`Class.name LIKE '%${search}%'`),
                    { note: { [Op.like]: `%${search}%` } }
                ]
            }
        }

        // Thực hiện truy vấn với include để lấy thông tin lớp học
        const [tuitions, total] = await Promise.all([
            db.ClassTuition.findAll({
                where: whereClause,
                include: [
                    {
                        model: db.Class,
                        as: 'class',
                        attributes: ['id', 'name', 'class_code']
                    }
                ],
                offset,
                limit,
                order: [[sortBy, sortOrder]]
            }),
            db.ClassTuition.count({
                where: whereClause,
                include: [
                    {
                        model: db.Class,
                        as: 'class'
                    }
                ]
            })
        ])

        // Format lại dữ liệu trước khi trả về
        const formattedTuitions = tuitions.map(tuition => {
            const plainTuition = tuition.get({ plain: true })
            const [year, month] = plainTuition.month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]
            return {
                ...plainTuition,
                monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`
            }
        })

        return res.status(200).json({
            message: 'Danh sách học phí của các lớp',
            data: formattedTuitions,
            pagination: {
                page,
                limit,
                totalRows: total,
                totalPages: Math.ceil(total / limit)
            }
        })
    } catch (error) {
        console.error('Lỗi khi lấy danh sách học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Lấy danh sách học phí của một lớp cụ thể
 */
export const getClassTuitionByClassId = async (req, res) => {
    try {
        const { classId } = req.params
        const page = parseInt(req.query.page, 10) || 1
        const limit = parseInt(req.query.limit, 10) || 10
        const offset = (page - 1) * limit
        const sortOrder = req.query.sortOrder || 'DESC'
        const sortBy = req.query.sortBy || 'month'

        // Kiểm tra lớp học có tồn tại không
        const classExists = await db.Class.findByPk(classId)
        if (!classExists) {
            return res.status(404).json({ message: 'Lớp học không tồn tại' })
        }

        // Lấy danh sách học phí của lớp
        const [tuitions, total] = await Promise.all([
            db.ClassTuition.findAll({
                where: { classId },
                offset,
                limit,
                order: [[sortBy, sortOrder]]
            }),
            db.ClassTuition.count({ where: { classId } })
        ])

        // Format lại dữ liệu trước khi trả về
        const formattedTuitions = tuitions.map(tuition => {
            const plainTuition = tuition.get({ plain: true })
            const [year, month] = plainTuition.month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]
            return {
                ...plainTuition,
                monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`
            }
        })

        return res.status(200).json({
            message: `Danh sách học phí của lớp ${classExists.name}`,
            data: formattedTuitions,
            class: {
                id: classExists.id,
                name: classExists.name,
                class_code: classExists.class_code
            },
            pagination: {
                page,
                limit,
                totalRows: total,
                totalPages: Math.ceil(total / limit)
            }
        })
    } catch (error) {
        console.error('Lỗi khi lấy danh sách học phí của lớp:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Lấy thông tin chi tiết một học phí
 */
export const getClassTuitionById = async (req, res) => {
    try {
        const { id } = req.params
        const tuition = await db.ClassTuition.findByPk(id, {
            include: [
                {
                    model: db.Class,
                    as: 'class',
                    attributes: ['id', 'name', 'class_code']
                }
            ]
        })

        if (!tuition) {
            return res.status(404).json({ message: 'Học phí không tồn tại' })
        }

        // Format lại dữ liệu trước khi trả về
        const plainTuition = tuition.get({ plain: true })
        const [year, month] = plainTuition.month.split('-')
        const monthNames = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ]
        const formattedTuition = {
            ...plainTuition,
            monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`
        }

        return res.status(200).json({
            message: 'Chi tiết học phí',
            data: formattedTuition
        })
    } catch (error) {
        console.error('Lỗi khi lấy chi tiết học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Tạo mới học phí cho lớp
 */
export const createClassTuition = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { classId, month, amount, note } = req.body

        // Kiểm tra lớp học có tồn tại không
        const classExists = await db.Class.findByPk(classId, { transaction })
        if (!classExists) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Lớp học không tồn tại' })
        }

        // Kiểm tra xem đã có học phí cho lớp này trong tháng này chưa
        const existingTuition = await db.ClassTuition.findOne({
            where: {
                classId,
                month
            },
            transaction
        })

        if (existingTuition) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Đã tồn tại học phí cho lớp này trong tháng này' })
        }

        // Tạo mới học phí
        const newTuition = await db.ClassTuition.create({
            classId,
            month,
            amount,
            note
        }, { transaction })

        // Tìm các học sinh đã tham gia lớp này
        const students = await db.StudentClassStatus.findAll({
            where: {
                classId: classId,
                status: 'JS' // Joined Successfully
            },
            attributes: ['studentId'],
            raw: true,
            transaction
        })

        let updatedPayments = 0

        if (students && students.length > 0) {
            
            const payments = await db.TuitionPayment.findAll({
                where: {
                    userId: {
                        [Op.in]: students.map(student => student.studentId)
                    },
                    month: month,
                    isCustom: false
                },
                transaction
            })

            if (payments && payments.length > 0) {
                console.log(`Cập nhật expectedAmount cho ${payments.length} khoản thanh toán sau khi tạo học phí mới`)

                // Tạo cache để tối ưu hiệu suất
                const cache = {
                    classTuitions: {}
                }

                // Cập nhật từng khoản thanh toán
                for (const payment of payments) {
                    try {
                        await tuitionService.updateExpectedAmountForTuitionPayment(
                            payment.id,
                            { transaction },
                            cache
                        )
                        updatedPayments++
                    } catch (updateError) {
                        console.error(`Lỗi khi cập nhật expectedAmount cho khoản thanh toán ID ${payment.id}:`, updateError)
                        // Tiếp tục xử lý các khoản thanh toán khác ngay cả khi có lỗi
                    }
                }
            }
        }

        await transaction.commit()

        return res.status(201).json({
            message: 'Tạo học phí thành công',
            data: newTuition,
            updatedPayments: updatedPayments
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi tạo học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Cập nhật thông tin học phí
 */
export const updateClassTuition = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { id } = req.params
        const { amount, note } = req.body

        // Kiểm tra học phí có tồn tại không
        const tuition = await db.ClassTuition.findByPk(id, { transaction })
        if (!tuition) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Học phí không tồn tại' })
        }

        // Lưu lại giá trị cũ trước khi cập nhật
        const oldAmount = tuition.amount


        await tuition.update({
            amount,
            note
        }, { transaction })

        // Nếu số tiền học phí thay đổi, cập nhật expectedAmount cho các khoản thanh toán
        if (parseFloat(oldAmount) !== parseFloat(amount)) {
            // Tìm các học sinh đã tham gia lớp này
            const students = await db.StudentClassStatus.findAll({
                where: {
                    classId: tuition.classId,
                    status: 'JS' // Joined Successfully
                },
                attributes: ['studentId'],
                raw: true,
                transaction
            })

            if (students && students.length > 0) {
                
                const payments = await db.TuitionPayment.findAll({
                    where: {
                        userId: {
                            [Op.in]: students.map(student => student.studentId)
                        },
                        month: tuition.month,
                        isCustom: false
                    },
                    transaction
                })

                if (payments && payments.length > 0) {
                    console.log(`Cập nhật expectedAmount cho ${payments.length} khoản thanh toán sau khi cập nhật học phí`)

                    // Tạo cache để tối ưu hiệu suất
                    const cache = {
                        classTuitions: {}
                    }

                    // Cập nhật từng khoản thanh toán
                    for (const payment of payments) {
                        try {
                            await tuitionService.updateExpectedAmountForTuitionPayment(
                                payment.id,
                                { transaction },
                                cache
                            )
                        } catch (updateError) {
                            console.error(`Lỗi khi cập nhật expectedAmount cho khoản thanh toán ID ${payment.id}:`, updateError)
                            // Tiếp tục xử lý các khoản thanh toán khác ngay cả khi có lỗi
                        }
                    }
                }
            }
        }

        await transaction.commit()

        return res.status(200).json({
            message: 'Cập nhật học phí thành công',
            data: tuition
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi cập nhật học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Xóa học phí
 */
export const deleteClassTuition = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { id } = req.params

        // Kiểm tra học phí có tồn tại không
        const tuition = await db.ClassTuition.findByPk(id, { transaction })
        if (!tuition) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Học phí không tồn tại' })
        }

        // Kiểm tra xem có học sinh nào đã đóng học phí này chưa
        const students = await db.StudentClassStatus.findAll({
            where: {
                classId: tuition.classId,
                status: 'JS' // Joined Successfully
            },
            attributes: ['studentId'],
            raw: true
        })

        await tuition.destroy({ transaction })

        const studentIds = students.map(s => s.studentId)
        let payments = []

        if (studentIds.length > 0) {
            payments = await db.TuitionPayment.findAll({
                where: {
                    userId: {
                        [Op.in]: studentIds
                    },
                    month: tuition.month,
                    isCustom: false
                },
                transaction
            })

            if (payments && payments.length > 0) {
                console.log(`Cập nhật expectedAmount cho ${payments.length} khoản thanh toán sau khi xóa học phí`)

                // Tạo cache để tối ưu hiệu suất
                const cache = {
                    classTuitions: {}
                }

                // Cập nhật từng khoản thanh toán
                for (const payment of payments) {
                    try {
                        await tuitionService.updateExpectedAmountForTuitionPayment(
                            payment.id,
                            { transaction },
                            cache
                        )
                    } catch (updateError) {
                        console.error(`Lỗi khi cập nhật expectedAmount cho khoản thanh toán ID ${payment.id}:`, updateError)
                        // Tiếp tục xử lý các khoản thanh toán khác ngay cả khi có lỗi
                    }
                }
            }
        }

        await transaction.commit()

        return res.status(200).json({
            message: 'Xóa học phí thành công',
            data: id,
            updatedPayments: payments ? payments.length : 0
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi xóa học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Tạo hàng loạt học phí cho các lớp có status là LHD và thuộc tính class (lớp 10, 11, 12)
 */
export const createBatchClassTuition = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { month, amount, note, classAttribute } = req.body

        if (!month) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Tháng không được để trống' })
        }

        // Kiểm tra định dạng tháng (YYYY-MM)
        const monthRegex = /^\d{4}-\d{2}$/
        if (!monthRegex.test(month)) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Định dạng tháng không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM' })
        }

        // Xây dựng điều kiện tìm kiếm lớp
        let whereClause = {
            status: 'LHD', // Lớp Hoạt Động
            public: true
        }

        // Nếu có thuộc tính class (lớp 10, 11, 12), thêm vào điều kiện tìm kiếm
        if (classAttribute) {
            whereClause.grade = classAttribute
        }
        console.log(whereClause)
        // Lấy danh sách các lớp có status là LHD và thuộc tính class (nếu có)
        const classes = await db.Class.findAll({
            where: whereClause,
            attributes: ['id', 'name', 'grade'],
            transaction
        })
        console.log(classes)

        if (classes.length === 0) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Không tìm thấy lớp nào phù hợp với điều kiện' })
        }

        // Tạo học phí cho từng lớp
        const createdTuitions = []
        const skippedTuitions = []

        for (const classItem of classes) {
            // Kiểm tra xem đã có học phí cho lớp này trong tháng này chưa
            const existingTuition = await db.ClassTuition.findOne({
                where: {
                    classId: classItem.id,
                    month
                },
                transaction
            })

            // Nếu chưa có, tạo mới
            if (!existingTuition) {
                const newTuition = await db.ClassTuition.create({
                    classId: classItem.id,
                    month,
                    amount: amount || 0,
                    note: note || `Học phí tháng ${month} cho lớp ${classItem.name}`
                }, { transaction })

                const students = await db.StudentClassStatus.findAll({
                    where: {
                        classId: classItem.id,
                        status: 'JS' // Joined Successfully
                    },
                    attributes: ['studentId'],
                    raw: true,
                    transaction
                })

                if (students && students.length > 0) {
                    const payments = await db.TuitionPayment.findAll({
                        where: {
                            userId: {
                                [Op.in]: students.map(student => student.studentId)
                            },
                            month: month,
                            isCustom: false
                        },
                        transaction
                    })

                    if (payments && payments.length > 0) {
                        console.log(`Cập nhật expectedAmount cho ${payments.length} khoản thanh toán sau khi tạo học phí mới cho lớp ${classItem.name}`)

                        // Tạo cache để tối ưu hiệu suất
                        const cache = {
                            classTuitions: {}
                        }

                        // Cập nhật từng khoản thanh toán
                        for (const payment of payments) {
                            try {
                                await tuitionService.updateExpectedAmountForTuitionPayment(
                                    payment.id,
                                    { transaction },
                                    cache
                                )
                            } catch (updateError) {
                                console.error(`Lỗi khi cập nhật expectedAmount cho khoản thanh toán ID ${payment.id}:`, updateError)
                                // Tiếp tục xử lý các khoản thanh toán khác ngay cả khi có lỗi
                            }
                        }
                    }
                }

                createdTuitions.push({
                    id: newTuition.id,
                    classId: classItem.id,
                    className: classItem.name,
                    classAttribute: classItem.grade
                })
            } else {
                skippedTuitions.push({
                    classId: classItem.id,
                    className: classItem.name,
                    classAttribute: classItem.grade
                })
            }
        }

        await transaction.commit()

        return res.status(201).json({
            message: 'Tạo hàng loạt học phí thành công',
            data: {
                created: createdTuitions,
                skipped: skippedTuitions,
                totalCreated: createdTuitions.length,
                totalSkipped: skippedTuitions.length
            }
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi tạo hàng loạt học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}
