import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import UserType from '../constants/UserType.js'
import * as DoExamController from '../controllers/DoExamController.js'

const router = express.Router()

router.get('/v1/user/join-exam/:examId',
    requireRoles([]),
    asyncHandler(DoExamController.joinExam)
)

router.post('/v1/user/submit-answer',
    requireRoles([]),
    asyncHandler(DoExamController.submitAnswerHandler)
)

router.post('/v1/user/calculate-score/:attemptId',
    requireRoles([]),
    asyncHandler(DoExamController.calculateScoreHandler)
)

// New API routes to replace socket functionality
router.get('/v1/user/exam-time/:examId/:attemptId',
    requireRoles([]),
    asyncHandler(DoExamController.getRemainingTimeHandler)
)

router.post('/v1/user/log-activity',
    requireRoles([]),
    asyncHandler(DoExamController.logUserActivityHandler)
)

router.post('/v1/user/submit-answer-attempt',
    requireRoles([]),
    asyncHandler(DoExamController.submitAnswerWithAttemptHandler)
)

router.post('/v1/user/leave-exam',
    requireRoles([]),
    asyncHandler(DoExamController.leaveExamHandler)
)

router.post('/v1/user/submit-exam',
    requireRoles([]),
    asyncHandler(DoExamController.submitExamHandler)
)

export default router