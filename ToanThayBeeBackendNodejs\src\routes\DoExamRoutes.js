import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import UserType from '../constants/UserType.js'
import * as DoExamController from '../controllers/DoExamController.js'

const router = express.Router()

router.get('/v1/user/join-exam/:examId',
    requireRoles([]),
    async<PERSON><PERSON><PERSON>(DoExamController.joinExam)
)

router.post('/v1/user/submit-answer',
    requireRoles([]),
    asyncHandler(DoExamController.submitAnswerHandler)
)

router.post('/v1/user/calculate-score/:attemptId',
    requireRoles([]),
    async<PERSON>and<PERSON>(DoExamController.calculateScoreHandler)
)

export default router