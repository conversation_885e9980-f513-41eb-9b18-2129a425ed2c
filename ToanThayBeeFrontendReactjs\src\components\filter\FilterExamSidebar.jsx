import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchCodesByType } from "../../features/code/codeSlice";
import { fetchPublicExams } from "../../features/exam/examSlice";
import { setSelectedGrade, setSelectedChapters, setSelectedExamTypes, setIsSearch } from "../../features/filter/filterSlice";
import { setCurrentPage } from "../../features/exam/examSlice";
import LoadingSpinner from "../loading/LoadingSpinner";
import { Search, X } from "lucide-react";

const FilterExamSidebar = () => {
    const { codes } = useSelector((state) => state.codes);
    const { isSearch, selectedGrade, selectedChapters, selectedExamTypes } = useSelector((state) => state.filter);
    const dispatch = useDispatch();

    const { pagination } = useSelector((state) => state.exams);
    const { page: currentPage, pageSize: limit, sortOrder } = pagination;

    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const [isClassroomExam, setIsClassroomExam] = useState(null);
    const [activeTab, setActiveTab] = useState('all'); // 'all', 'classroom', 'self'


    useEffect(() => {
        dispatch(fetchCodesByType(['chapter', 'grade', 'exam type']));
    }, [dispatch]);

    const fetchExams = (override = {}) => {
        // Only apply filters if isSearch is true or if explicitly overridden
        const shouldApplyFilters = isSearch || override.applyFilters;

        dispatch(fetchPublicExams({
            page: override.page ?? currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: shouldApplyFilters ? (override.typeOfExam ?? selectedExamTypes) : [],
            class: shouldApplyFilters ? (override.class === null ? override.class : selectedGrade) : null,
            chapter: shouldApplyFilters ? (override.chapter ?? selectedChapters) : [],
            search: shouldApplyFilters ? search : "",
            isClassroomExam: override.isClassroomExam
        }));
    }

    // Only fetch exams when page changes, not when filters change
    useEffect(() => {
        if (isSearch) {
            fetchExams({ isClassroomExam });
        }
    }, [dispatch, isSearch]);

    useEffect(() => {
        fetchExams({ isClassroomExam });
    }, [currentPage]);

    useEffect(() => {
        if (selectedChapters.length === 0 && selectedGrade === null && selectedExamTypes.length === 0 && search === "") {
            dispatch(setIsSearch(false));
        }
    }, [dispatch, selectedChapters, selectedGrade, selectedExamTypes, search]);

    const handleSearch = () => {
        setLoading(true);
        // Set isSearch to true first so filters will be applied
        dispatch(setIsSearch(true));

        dispatch(fetchPublicExams({
            page: currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: selectedExamTypes,
            class: selectedGrade,
            chapter: selectedChapters,
            search,
            isClassroomExam
        }))
            .then(() => {
                setLoading(false);
            });
    }

    const resetAllFilters = () => {
        setSearch("");
        dispatch(setSelectedGrade(null));
        dispatch(setSelectedChapters([]));
        dispatch(setSelectedExamTypes([]));

        // Set isSearch to true to ensure filters are applied (in this case, empty filters)
        dispatch(setIsSearch(true));

        // Apply the reset filters immediately
        setLoading(true);
        dispatch(fetchPublicExams({
            page: currentPage,
            limit: 10,
            sortOrder,
            typeOfExam: [],
            class: null,
            chapter: [],
            search: "",
            isClassroomExam
        }))
            .then(() => {
                setLoading(false);
            });
    }

    const toggleItem = (codeList, dispatchSetAction) => (code) => (isChecked) => {
        const newList = isChecked
            ? [...codeList, code]
            : codeList.filter((item) => item !== code);

        dispatch(dispatchSetAction(newList));
    };

    const handleSelectGrade = (gradeCode) => (isChecked) => {
        dispatch(setSelectedGrade(isChecked ? gradeCode : null));
        dispatch(setSelectedChapters([])); // reset selected chapters when grade changes
    };

    return (
        <div className="w-full bg-gray-50 rounded-lg shadow-sm p-4 sticky top-20">
            <div className="flex flex-col gap-4">
                <h2 className="text-xl font-bold text-zinc-800">Bộ lọc</h2>

                {/* Search bar */}
                <div className="relative w-full">
                    <input
                        type="text"
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        placeholder="Tìm kiếm đề thi..."
                        className="w-full h-10 pl-10 pr-10 text-sm text-gray-700 placeholder-gray-400 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sky-400 focus:border-sky-400 transition-all duration-150"
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                handleSearch();
                            }
                        }}
                    />
                    <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                        <Search size={18} className="text-gray-400" />
                    </div>
                    {loading && (
                        <div className="absolute inset-y-0 right-3 flex items-center">
                            <LoadingSpinner color="border-black" size="1.25rem" />
                        </div>
                    )}
                </div>

                {/* Tab filter */}
                <div className="flex flex-col gap-2 border-b border-gray-200 pb-4">
                    <h3 className="text-base font-medium text-gray-700">Loại đề</h3>
                    <div className="flex flex-col gap-2">
                        <div
                            className={`px-3 py-2 rounded-lg cursor-pointer ${activeTab === 'all' ? 'bg-gray-200 text-sky-700 font-medium' : 'hover:bg-gray-100 text-gray-700 bg-white'}`}
                            onClick={() => {
                                setIsClassroomExam(null);
                                setActiveTab('all');
                                dispatch(setCurrentPage(1));
                                fetchExams({
                                    page: 1,
                                    isClassroomExam: null,
                                    applyFilters: isSearch
                                });
                            }}
                        >
                            Tất cả
                        </div>
                        <div
                            className={`px-3 py-2 rounded-lg cursor-pointer ${activeTab === 'classroom' ? 'bg-gray-200 text-sky-700 font-medium' : 'hover:bg-gray-100 text-gray-700 bg-white'}`}
                            onClick={() => {
                                setIsClassroomExam(true);
                                setActiveTab('classroom');
                                dispatch(setCurrentPage(1));
                                fetchExams({
                                    page: 1,
                                    isClassroomExam: true,
                                    applyFilters: isSearch
                                });
                            }}
                        >
                            Đề trên lớp
                        </div>
                        <div
                            className={`px-3 py-2 rounded-lg cursor-pointer ${activeTab === 'self' ? 'bg-gray-200 text-sky-700 font-medium' : 'hover:bg-gray-100 text-gray-700 bg-white'}`}
                            onClick={() => {
                                setIsClassroomExam(false);
                                setActiveTab('self');
                                dispatch(setCurrentPage(1));
                                fetchExams({
                                    page: 1,
                                    isClassroomExam: false,
                                    applyFilters: isSearch
                                });
                            }}
                        >
                            Đề tự luyện
                        </div>
                    </div>
                </div>

                {/* Grade filter */}
                <div className="border-b border-gray-200 pb-4">
                    <h3 className="text-base font-medium text-gray-700 mb-2">Lớp</h3>
                    <div className="flex flex-wrap gap-2">
                        {codes?.['grade']?.map((code) => (
                            <div
                                key={code.code}
                                onClick={() => handleSelectGrade(code.code)(selectedGrade !== code.code)}
                                className={`px-3 py-1.5 rounded-lg text-sm cursor-pointer ${selectedGrade === code.code
                                    ? 'bg-gray-200 text-sky-700 border border-gray-300 font-medium'
                                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                                    }`}
                            >
                                {code.description}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Chapter filter */}
                <div className="border-b border-gray-200 pb-4">
                    <h3 className="text-base font-medium text-gray-700 mb-2">Chương</h3>
                    {!selectedGrade ? (
                        <div className="text-sm text-gray-500 italic">
                            Chọn lớp để hiển thị chương
                        </div>
                    ) : (
                        <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
                            {codes?.['chapter']
                                ?.filter((code) => code.code.startsWith(selectedGrade) && code.code.length === 4)
                                ?.map((code) => (
                                    <div
                                        key={code.code}
                                        onClick={() => {
                                            toggleItem(selectedChapters, setSelectedChapters)(code.code)(
                                                !selectedChapters.includes(code.code)
                                            );
                                        }}
                                        className={`px-3 py-1.5 rounded-lg text-sm cursor-pointer ${selectedChapters.includes(code.code)
                                            ? 'bg-gray-200 text-sky-700 border border-gray-300 font-medium'
                                            : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
                                            }`}
                                    >
                                        {code.description}
                                    </div>
                                ))
                            }
                        </div>
                    )}
                </div>

                {/* Exam type filter */}
                <div className="pb-4">
                    <h3 className="text-base font-medium text-gray-700 mb-2">Loại đề</h3>
                    <div className="flex flex-wrap gap-2">
                        {codes?.['exam type']?.map((code) => (
                            <div
                                key={code.code}
                                onClick={() => {
                                    toggleItem(selectedExamTypes, setSelectedExamTypes)(code.code)(
                                        !selectedExamTypes.includes(code.code)
                                    );
                                }}
                                className={`px-3 py-1.5 rounded-lg text-sm cursor-pointer ${selectedExamTypes.includes(code.code)
                                    ? 'bg-sky-50 text-sky-700 border border-sky-300 font-medium'
                                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                                    }`}
                            >
                                {code.description}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Action buttons */}
                <div className="flex flex-col gap-2">
                    <button
                        onClick={handleSearch}
                        className="bg-slate-800 hover:bg-slate-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-all"
                    >
                        Tìm kiếm
                    </button>
                    <button
                        onClick={resetAllFilters}
                        className="border border-gray-300 text-gray-700 hover:bg-gray-200 bg-gray-100 text-sm font-medium py-2 px-4 rounded-lg transition-all"
                    >
                        Xóa bộ lọc
                    </button>
                </div>
            </div>
        </div>
    );
};

export default FilterExamSidebar;
