{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\PracticePage.jsx\",\n  _s = $RefreshSig$();\nimport UserLayout from \"../../../layouts/UserLayout\";\nimport ShowTotalResult from \"../../../components/bar/ShowTotalResult\";\nimport ExamCard from \"../../../components/card/ExamCard\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect, useState } from \"react\";\nimport { setCurrentPage } from \"../../../features/exam/examSlice\";\nimport Pagination from \"../../../components/Pagination\";\nimport LoadingSpinner from \"../../../components/loading/LoadingSpinner\";\nimport NoDataFound from \"../../../assets/images/error-file.png\";\nimport FilterExamTopbar from \"../../../components/filter/FilterExamTopbar\";\nimport FilterExamSidebar from \"../../../components/filter/FilterExamSidebar\";\nimport { Filter, BookOpen, Loader, FileText, GraduationCap } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PracticePage = () => {\n  _s();\n  const {\n    exams,\n    pagination\n  } = useSelector(state => state.exams);\n  const dispatch = useDispatch();\n  const {\n    loading\n  } = useSelector(state => state.states);\n  const {\n    pageSize: limit,\n    page: currentPage,\n    sortOrder,\n    total: totalItems\n  } = pagination;\n  const {\n    codes\n  } = useSelector(state => state.codes);\n  const handlePageChange = page => {\n    dispatch(setCurrentPage(page));\n  };\n  return /*#__PURE__*/_jsxDEV(UserLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden\",\n        children: /*#__PURE__*/_jsxDEV(FilterExamTopbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block w-full md:w-64 lg:w-72 flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(FilterExamSidebar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-zinc-800 mb-4 md:mt-0\",\n              children: \"Danh s\\xE1ch \\u0111\\u1EC1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-h-screen\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center items-center w-full h-40\",\n                children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n                  type: \"dots\",\n                  color: \"border-sky-600\",\n                  size: \"4rem\",\n                  showText: true,\n                  text: \"\\u0110ang t\\u1EA3i danh s\\xE1ch \\u0111\\u1EC1 thi...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 37\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ShowTotalResult, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-b border-gray-200 my-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 41\n                }, this), (exams.length === 0 || !exams[0]) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center flex-col justify-center p-4 w-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: NoDataFound,\n                    alt: \"No Data Found\",\n                    className: \"w-[8rem]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 71,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-500 text-sm sm:text-base md:text-lg\",\n                    children: \"Kh\\xF4ng c\\xF3 \\u0111\\u1EC1 n\\xE0o ph\\xF9 h\\u1EE3p\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col gap-4\",\n                  children: exams.length > 0 && exams[0] && exams.map((exam, index) => /*#__PURE__*/_jsxDEV(ExamCard, {\n                    exam: exam,\n                    codes: codes,\n                    horizontal: true\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 53\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 41\n                }, this), exams.length > 0 && exams[0] && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center mt-8\",\n                  children: /*#__PURE__*/_jsxDEV(Pagination, {\n                    currentPage: currentPage,\n                    totalItems: totalItems,\n                    limit: 10,\n                    onPageChange: handlePageChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 9\n  }, this);\n};\n_s(PracticePage, \"MgJzR6RXTXoeZigIaMfYY+ku0/o=\", false, function () {\n  return [useSelector, useDispatch, useSelector, useSelector];\n});\n_c = PracticePage;\nexport default PracticePage;\nvar _c;\n$RefreshReg$(_c, \"PracticePage\");", "map": {"version": 3, "names": ["UserLayout", "ShowTotalResult", "ExamCard", "useDispatch", "useSelector", "useEffect", "useState", "setCurrentPage", "Pagination", "LoadingSpinner", "NoDataFound", "FilterExamTopbar", "FilterExamSidebar", "Filter", "BookOpen", "Loader", "FileText", "GraduationCap", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PracticePage", "_s", "exams", "pagination", "state", "dispatch", "loading", "states", "pageSize", "limit", "page", "currentPage", "sortOrder", "total", "totalItems", "codes", "handlePageChange", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "color", "size", "showText", "text", "length", "src", "alt", "map", "exam", "index", "horizontal", "onPageChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/PracticePage.jsx"], "sourcesContent": ["import UserLayout from \"../../../layouts/UserLayout\";\r\nimport ShowTotalResult from \"../../../components/bar/ShowTotalResult\";\r\nimport ExamCard from \"../../../components/card/ExamCard\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { setCurrentPage } from \"../../../features/exam/examSlice\";\r\nimport Pagination from \"../../../components/Pagination\";\r\nimport LoadingSpinner from \"../../../components/loading/LoadingSpinner\";\r\nimport NoDataFound from \"../../../assets/images/error-file.png\";\r\nimport FilterExamTopbar from \"../../../components/filter/FilterExamTopbar\";\r\nimport FilterExamSidebar from \"../../../components/filter/FilterExamSidebar\";\r\nimport {\r\n    Filter,\r\n    BookOpen,\r\n    Loader,\r\n    FileText,\r\n    GraduationCap\r\n} from \"lucide-react\";\r\n\r\nconst PracticePage = () => {\r\n    const { exams, pagination } = useSelector((state) => state.exams);\r\n    const dispatch = useDispatch();\r\n    const { loading } = useSelector((state) => state.states);\r\n    const { pageSize: limit, page: currentPage, sortOrder, total: totalItems } = pagination;\r\n    const { codes } = useSelector((state) => state.codes);\r\n\r\n    const handlePageChange = (page) => {\r\n        dispatch(setCurrentPage(page));\r\n    };\r\n\r\n    return (\r\n        <UserLayout>\r\n            <div className=\"bg-white\">\r\n                {/* Mobile filter topbar - visible on mobile only */}\r\n                <div className=\"md:hidden\">\r\n                    <FilterExamTopbar />\r\n                </div>\r\n\r\n                {/* Main content with sidebar */}\r\n                <div className=\" p-4\">\r\n                    <div className=\"flex flex-col md:flex-row gap-6\">\r\n                        {/* Sidebar - visible on desktop only */}\r\n                        <div className=\"hidden md:block w-full md:w-64 lg:w-72 flex-shrink-0\">\r\n                            <FilterExamSidebar />\r\n                        </div>\r\n\r\n                        {/* Main content area */}\r\n                        <div className=\"flex-1 p-4\">\r\n                            <h1 className=\"text-2xl font-bold text-zinc-800 mb-4 md:mt-0\">Danh sách đề</h1>\r\n\r\n                            <div className=\"min-h-screen\">\r\n                                {loading ? (\r\n                                    <div className=\"flex justify-center items-center w-full h-40\">\r\n                                        <LoadingSpinner\r\n                                            type=\"dots\"\r\n                                            color=\"border-sky-600\"\r\n                                            size=\"4rem\"\r\n                                            showText={true}\r\n                                            text=\"Đang tải danh sách đề thi...\"\r\n                                        />\r\n                                    </div>\r\n                                ) : (\r\n                                    <>\r\n                                        <ShowTotalResult />\r\n\r\n                                        <div className=\"border-b border-gray-200 my-4\" />\r\n\r\n                                        {/* Exam list */}\r\n                                        {(exams.length === 0 || !exams[0]) && (\r\n                                            <div className=\"flex items-center flex-col justify-center p-4 w-full\">\r\n                                                <img src={NoDataFound} alt=\"No Data Found\" className=\"w-[8rem]\" />\r\n                                                <p className=\"text-gray-500 text-sm sm:text-base md:text-lg\">Không có đề nào phù hợp</p>\r\n                                            </div>\r\n                                        )}\r\n                                        <div className=\"flex flex-col gap-4\">\r\n                                            {(exams.length > 0 && exams[0]) && (\r\n                                                exams.map((exam, index) => (\r\n                                                    <ExamCard\r\n                                                        key={index}\r\n                                                        exam={exam}\r\n                                                        codes={codes}\r\n                                                        horizontal={true}\r\n                                                    />\r\n                                                ))\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        {/* Pagination */}\r\n                                        {(exams.length > 0 && exams[0]) && (\r\n                                            <div className=\"flex justify-center mt-8\">\r\n                                                <Pagination\r\n                                                    currentPage={currentPage}\r\n                                                    totalItems={totalItems}\r\n                                                    limit={10}\r\n                                                    onPageChange={handlePageChange}\r\n                                                />\r\n                                            </div>\r\n                                        )}\r\n                                    </>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </UserLayout>\r\n    );\r\n};\r\n\r\nexport default PracticePage;\r\n"], "mappings": ";;AAAA,OAAOA,UAAU,MAAM,6BAA6B;AACpD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,SACIC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,aAAa,QACV,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGrB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACF,KAAK,CAAC;EACjE,MAAMG,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB;EAAQ,CAAC,GAAGxB,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACG,MAAM,CAAC;EACxD,MAAM;IAAEC,QAAQ,EAAEC,KAAK;IAAEC,IAAI,EAAEC,WAAW;IAAEC,SAAS;IAAEC,KAAK,EAAEC;EAAW,CAAC,GAAGX,UAAU;EACvF,MAAM;IAAEY;EAAM,CAAC,GAAGjC,WAAW,CAAEsB,KAAK,IAAKA,KAAK,CAACW,KAAK,CAAC;EAErD,MAAMC,gBAAgB,GAAIN,IAAI,IAAK;IAC/BL,QAAQ,CAACpB,cAAc,CAACyB,IAAI,CAAC,CAAC;EAClC,CAAC;EAED,oBACIb,OAAA,CAACnB,UAAU;IAAAuC,QAAA,eACPpB,OAAA;MAAKqB,SAAS,EAAC,UAAU;MAAAD,QAAA,gBAErBpB,OAAA;QAAKqB,SAAS,EAAC,WAAW;QAAAD,QAAA,eACtBpB,OAAA,CAACR,gBAAgB;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAGNzB,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAAAD,QAAA,eACjBpB,OAAA;UAAKqB,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAE5CpB,OAAA;YAAKqB,SAAS,EAAC,sDAAsD;YAAAD,QAAA,eACjEpB,OAAA,CAACP,iBAAiB;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAGNzB,OAAA;YAAKqB,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACvBpB,OAAA;cAAIqB,SAAS,EAAC,+CAA+C;cAAAD,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAE/EzB,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAD,QAAA,EACxBX,OAAO,gBACJT,OAAA;gBAAKqB,SAAS,EAAC,8CAA8C;gBAAAD,QAAA,eACzDpB,OAAA,CAACV,cAAc;kBACXoC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,gBAAgB;kBACtBC,IAAI,EAAC,MAAM;kBACXC,QAAQ,EAAE,IAAK;kBACfC,IAAI,EAAC;gBAA8B;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,gBAENzB,OAAA,CAAAE,SAAA;gBAAAkB,QAAA,gBACIpB,OAAA,CAAClB,eAAe;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEnBzB,OAAA;kBAAKqB,SAAS,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAGhD,CAACpB,KAAK,CAAC0B,MAAM,KAAK,CAAC,IAAI,CAAC1B,KAAK,CAAC,CAAC,CAAC,kBAC7BL,OAAA;kBAAKqB,SAAS,EAAC,sDAAsD;kBAAAD,QAAA,gBACjEpB,OAAA;oBAAKgC,GAAG,EAAEzC,WAAY;oBAAC0C,GAAG,EAAC,eAAe;oBAACZ,SAAS,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClEzB,OAAA;oBAAGqB,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvF,CACR,eACDzB,OAAA;kBAAKqB,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAC9Bf,KAAK,CAAC0B,MAAM,GAAG,CAAC,IAAI1B,KAAK,CAAC,CAAC,CAAC,IAC1BA,KAAK,CAAC6B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAClBpC,OAAA,CAACjB,QAAQ;oBAELoD,IAAI,EAAEA,IAAK;oBACXjB,KAAK,EAAEA,KAAM;oBACbmB,UAAU,EAAE;kBAAK,GAHZD,KAAK;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIb,CACJ;gBACJ;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,EAGJpB,KAAK,CAAC0B,MAAM,GAAG,CAAC,IAAI1B,KAAK,CAAC,CAAC,CAAC,iBAC1BL,OAAA;kBAAKqB,SAAS,EAAC,0BAA0B;kBAAAD,QAAA,eACrCpB,OAAA,CAACX,UAAU;oBACPyB,WAAW,EAAEA,WAAY;oBACzBG,UAAU,EAAEA,UAAW;oBACvBL,KAAK,EAAE,EAAG;oBACV0B,YAAY,EAAEnB;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR;cAAA,eACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAACrB,EAAA,CAxFID,YAAY;EAAA,QACgBlB,WAAW,EACxBD,WAAW,EACRC,WAAW,EAEbA,WAAW;AAAA;AAAAsD,EAAA,GAL3BpC,YAAY;AA0FlB,eAAeA,YAAY;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}