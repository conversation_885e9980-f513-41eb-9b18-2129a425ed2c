{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\card\\\\ExamCard.jsx\",\n  _s = $RefreshSig$();\nimport ExamDefaultImage from \"../../assets/images/defaultExamImage.png\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { saveExamForUser } from \"../../features/exam/examSlice\";\nimport React from \"react\";\nimport { Calendar, Clock, BookOpen, GraduationCap, ChevronRight, Bookmark, CheckCircle, Lock, Play, Eye } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst formatDate = dateString => {\n  if (!dateString) return \"\";\n  const date = new Date(dateString);\n  return date.toLocaleDateString(\"vi-VN\", {\n    day: \"2-digit\",\n    month: \"2-digit\",\n    year: \"numeric\"\n  });\n};\nconst ExamCard = _ref => {\n  _s();\n  var _codes$examType2, _codes$examType2$find;\n  let {\n    exam,\n    codes,\n    horizontal = false\n  } = _ref;\n  const {\n    name,\n    typeOfExam,\n    class: examClass,\n    chapter,\n    testDuration,\n    createdAt,\n    imageUrl,\n    id,\n    isSave,\n    isDone,\n    acceptDoExam = true\n  } = exam;\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const handleClicked = () => navigate(\"/practice/exam/\".concat(id));\n  const handleSaveExam = e => {\n    e.stopPropagation();\n    dispatch(saveExamForUser({\n      examId: id\n    }));\n  };\n\n  // Old components removed - replaced with modern Lucide icons design\n\n  // Get exam type description\n  const getExamTypeDescription = () => {\n    var _codes$examType, _codes$examType$find;\n    return codes && ((_codes$examType = codes['exam type']) === null || _codes$examType === void 0 ? void 0 : (_codes$examType$find = _codes$examType.find(c => c.code === typeOfExam)) === null || _codes$examType$find === void 0 ? void 0 : _codes$examType$find.description) || typeOfExam || '';\n  };\n\n  // Get chapter description\n  const getChapterDescription = () => {\n    var _codes$chapter, _codes$chapter$find;\n    return chapter ? ((_codes$chapter = codes['chapter']) === null || _codes$chapter === void 0 ? void 0 : (_codes$chapter$find = _codes$chapter.find(c => c.code === chapter)) === null || _codes$chapter$find === void 0 ? void 0 : _codes$chapter$find.description) || chapter : 'Không có';\n  };\n\n  // Get status info\n  const getStatusInfo = () => {\n    if (isDone) {\n      return {\n        icon: CheckCircle,\n        text: 'Đã hoàn thành',\n        color: 'text-green-600',\n        bgColor: 'bg-green-50'\n      };\n    } else if (!acceptDoExam) {\n      return {\n        icon: Lock,\n        text: 'Không thể làm',\n        color: 'text-orange-500',\n        bgColor: 'bg-orange-50'\n      };\n    } else {\n      return {\n        icon: Play,\n        text: 'Có thể làm',\n        color: 'text-cyan-600',\n        bgColor: 'bg-cyan-50'\n      };\n    }\n  };\n  const statusInfo = getStatusInfo();\n  const StatusIconComponent = statusInfo.icon;\n\n  // Render horizontal layout (ArticleCard style)\n  if (horizontal) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 hover:bg-gray-50 border-b border-gray-100 last:border-b-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-sky-600 hover:text-sky-800 cursor-pointer\",\n          onClick: handleClicked,\n          title: name,\n          children: name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 ml-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSaveExam,\n            className: \"p-2 rounded-full transition-colors \".concat(isSave ? 'text-sky-600 bg-sky-50 hover:bg-sky-100' : 'text-gray-400 hover:text-sky-600 hover:bg-sky-50'),\n            title: isSave ? \"Đã lưu đề thi\" : \"Lưu đề thi\",\n            children: /*#__PURE__*/_jsxDEV(Bookmark, {\n              size: 16,\n              className: isSave ? 'fill-current' : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 rounded-full \".concat(statusInfo.bgColor),\n            children: /*#__PURE__*/_jsxDEV(StatusIconComponent, {\n              size: 16,\n              className: statusInfo.color\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-4 text-sm text-gray-500 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(GraduationCap, {\n            size: 14,\n            className: \"mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"L\\u1EDBp \", examClass]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(BookOpen, {\n            size: 14,\n            className: \"mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: getChapterDescription()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Clock, {\n            size: 14,\n            className: \"mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: testDuration ? testDuration + ' phút' : 'Không có'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            size: 14,\n            className: \"mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: formatDate(createdAt)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full\",\n            children: getExamTypeDescription()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-3 py-1 text-sm rounded-full \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n            children: statusInfo.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-sm text-sky-600 hover:text-sky-800 font-medium flex items-center\",\n          onClick: e => {\n            e.stopPropagation();\n            handleClicked();\n          },\n          children: [isDone ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Eye, {\n              size: 16,\n              className: \"mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 33\n            }, this), \"Xem l\\u1EA1i b\\xE0i l\\xE0m\"]\n          }, void 0, true) : acceptDoExam ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              size: 16,\n              className: \"mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 33\n            }, this), \"B\\u1EAFt \\u0111\\u1EA7u l\\xE0m b\\xE0i\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Lock, {\n              size: 16,\n              className: \"mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 33\n            }, this), \"Kh\\xF4ng th\\u1EC3 l\\xE0m\"]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(ChevronRight, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Render vertical layout (original)\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer flex flex-col h-full\",\n    onClick: handleClicked,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 sm:p-4 flex-1 flex flex-col\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              title: name,\n              className: \"text-sm font-semibold font-bevietnam text-black flex-1\",\n              children: (name === null || name === void 0 ? void 0 : name.length) > 30 ? (name === null || name === void 0 ? void 0 : name.slice(0, 30)) + \"...\" : name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs font-medium text-gray-800\",\n              children: codes && ((_codes$examType2 = codes['exam type']) === null || _codes$examType2 === void 0 ? void 0 : (_codes$examType2$find = _codes$examType2.find(c => c.code === typeOfExam)) === null || _codes$examType2$find === void 0 ? void 0 : _codes$examType2$find.description) || typeOfExam || ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"items-center sm:flex hidden gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(BookmarkButton, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"items-center sm:hidden flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(BookmarkButton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(StatusIndicator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-px w-full bg-gray-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap items-center text-xs sm:text-sm text-gray-600 gap-x-2 gap-y-1\",\n          children: examDetails.map((detail, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [index > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-300\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 47\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center shrink-0\",\n              children: [detail.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [detail.label, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-800\",\n                  children: detail.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 58\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 33\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 9\n  }, this);\n};\n_s(ExamCard, \"ZaVe+Vo7W9FMoQ/aTgBrV7UvA04=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = ExamCard;\nexport default ExamCard;\nvar _c;\n$RefreshReg$(_c, \"ExamCard\");", "map": {"version": 3, "names": ["ExamDefaultImage", "useNavigate", "useDispatch", "saveExamForUser", "React", "Calendar", "Clock", "BookOpen", "GraduationCap", "ChevronRight", "Bookmark", "CheckCircle", "Lock", "Play", "Eye", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "day", "month", "year", "ExamCard", "_ref", "_s", "_codes$examType2", "_codes$examType2$find", "exam", "codes", "horizontal", "name", "typeOfExam", "class", "examClass", "chapter", "testDuration", "createdAt", "imageUrl", "id", "isSave", "isDone", "acceptDoExam", "navigate", "dispatch", "handleClicked", "concat", "handleSaveExam", "e", "stopPropagation", "examId", "getExamTypeDescription", "_codes$examType", "_codes$examType$find", "find", "c", "code", "description", "getChapterDescription", "_codes$chapter", "_codes$chapter$find", "getStatusInfo", "icon", "text", "color", "bgColor", "statusInfo", "StatusIconComponent", "className", "children", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "length", "slice", "BookmarkButton", "StatusIndicator", "examDetails", "map", "detail", "index", "label", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/card/ExamCard.jsx"], "sourcesContent": ["import ExamDefaultImage from \"../../assets/images/defaultExamImage.png\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { saveExamForUser } from \"../../features/exam/examSlice\";\r\nimport React from \"react\";\r\nimport {\r\n    Calendar,\r\n    Clock,\r\n    BookOpen,\r\n    GraduationCap,\r\n    ChevronRight,\r\n    Bookmark,\r\n    CheckCircle,\r\n    Lock,\r\n    Play,\r\n    Eye\r\n} from \"lucide-react\";\r\n\r\nconst formatDate = (dateString) => {\r\n    if (!dateString) return \"\";\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString(\"vi-VN\", {\r\n        day: \"2-digit\",\r\n        month: \"2-digit\",\r\n        year: \"numeric\",\r\n    });\r\n};\r\n\r\nconst ExamCard = ({ exam, codes, horizontal = false }) => {\r\n    const { name, typeOfExam, class: examClass, chapter, testDuration, createdAt, imageUrl, id, isSave, isDone, acceptDoExam = true } = exam;\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n\r\n    const handleClicked = () => navigate(`/practice/exam/${id}`);\r\n    const handleSaveExam = (e) => {\r\n        e.stopPropagation();\r\n        dispatch(saveExamForUser({ examId: id }));\r\n    };\r\n\r\n    // Old components removed - replaced with modern Lucide icons design\r\n\r\n    // Get exam type description\r\n    const getExamTypeDescription = () => {\r\n        return codes && codes['exam type']?.find(c => c.code === typeOfExam)?.description || typeOfExam || '';\r\n    };\r\n\r\n    // Get chapter description\r\n    const getChapterDescription = () => {\r\n        return chapter ? codes['chapter']?.find(c => c.code === chapter)?.description || chapter : 'Không có';\r\n    };\r\n\r\n    // Get status info\r\n    const getStatusInfo = () => {\r\n        if (isDone) {\r\n            return {\r\n                icon: CheckCircle,\r\n                text: 'Đã hoàn thành',\r\n                color: 'text-green-600',\r\n                bgColor: 'bg-green-50'\r\n            };\r\n        } else if (!acceptDoExam) {\r\n            return {\r\n                icon: Lock,\r\n                text: 'Không thể làm',\r\n                color: 'text-orange-500',\r\n                bgColor: 'bg-orange-50'\r\n            };\r\n        } else {\r\n            return {\r\n                icon: Play,\r\n                text: 'Có thể làm',\r\n                color: 'text-cyan-600',\r\n                bgColor: 'bg-cyan-50'\r\n            };\r\n        }\r\n    };\r\n\r\n    const statusInfo = getStatusInfo();\r\n    const StatusIconComponent = statusInfo.icon;\r\n\r\n    // Render horizontal layout (ArticleCard style)\r\n    if (horizontal) {\r\n        return (\r\n            <div className=\"p-6 hover:bg-gray-50 border-b border-gray-100 last:border-b-0\">\r\n                {/* Header */}\r\n                <div className=\"flex items-start justify-between mb-3\">\r\n                    <h3\r\n                        className=\"text-lg font-medium text-sky-600 hover:text-sky-800 cursor-pointer\"\r\n                        onClick={handleClicked}\r\n                        title={name}\r\n                    >\r\n                        {name}\r\n                    </h3>\r\n                    <div className=\"flex items-center gap-2 ml-4\">\r\n                        <button\r\n                            onClick={handleSaveExam}\r\n                            className={`p-2 rounded-full transition-colors ${isSave\r\n                                ? 'text-sky-600 bg-sky-50 hover:bg-sky-100'\r\n                                : 'text-gray-400 hover:text-sky-600 hover:bg-sky-50'\r\n                            }`}\r\n                            title={isSave ? \"Đã lưu đề thi\" : \"Lưu đề thi\"}\r\n                        >\r\n                            <Bookmark size={16} className={isSave ? 'fill-current' : ''} />\r\n                        </button>\r\n                        <div className={`p-2 rounded-full ${statusInfo.bgColor}`}>\r\n                            <StatusIconComponent size={16} className={statusInfo.color} />\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Meta information */}\r\n                <div className=\"flex flex-wrap gap-4 text-sm text-gray-500 mb-3\">\r\n                    <div className=\"flex items-center\">\r\n                        <GraduationCap size={14} className=\"mr-1\" />\r\n                        <span>Lớp {examClass}</span>\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                        <BookOpen size={14} className=\"mr-1\" />\r\n                        <span>{getChapterDescription()}</span>\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                        <Clock size={14} className=\"mr-1\" />\r\n                        <span>{testDuration ? testDuration + ' phút' : 'Không có'}</span>\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                        <Calendar size={14} className=\"mr-1\" />\r\n                        <span>{formatDate(createdAt)}</span>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Exam type and status */}\r\n                <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <span className=\"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full\">\r\n                            {getExamTypeDescription()}\r\n                        </span>\r\n                        <span className={`px-3 py-1 text-sm rounded-full ${statusInfo.bgColor} ${statusInfo.color}`}>\r\n                            {statusInfo.text}\r\n                        </span>\r\n                    </div>\r\n                    <button\r\n                        className=\"text-sm text-sky-600 hover:text-sky-800 font-medium flex items-center\"\r\n                        onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleClicked();\r\n                        }}\r\n                    >\r\n                        {isDone ? (\r\n                            <>\r\n                                <Eye size={16} className=\"mr-1\" />\r\n                                Xem lại bài làm\r\n                            </>\r\n                        ) : acceptDoExam ? (\r\n                            <>\r\n                                <Play size={16} className=\"mr-1\" />\r\n                                Bắt đầu làm bài\r\n                            </>\r\n                        ) : (\r\n                            <>\r\n                                <Lock size={16} className=\"mr-1\" />\r\n                                Không thể làm\r\n                            </>\r\n                        )}\r\n                        <ChevronRight size={16} />\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Render vertical layout (original)\r\n    return (\r\n        <div\r\n            className=\"bg-white rounded shadow-md hover:shadow-lg transition overflow-hidden border border-gray-200 cursor-pointer flex flex-col h-full\"\r\n            onClick={handleClicked}\r\n        >\r\n            <div className=\"p-3 sm:p-4 flex-1 flex flex-col\">\r\n                {/* Header with icon */}\r\n                <div className=\"flex-1 space-y-2\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex flex-col\">\r\n                            <p\r\n                                title={name}\r\n                                className=\"text-sm font-semibold font-bevietnam text-black flex-1\"\r\n                            >\r\n                                {name?.length > 30 ? name?.slice(0, 30) + \"...\" : name}\r\n                            </p>\r\n                            <p className=\"text-xs font-medium text-gray-800\">\r\n                                {codes && codes['exam type']?.find(c => c.code === typeOfExam)?.description || typeOfExam || ''}\r\n                            </p>\r\n                        </div>\r\n                        <div className=\"items-center sm:flex hidden gap-2\">\r\n                            <BookmarkButton />\r\n                            <StatusIndicator />\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"items-center sm:hidden flex gap-2\">\r\n                        <BookmarkButton />\r\n                        <StatusIndicator />\r\n                    </div>\r\n\r\n                    {/* Divider */}\r\n                    <div className=\"h-px w-full bg-gray-100\"></div>\r\n\r\n                    {/* Exam details */}\r\n                    <div className=\"flex flex-wrap items-center text-xs sm:text-sm text-gray-600 gap-x-2 gap-y-1\">\r\n                        {examDetails.map((detail, index) => (\r\n                            <React.Fragment key={index}>\r\n                                {index > 0 && <span className=\"text-gray-300\">|</span>}\r\n                                <div className=\"flex items-center shrink-0\">\r\n                                    {detail.icon}\r\n                                    <span>{detail.label} <span className=\"font-medium text-gray-800\">{detail.value}</span></span>\r\n                                </div>\r\n                            </React.Fragment>\r\n                        ))}\r\n                    </div>\r\n\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ExamCard;\r\n"], "mappings": ";;AAAA,OAAOA,gBAAgB,MAAM,0CAA0C;AACvE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SACIC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,aAAa,EACbC,YAAY,EACZC,QAAQ,EACRC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,GAAG,QACA,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,UAAU,GAAIC,UAAU,IAAK;EAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACpCC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACV,CAAC,CAAC;AACN,CAAC;AAED,MAAMC,QAAQ,GAAGC,IAAA,IAAyC;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,qBAAA;EAAA,IAAxC;IAAEC,IAAI;IAAEC,KAAK;IAAEC,UAAU,GAAG;EAAM,CAAC,GAAAN,IAAA;EACjD,MAAM;IAAEO,IAAI;IAAEC,UAAU;IAAEC,KAAK,EAAEC,SAAS;IAAEC,OAAO;IAAEC,YAAY;IAAEC,SAAS;IAAEC,QAAQ;IAAEC,EAAE;IAAEC,MAAM;IAAEC,MAAM;IAAEC,YAAY,GAAG;EAAK,CAAC,GAAGd,IAAI;EACxI,MAAMe,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM+C,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAE9B,MAAM+C,aAAa,GAAGA,CAAA,KAAMF,QAAQ,mBAAAG,MAAA,CAAmBP,EAAE,CAAE,CAAC;EAC5D,MAAMQ,cAAc,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBL,QAAQ,CAAC7C,eAAe,CAAC;MAAEmD,MAAM,EAAEX;IAAG,CAAC,CAAC,CAAC;EAC7C,CAAC;;EAED;;EAEA;EACA,MAAMY,sBAAsB,GAAGA,CAAA,KAAM;IAAA,IAAAC,eAAA,EAAAC,oBAAA;IACjC,OAAOxB,KAAK,MAAAuB,eAAA,GAAIvB,KAAK,CAAC,WAAW,CAAC,cAAAuB,eAAA,wBAAAC,oBAAA,GAAlBD,eAAA,CAAoBE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKxB,UAAU,CAAC,cAAAqB,oBAAA,uBAApDA,oBAAA,CAAsDI,WAAW,KAAIzB,UAAU,IAAI,EAAE;EACzG,CAAC;;EAED;EACA,MAAM0B,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,cAAA,EAAAC,mBAAA;IAChC,OAAOzB,OAAO,GAAG,EAAAwB,cAAA,GAAA9B,KAAK,CAAC,SAAS,CAAC,cAAA8B,cAAA,wBAAAC,mBAAA,GAAhBD,cAAA,CAAkBL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKrB,OAAO,CAAC,cAAAyB,mBAAA,uBAA/CA,mBAAA,CAAiDH,WAAW,KAAItB,OAAO,GAAG,UAAU;EACzG,CAAC;;EAED;EACA,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIpB,MAAM,EAAE;MACR,OAAO;QACHqB,IAAI,EAAEvD,WAAW;QACjBwD,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE;MACb,CAAC;IACL,CAAC,MAAM,IAAI,CAACvB,YAAY,EAAE;MACtB,OAAO;QACHoB,IAAI,EAAEtD,IAAI;QACVuD,IAAI,EAAE,eAAe;QACrBC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE;MACb,CAAC;IACL,CAAC,MAAM;MACH,OAAO;QACHH,IAAI,EAAErD,IAAI;QACVsD,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,eAAe;QACtBC,OAAO,EAAE;MACb,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,UAAU,GAAGL,aAAa,CAAC,CAAC;EAClC,MAAMM,mBAAmB,GAAGD,UAAU,CAACJ,IAAI;;EAE3C;EACA,IAAIhC,UAAU,EAAE;IACZ,oBACIlB,OAAA;MAAKwD,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAE1EzD,OAAA;QAAKwD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAClDzD,OAAA;UACIwD,SAAS,EAAC,oEAAoE;UAC9EE,OAAO,EAAEzB,aAAc;UACvB0B,KAAK,EAAExC,IAAK;UAAAsC,QAAA,EAEXtC;QAAI;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACL/D,OAAA;UAAKwD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBACzCzD,OAAA;YACI0D,OAAO,EAAEvB,cAAe;YACxBqB,SAAS,wCAAAtB,MAAA,CAAwCN,MAAM,GACjD,yCAAyC,GACzC,kDAAkD,CACrD;YACH+B,KAAK,EAAE/B,MAAM,GAAG,eAAe,GAAG,YAAa;YAAA6B,QAAA,eAE/CzD,OAAA,CAACN,QAAQ;cAACsE,IAAI,EAAE,EAAG;cAACR,SAAS,EAAE5B,MAAM,GAAG,cAAc,GAAG;YAAG;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACT/D,OAAA;YAAKwD,SAAS,sBAAAtB,MAAA,CAAsBoB,UAAU,CAACD,OAAO,CAAG;YAAAI,QAAA,eACrDzD,OAAA,CAACuD,mBAAmB;cAACS,IAAI,EAAE,EAAG;cAACR,SAAS,EAAEF,UAAU,CAACF;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN/D,OAAA;QAAKwD,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC5DzD,OAAA;UAAKwD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BzD,OAAA,CAACR,aAAa;YAACwE,IAAI,EAAE,EAAG;YAACR,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C/D,OAAA;YAAAyD,QAAA,GAAM,WAAI,EAACnC,SAAS;UAAA;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACN/D,OAAA;UAAKwD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BzD,OAAA,CAACT,QAAQ;YAACyE,IAAI,EAAE,EAAG;YAACR,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvC/D,OAAA;YAAAyD,QAAA,EAAOX,qBAAqB,CAAC;UAAC;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACN/D,OAAA;UAAKwD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BzD,OAAA,CAACV,KAAK;YAAC0E,IAAI,EAAE,EAAG;YAACR,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpC/D,OAAA;YAAAyD,QAAA,EAAOjC,YAAY,GAAGA,YAAY,GAAG,OAAO,GAAG;UAAU;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACN/D,OAAA;UAAKwD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BzD,OAAA,CAACX,QAAQ;YAAC2E,IAAI,EAAE,EAAG;YAACR,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvC/D,OAAA;YAAAyD,QAAA,EAAOtD,UAAU,CAACsB,SAAS;UAAC;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN/D,OAAA;QAAKwD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9CzD,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACpCzD,OAAA;YAAMwD,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EACrElB,sBAAsB,CAAC;UAAC;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACP/D,OAAA;YAAMwD,SAAS,oCAAAtB,MAAA,CAAoCoB,UAAU,CAACD,OAAO,OAAAnB,MAAA,CAAIoB,UAAU,CAACF,KAAK,CAAG;YAAAK,QAAA,EACvFH,UAAU,CAACH;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/D,OAAA;UACIwD,SAAS,EAAC,uEAAuE;UACjFE,OAAO,EAAGtB,CAAC,IAAK;YACZA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBJ,aAAa,CAAC,CAAC;UACnB,CAAE;UAAAwB,QAAA,GAED5B,MAAM,gBACH7B,OAAA,CAAAE,SAAA;YAAAuD,QAAA,gBACIzD,OAAA,CAACF,GAAG;cAACkE,IAAI,EAAE,EAAG;cAACR,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8BAEtC;UAAA,eAAE,CAAC,GACHjC,YAAY,gBACZ9B,OAAA,CAAAE,SAAA;YAAAuD,QAAA,gBACIzD,OAAA,CAACH,IAAI;cAACmE,IAAI,EAAE,EAAG;cAACR,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wCAEvC;UAAA,eAAE,CAAC,gBAEH/D,OAAA,CAAAE,SAAA;YAAAuD,QAAA,gBACIzD,OAAA,CAACJ,IAAI;cAACoE,IAAI,EAAE,EAAG;cAACR,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEvC;UAAA,eAAE,CACL,eACD/D,OAAA,CAACP,YAAY;YAACuE,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;;EAEA;EACA,oBACI/D,OAAA;IACIwD,SAAS,EAAC,kIAAkI;IAC5IE,OAAO,EAAEzB,aAAc;IAAAwB,QAAA,eAEvBzD,OAAA;MAAKwD,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAE5CzD,OAAA;QAAKwD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BzD,OAAA;UAAKwD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9CzD,OAAA;YAAKwD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BzD,OAAA;cACI2D,KAAK,EAAExC,IAAK;cACZqC,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAEjE,CAAAtC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,MAAM,IAAG,EAAE,GAAG,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK,GAAG/C;YAAI;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACJ/D,OAAA;cAAGwD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC3CxC,KAAK,MAAAH,gBAAA,GAAIG,KAAK,CAAC,WAAW,CAAC,cAAAH,gBAAA,wBAAAC,qBAAA,GAAlBD,gBAAA,CAAoB4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKxB,UAAU,CAAC,cAAAL,qBAAA,uBAApDA,qBAAA,CAAsD8B,WAAW,KAAIzB,UAAU,IAAI;YAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN/D,OAAA;YAAKwD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC9CzD,OAAA,CAACmE,cAAc;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClB/D,OAAA,CAACoE,eAAe;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN/D,OAAA;UAAKwD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9CzD,OAAA,CAACmE,cAAc;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClB/D,OAAA,CAACoE,eAAe;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAGN/D,OAAA;UAAKwD,SAAS,EAAC;QAAyB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG/C/D,OAAA;UAAKwD,SAAS,EAAC,8EAA8E;UAAAC,QAAA,EACxFY,WAAW,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC3BxE,OAAA,CAACZ,KAAK,CAACa,QAAQ;YAAAwD,QAAA,GACVe,KAAK,GAAG,CAAC,iBAAIxE,OAAA;cAAMwD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD/D,OAAA;cAAKwD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GACtCc,MAAM,CAACrB,IAAI,eACZlD,OAAA;gBAAAyD,QAAA,GAAOc,MAAM,CAACE,KAAK,EAAC,GAAC,eAAAzE,OAAA;kBAAMwD,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEc,MAAM,CAACG;gBAAK;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA,GALWS,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAClD,EAAA,CAlMIF,QAAQ;EAAA,QAEO1B,WAAW,EACXC,WAAW;AAAA;AAAAyF,EAAA,GAH1BhE,QAAQ;AAoMd,eAAeA,QAAQ;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}