import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as tuitionApi from "../../services/tuitionApi";
import { setCurrentPage, setTotalPages, setTotalItems, setLimit } from "../filter/filterSlice";
import { apiHandler } from "../../utils/apiHandler";
import { setErrorMessage, setSuccessMessage } from "../state/stateApiSlice";

// Class Tuition Thunks
export const fetchClassTuitions = createAsyncThunk(
  "tuition/fetchClassTuitions",
  async ({ search, currentPage, limit, sortOrder }, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getAllClassTuitionAPI,
      { search, page: currentPage, limit, sortOrder },
      (data) => {
        const { limit, page, totalPages, totalRows } = data.pagination;
        dispatch(setCurrentPage(page));
        dispatch(setTotalPages(totalPages));
        dispatch(setTotalItems(totalRows));
        dispatch(setLimit(limit));
      },
      true,
      false
    );
  }
);

export const fetchClassTuitionsByClassId = createAsyncThunk(
  "tuition/fetchClassTuitionsByClassId",
  async ({ classId, search, currentPage, limit, sortOrder }, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getClassTuitionByClassIdAPI,
      {classId, search, page: currentPage, limit, sortOrder },
      (data) => {
        dispatch(setCurrentPage(data.currentPage));
        dispatch(setTotalPages(data.totalPages));
        dispatch(setTotalItems(data.totalItems));
      },
      true,
      false
    );
  }
);

export const fetchClassTuitionById = createAsyncThunk(
  "tuition/fetchClassTuitionById",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getClassTuitionByIdAPI,
      id,
      null,
      true,
      false
    );
  }
);

export const createClassTuition = createAsyncThunk(
  "tuition/createClassTuition",
  async (tuitionData, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.createClassTuitionAPI,
      tuitionData,
      (data) => {
        dispatch(setSuccessMessage("Học phí lớp học đã được tạo thành công!"));
      },
      true,
      true
    );
  }
);

export const updateClassTuition = createAsyncThunk(
  "tuition/updateClassTuition",
  async ({ id, tuitionData }, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.updateClassTuitionAPI,
      [id, tuitionData],
      (data) => {
        dispatch(setSuccessMessage("Học phí lớp học đã được cập nhật thành công!"));
      },
      true,
      true
    );
  }
);

export const deleteClassTuition = createAsyncThunk(
  "tuition/deleteClassTuition",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.deleteClassTuitionAPI,
      id,
      (data) => {
        dispatch(setSuccessMessage("Học phí lớp học đã được xóa thành công!"));
      },
      true,
      true
    );
  }
);

export const createBatchClassTuition = createAsyncThunk(
  "tuition/createBatchClassTuition",
  async (batchData, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.createBatchClassTuitionAPI,
      batchData,
      (data) => {
      },
      true,
      true
    );
  }
);

// Tuition Payment Thunks
export const fetchTuitionPayments = createAsyncThunk(
  "tuition/fetchTuitionPayments",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getAllTuitionPaymentsAPI,
      params,
      (data) => {
        const { limit, page, totalPages, totalRows } = data.pagination;
        dispatch(setCurrentPage(page));
        dispatch(setTotalPages(totalPages));
        dispatch(setTotalItems(totalRows));
        dispatch(setLimit(limit));
      },
      true,
      false
    );
  }
);

export const fetchClassTuitionPayments = createAsyncThunk(
  "tuition/fetchClassTuitionPayments",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getClassTuitionPaymentsAPI,
      params,
      (data) => {
        const { limit, page, totalPages, totalRows } = data.pagination;
        dispatch(setCurrentPage(page));
        dispatch(setTotalPages(totalPages));
        dispatch(setTotalItems(totalRows));
        dispatch(setLimit(limit));
      },
      true,
      false
    );
  }
);

export const fetchUserTuitionPaymentsAdmin = createAsyncThunk(
  "tuition/fetchUserTuitionPaymentsAdmin",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionPaymentsAdminAPI,
      params,
      (data) => {
        dispatch(setCurrentPage(data.currentPage));
        dispatch(setTotalPages(data.totalPages));
        dispatch(setTotalItems(data.totalItems));
      },
      true,
      false
    );
  }
);

export const fetchUserTuitionPayments = createAsyncThunk(
  "tuition/fetchUserTuitionPayments",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionPaymentsAPI,
      params,
      (data) => {
        const { limit, page, totalPages, totalRows } = data.pagination;
        dispatch(setCurrentPage(page));
        dispatch(setTotalPages(totalPages));
        dispatch(setTotalItems(totalRows));
        dispatch(setLimit(limit));
      },
      false,
      false
    );
  }
);

export const fetchTuitionPaymentByIdAdmin = createAsyncThunk(
  "tuition/fetchTuitionPaymentByIdAdmin",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getTuitionPaymentByIdAdminAPI,
      id,
      null,
      true,
      false
    );
  }
);

export const fetchTuitionPaymentByIdUser = createAsyncThunk(
  "tuition/fetchTuitionPaymentByIdUser",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getTuitionPaymentByIdUserAPI,
      id,
      null,
      true,
      false
    );
  }
);

// Alias cho fetchTuitionPaymentByIdUser để sử dụng trong trang chi tiết học phí
export const fetchUserTuitionPaymentById = fetchTuitionPaymentByIdUser;

export const createTuitionPayment = createAsyncThunk(
  "tuition/createTuitionPayment",
  async (paymentData, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.createTuitionPaymentAPI,
      paymentData,
      (data) => {
        dispatch(setSuccessMessage("Thanh toán học phí đã được tạo thành công!"));
      },
      true,
      true
    );
  }
);

export const createBatchTuitionPayments = createAsyncThunk(
  "tuition/createBatchTuitionPayments",
  async (batchData, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.createBatchTuitionPaymentsAPI,
      batchData,
      (data) => {
        dispatch(setSuccessMessage("Các thanh toán học phí đã được tạo thành công!"));
      },
      true,
      true
    );
  }
);

export const updateTuitionPayment = createAsyncThunk(
  "tuition/updateTuitionPayment",
  async ({ id, paymentData }, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.updateTuitionPaymentAPI,
      { id, paymentData },
      (data) => {
        dispatch(setSuccessMessage("Thanh toán học phí đã được cập nhật thành công!"));
      },
      true,
      true
    );
  }
);

export const deleteTuitionPayment = createAsyncThunk(
  "tuition/deleteTuitionPayment",
  async (id, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.deleteTuitionPaymentAPI,
      id,
      (data) => {
        dispatch(setSuccessMessage("Thanh toán học phí đã được xóa thành công!"));
      },
      true,
      true
    );
  }
);

// Thống kê doanh thu học phí
export const fetchTuitionStatistics = createAsyncThunk(
  "tuition/fetchTuitionStatistics",
  async (params, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getTuitionStatisticsAPI,
      params,
      null,
      true,
      false
    );
  }
);

// Lấy thống kê tổng quan về các khoản học phí của một học sinh (admin view)
export const fetchUserTuitionSummaryAdmin = createAsyncThunk(
  "tuition/fetchUserTuitionSummaryAdmin",
  async (userId, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionSummaryAdminAPI,
      userId,
      null,
      true,
      false
    );
  }
);

// Lấy thống kê tổng quan về các khoản học phí của học sinh hiện tại
export const fetchUserTuitionSummary = createAsyncThunk(
  "tuition/fetchUserTuitionSummary",
  async (_, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getUserTuitionSummaryAPI,
      null,
      null,
      false,
      false
    );
  }
);

// Lấy danh sách học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
export const fetchStudentClassTuitionsByMonth = createAsyncThunk(
  "tuition/fetchStudentClassTuitionsByMonth",
  async (month, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getStudentClassTuitionsByMonthAPI,
      month,
      null,
      true,
      false
    );
  }
);

// Admin xem danh sách học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
export const fetchStudentClassTuitionsByMonthAdmin = createAsyncThunk(
  "tuition/fetchStudentClassTuitionsByMonthAdmin",
  async ({ userId, month }, { dispatch }) => {
    return await apiHandler(
      dispatch,
      tuitionApi.getStudentClassTuitionsByMonthAdminAPI,
      {userId, month},
      null,
      true,
      false
    );
  }
);

// Tuition Slice
const tuitionSlice = createSlice({
  name: "tuition",
  initialState: {
    classTuitions: [],
    classTuition: null,
    tuitionPayments: [],
    tuitionPayment: null,
    tuitionStatistics: null,
    userTuitionSummary: null,
    studentClassTuitions: null,
    studentClassTuitionsAdmin: null,
    loading: false,
    error: null,
  },
  reducers: {
    clearClassTuition: (state) => {
      state.classTuition = null;
    },
    clearTuitionPayment: (state) => {
      state.tuitionPayment = null;
    },
    clearTuitionStatistics: (state) => {
      state.tuitionStatistics = null;
    },
    clearUserTuitionSummary: (state) => {
      state.userTuitionSummary = null;
    },
    clearStudentClassTuitions: (state) => {
      state.studentClassTuitions = null;
    },
    clearStudentClassTuitionsAdmin: (state) => {
      state.studentClassTuitionsAdmin = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Class Tuition reducers
      .addCase(fetchClassTuitions.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchClassTuitions.fulfilled, (state, action) => {
        state.loading = false;
        state.classTuitions = action.payload?.data || [];
      })
      .addCase(fetchClassTuitions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchClassTuitionsByClassId.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchClassTuitionsByClassId.fulfilled, (state, action) => {
        state.loading = false;
        state.classTuitions = action.payload?.data || [];
      })
      .addCase(fetchClassTuitionsByClassId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchClassTuitionById.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchClassTuitionById.fulfilled, (state, action) => {
        state.loading = false;
        state.classTuition = action.payload?.data || null;
      })
      .addCase(fetchClassTuitionById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // Tuition Payment reducers
      .addCase(fetchTuitionPayments.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionPayments.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayments = action.payload?.data || [];
      })
      .addCase(fetchTuitionPayments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchClassTuitionPayments.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchClassTuitionPayments.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayments = action.payload?.data || [];
      })
      .addCase(fetchClassTuitionPayments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchUserTuitionPaymentsAdmin.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionPaymentsAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayments = action.payload?.data || [];
      })
      .addCase(fetchUserTuitionPaymentsAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchUserTuitionPayments.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionPayments.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayments = action.payload?.data || [];
      })
      .addCase(fetchUserTuitionPayments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchTuitionPaymentByIdAdmin.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionPaymentByIdAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayment = action.payload?.data || null;
      })
      .addCase(fetchTuitionPaymentByIdAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(fetchTuitionPaymentByIdUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionPaymentByIdUser.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionPayment = action.payload?.data || null;
      })
      .addCase(fetchTuitionPaymentByIdUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(deleteTuitionPayment.fulfilled, (state, action) => {
        // console.log("deleteTuitionPayment", action.payload.data);
        state.tuitionPayments = state.tuitionPayments.filter(
          (payment) => payment.id != action.payload.data
        );
      })
      // Tuition Statistics reducers
      .addCase(fetchTuitionStatistics.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTuitionStatistics.fulfilled, (state, action) => {
        state.loading = false;
        state.tuitionStatistics = action.payload?.data || null;
      })
      .addCase(fetchTuitionStatistics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // User Tuition Summary reducers (admin view)
      .addCase(fetchUserTuitionSummaryAdmin.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionSummaryAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.userTuitionSummary = action.payload?.data || null;
      })
      .addCase(fetchUserTuitionSummaryAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // User Tuition Summary reducers (user view)
      .addCase(fetchUserTuitionSummary.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUserTuitionSummary.fulfilled, (state, action) => {
        state.loading = false;
        state.userTuitionSummary = action.payload?.data || null;
      })
      .addCase(fetchUserTuitionSummary.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // Student Class Tuitions by Month reducers
      .addCase(fetchStudentClassTuitionsByMonth.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchStudentClassTuitionsByMonth.fulfilled, (state, action) => {
        state.loading = false;
        state.studentClassTuitions = action.payload?.data || null;
      })
      .addCase(fetchStudentClassTuitionsByMonth.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // Student Class Tuitions by Month Admin reducers
      .addCase(fetchStudentClassTuitionsByMonthAdmin.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchStudentClassTuitionsByMonthAdmin.fulfilled, (state, action) => {
        state.loading = false;
        state.studentClassTuitionsAdmin = action.payload?.data || null;
      })
      .addCase(fetchStudentClassTuitionsByMonthAdmin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      .addCase(deleteClassTuition.fulfilled, (state, action) => {
        state.classTuitions = state.classTuitions.filter(
          (payment) => payment.id != action.payload.data
        );
      });
  },
});

export const {
  clearClassTuition,
  clearTuitionPayment,
  clearTuitionStatistics,
  clearUserTuitionSummary,
  clearStudentClassTuitions,
  clearStudentClassTuitionsAdmin
} = tuitionSlice.actions;
export default tuitionSlice.reducer;
