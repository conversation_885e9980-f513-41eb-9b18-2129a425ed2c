import UserLayout from "../../../layouts/UserLayout";
import ShowTotalResult from "../../../components/bar/ShowTotalResult";
import ExamCard from "../../../components/card/ExamCard";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { setCurrentPage } from "../../../features/exam/examSlice";
import Pagination from "../../../components/Pagination";
import LoadingSpinner from "../../../components/loading/LoadingSpinner";
import NoDataFound from "../../../assets/images/error-file.png";
import FilterExamTopbar from "../../../components/filter/FilterExamTopbar";
import FilterExamSidebar from "../../../components/filter/FilterExamSidebar";

const PracticePage = () => {
    const { exams, pagination } = useSelector((state) => state.exams);
    const dispatch = useDispatch();
    const { loading } = useSelector((state) => state.states);
    const { pageSize: limit, page: currentPage, sortOrder, total: totalItems } = pagination;
    const { codes } = useSelector((state) => state.codes);

    const handlePageChange = (page) => {
        dispatch(setCurrentPage(page));
    };

    return (
        <UserLayout>
            <div className="bg-white">
                {/* Mobile filter topbar - visible on mobile only */}
                <div className="md:hidden">
                    <FilterExamTopbar />
                </div>

                {/* Main content with sidebar */}
                <div className=" p-4">
                    <div className="flex flex-col md:flex-row gap-6">
                        {/* Sidebar - visible on desktop only */}
                        <div className="hidden md:block w-full md:w-64 lg:w-72 flex-shrink-0">
                            <FilterExamSidebar />
                        </div>

                        {/* Main content area */}
                        <div className="flex-1 p-4">
                            <h1 className="text-2xl font-bold text-zinc-800 mb-4 md:mt-0">Danh sách đề</h1>

                            <div className="min-h-screen">
                                {loading ? (
                                    <div className="flex justify-center items-center w-full h-40">
                                        <LoadingSpinner
                                            type="dots"
                                            color="border-sky-600"
                                            size="4rem"
                                            showText={true}
                                            text="Đang tải danh sách đề thi..."
                                        />
                                    </div>
                                ) : (
                                    <>
                                        <ShowTotalResult />

                                        <div className="border-b border-gray-200 my-4" />

                                        {/* Exam list */}
                                        {(exams.length === 0 || !exams[0]) && (
                                            <div className="flex items-center flex-col justify-center p-4 w-full">
                                                <img src={NoDataFound} alt="No Data Found" className="w-[8rem]" />
                                                <p className="text-gray-500 text-sm sm:text-base md:text-lg">Không có đề nào phù hợp</p>
                                            </div>
                                        )}
                                        <div className="flex flex-col gap-4">
                                            {(exams.length > 0 && exams[0]) && (
                                                exams.map((exam, index) => (
                                                    <ExamCard
                                                        key={index}
                                                        exam={exam}
                                                        codes={codes}
                                                        horizontal={true}
                                                    />
                                                ))
                                            )}
                                        </div>

                                        {/* Pagination */}
                                        {(exams.length > 0 && exams[0]) && (
                                            <div className="flex justify-center mt-8">
                                                <Pagination
                                                    currentPage={currentPage}
                                                    totalItems={totalItems}
                                                    limit={10}
                                                    onPageChange={handlePageChange}
                                                />
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </UserLayout>
    );
};

export default PracticePage;
