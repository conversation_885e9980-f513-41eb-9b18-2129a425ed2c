{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\DoExamPage.jsx\",\n  _s = $RefreshSig$();\nimport HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useState, useEffect, useRef, useCallback } from \"react\";\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\nimport { useParams } from \"react-router-dom\";\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\nimport { AnimatePresence } from \"framer-motion\";\nimport { Menu } from \"lucide-react\";\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\nimport ExamContent from \"../../../components/questions/ExamContent\";\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\nimport { setRemainingTime, summitExam, setSaveQuestions, setErrorQuestions, getRemainingTime, logUserActivity, submitAnswerWithAttempt, leaveExam, joinExam } from \"../../../features/doExam/doExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoExamPage = () => {\n  _s();\n  var _examContentRef$curre;\n  const {\n    examId\n  } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    exam\n  } = useSelector(state => state.exams);\n  const {\n    questions\n  } = useSelector(state => state.questions);\n  const {\n    answers\n  } = useSelector(state => state.answers);\n  const [fontSize, setFontSize] = useState(14); // 14px mặc định\n  const [imageSize, setImageSize] = useState(12); // đơn vị: rem\n  const questionRefs = useRef([]);\n  const [isAgree, setIsAgree] = useState(false);\n  const [attemptId, setAttemptId] = useState(null);\n  const attemptRef = useRef(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [flag, setFlag] = useState(false);\n  const [startTime1, setStartTime1] = useState(null);\n  const hasSubmittedRef = useRef(false);\n  const examRef = useRef(null);\n  const examContentRef = useRef(null);\n  useEffect(() => {\n    examRef.current = exam;\n    if ((exam === null || exam === void 0 ? void 0 : exam.acceptDoExam) === false) {\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  }, [exam]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchPublicExamById(examId));\n    }\n  }, [dispatch, examId]);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    remainingTime,\n    saveQuestions,\n    errorQuestions,\n    loadingJoin\n  } = useSelector(state => state.doExam);\n  const [markedQuestions, setMarkedQuestions] = useState(new Set());\n  const [timeWarningShown, setTimeWarningShown] = useState({\n    fiveMinutes: false,\n    oneMinute: false\n  });\n  const [isTimeBlinking, setIsTimeBlinking] = useState(false);\n  const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    const saved = localStorage.getItem(\"isDarkMode\");\n    return saved ? JSON.parse(saved) : false;\n  });\n  const [loadingSubmit, setLoadingSubmit] = useState(false);\n  const [isTimeUp, setIsTimeUp] = useState(false);\n  const [questionTN, setQuestionTN] = useState([]);\n  const [questionDS, setQuestionDS] = useState([]);\n  const [questionTLN, setQuestionTLN] = useState([]);\n  const [answerTN, setAnswerTN] = useState([]);\n  const [answerTLN, setAnswerTLN] = useState([]);\n  const [dsAnswers, setDsAnswers] = useState({});\n  document.addEventListener(\"copy\", e => {\n    e.preventDefault();\n  });\n  const addQuestion = questionId => {\n    if (!saveQuestions.includes(questionId)) {\n      dispatch(setSaveQuestions([...saveQuestions, questionId]));\n    }\n    removeErrorQuestion(questionId);\n  };\n  const addErrorQuestion = questionId => {\n    if (!errorQuestions.includes(questionId)) {\n      dispatch(setErrorQuestions([...errorQuestions, questionId]));\n    }\n    removeQuestion(questionId);\n  };\n  const removeQuestion = questionId => {\n    dispatch(setSaveQuestions(saveQuestions.filter(id => id !== questionId)));\n  };\n  const removeErrorQuestion = questionId => {\n    dispatch(setErrorQuestions(errorQuestions.filter(id => id !== questionId)));\n  };\n\n  // Hàm đánh dấu câu hỏi để xem lại sau\n  const toggleMarkQuestion = questionId => {\n    setMarkedQuestions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(questionId)) {\n        newSet.delete(questionId);\n      } else {\n        newSet.add(questionId);\n      }\n      return newSet;\n    });\n  };\n  const handleExitFullscreen = () => {\n    try {\n      exitFullscreen();\n    } catch (err) {\n      // Chỉ ghi log lỗi, không bắt lỗi\n      console.warn(\"Không thể thoát fullscreen:\", err);\n    }\n  };\n  const handleFontSizeChange = e => {\n    setFontSize(Number(e.target.value));\n  };\n  const handleImageSizeChange = e => {\n    setImageSize(Number(e.target.value));\n  };\n  const formatTime = seconds => {\n    const min = String(Math.floor(seconds / 60)).padStart(2, '0');\n    const sec = String(seconds % 60).padStart(2, '0');\n    return \"\".concat(min, \":\").concat(sec);\n  };\n  const handleFullScreen = async () => {\n    try {\n      // Sử dụng joinExam action thay vì fetch\n      const result = await dispatch(joinExam(examId)).unwrap();\n\n      // Xử lý khi join exam thành công\n      const {\n        attemptId,\n        startTime\n      } = result;\n      console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\n      setIsAgree(true);\n      attemptRef.current = attemptId;\n      setAttemptId(attemptId);\n      if (examId) {\n        dispatch(fetchPublicQuestionsByExamId(examId));\n      }\n      setStartTime1(startTime);\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) {\n        return;\n      }\n      try {\n        const success = await requestFullscreen();\n        if (!success) {\n          console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\n        }\n      } catch (err) {\n        console.error(\"❌ Lỗi khi bật fullscreen:\", err);\n        alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\n      }\n    } catch (error) {\n      console.error(\"Lỗi khi tham gia bài thi:\", error);\n      dispatch(setErrorMessage(\"Lỗi: \" + error.message));\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  };\n\n  // Removed socket-based exam_started listener - now handled in handleFullScreen\n\n  useEffect(() => {\n    if (exam !== null && exam !== void 0 && exam.testDuration && startTime1) {\n      const start = new Date(startTime1);\n      const now = new Date();\n      const elapsedSeconds = Math.floor((now - start) / 1000);\n      const totalSeconds = exam.testDuration * 60;\n      const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\n      dispatch(setRemainingTime(remaining));\n\n      // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\n      if (attemptId) {\n        dispatch(getRemainingTime({\n          examId,\n          attemptId\n        })).then(result => {\n          var _result$payload;\n          if (((_result$payload = result.payload) === null || _result$payload === void 0 ? void 0 : _result$payload.remainingTime) !== undefined) {\n            dispatch(setRemainingTime(result.payload.remainingTime));\n          }\n        }).catch(error => {\n          console.error(\"Lỗi khi lấy thời gian từ server:\", error);\n        });\n      }\n    }\n  }, [startTime1, exam, attemptId, examId, dispatch]);\n  useEffect(() => {\n    if (flag) return;\n    if (!remainingTime) setFlag(true);\n  }, [remainingTime]);\n  const handleAutoSubmit = async () => {\n    if (hasSubmittedRef.current) {\n      console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\n      return;\n    }\n    hasSubmittedRef.current = true; // Đánh dấu đã submit\n    console.log(\"Kiểm tra attemptId:\", attemptId);\n    if (!attemptId) {\n      console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\n      return;\n    }\n    console.log(\"Đang nộp bài với attemptId:\", attemptId);\n    dispatch(setSaveQuestions([]));\n    setLoadingSubmit(true);\n    try {\n      // Sử dụng API thay vì socket để nộp bài\n      const result = await dispatch(summitExam(attemptId)).unwrap();\n      console.log(\"Nộp bài thành công:\", result);\n\n      // Xử lý khi nộp bài thành công\n      dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\n\n      // Thoát fullscreen mà không bắt lỗi\n      try {\n        exitFullscreen();\n      } catch (err) {\n        // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\n        console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\n      }\n      const safeAttemptId = attemptRef.current;\n      const currentExam = examRef.current;\n      if (!safeAttemptId) {\n        console.error(\"Không có attemptId khi navigate!\");\n        return;\n      }\n\n      // Log để debug\n      console.log(\"Current exam state:\", currentExam);\n      console.log(\"Attempt ID:\", safeAttemptId);\n      if (!currentExam || !currentExam.seeCorrectAnswer) {\n        console.log(\"Chuyển về trang danh sách do:\", {\n          examNull: !currentExam,\n          cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\n        });\n        navigate(\"/practice/exam/\".concat(examId));\n        return;\n      }\n      navigate(\"/practice/exam/attempt/\".concat(safeAttemptId, \"/score\"));\n    } catch (error) {\n      console.error(\"Lỗi khi nộp bài:\", error);\n      setLoadingSubmit(false);\n      dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\n      hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\n\n      // Thử nộp lại sau 3 giây nếu lỗi xảy ra\n      setTimeout(() => {\n        if (!loadingSubmit && attemptRef.current) {\n          console.log(\"Thử nộp bài lại sau lỗi...\");\n          handleAutoSubmit();\n        }\n      }, 5000);\n    }\n  };\n\n  // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\n  const navigateToQuestion = useCallback(questionId => {\n    setSelectedQuestion(questionId);\n\n    // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\n    if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\n      // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\n      examContentRef.current.goToQuestionById(questionId);\n    } else {\n      // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\n      // Tìm phần tử câu hỏi bằng querySelector\n      setTimeout(() => {\n        // Thử tìm phần tử bằng data-question-id\n        const element = document.querySelector(\"[data-question-id=\\\"\".concat(questionId, \"\\\"]\"));\n        if (element) {\n          const offset = 80; // chiều cao của header sticky\n          const y = element.getBoundingClientRect().top + window.scrollY - offset;\n          window.scrollTo({\n            top: y,\n            behavior: \"smooth\"\n          });\n        } else {\n          // Fallback: Sử dụng refs\n          const refElement = questionRefs.current[questionId];\n          if (refElement) {\n            const offset = 80; // chiều cao của header sticky\n            const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\n            window.scrollTo({\n              top: y,\n              behavior: \"smooth\"\n            });\n          }\n        }\n      }, 0);\n    }\n  }, [questionRefs, examContentRef]);\n\n  // navigateToQuestion is used directly in components\n\n  const handleSelectAnswerTN = (questionId, statementId, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const newAnswer = {\n      questionId,\n      answerContent: statementId,\n      typeOfQuestion: type\n    };\n    dispatch(setAnswers(newAnswer));\n\n    // Sử dụng API thay vì socket\n    dispatch(submitAnswerWithAttempt({\n      questionId,\n      answerContent: statementId,\n      type,\n      attemptId\n    })).then(result => {\n      if (result.type.endsWith('/fulfilled')) {\n        // Answer submitted successfully\n        console.log(\"Đã lưu câu trả lời thành công\");\n      } else {\n        // Handle error\n        console.error(\"Lỗi khi lưu câu trả lời:\", result.error);\n      }\n    });\n  };\n  const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const currentAnswers = dsAnswers[questionId] || [];\n    const existing = currentAnswers.find(ans => ans.statementId === statementId);\n\n    // 🔁 Nếu đáp án đã giống thì không gửi lại\n    if (existing && existing.answer === selectedAnswer) {\n      return;\n    }\n    const updatedAnswers = currentAnswers.map(ans => ans.statementId === statementId ? {\n      ...ans,\n      answer: selectedAnswer\n    } : ans);\n\n    // Nếu chưa có statement này\n    if (!existing) {\n      updatedAnswers.push({\n        statementId,\n        answer: selectedAnswer\n      });\n    }\n    dispatch(setAnswers({\n      questionId,\n      answerContent: JSON.stringify(updatedAnswers),\n      typeOfQuestion: \"DS\"\n    }));\n\n    // Sử dụng API thay vì socket\n    dispatch(submitAnswerWithAttempt({\n      questionId,\n      answerContent: updatedAnswers,\n      type: \"DS\",\n      attemptId\n    })).then(result => {\n      if (result.type.endsWith('/fulfilled')) {\n        console.log(\"Đã lưu câu trả lời DS thành công\");\n      } else {\n        console.error(\"Lỗi khi lưu câu trả lời DS:\", result.error);\n      }\n    });\n  };\n  const handleSelectAnswerTLN = (questionId, answerContent, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    if (!answerContent || answerContent.trim() === \"\") {\n      return;\n    }\n    const formattedAnswer = answerContent.trim().replace(\",\", \".\");\n    dispatch(setAnswers({\n      questionId,\n      answerContent,\n      typeOfQuestion: type\n    }));\n\n    // Sử dụng API thay vì socket\n    dispatch(submitAnswerWithAttempt({\n      questionId,\n      answerContent: formattedAnswer,\n      type,\n      attemptId\n    })).then(result => {\n      if (result.type.endsWith('/fulfilled')) {\n        console.log(\"Đã lưu câu trả lời TLN thành công\");\n      } else {\n        console.error(\"Lỗi khi lưu câu trả lời TLN:\", result.error);\n      }\n    });\n  };\n\n  // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\n  const questionsToMarkAsSaved = useRef(new Set());\n\n  // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\n  useEffect(() => {\n    if (questionsToMarkAsSaved.current.size > 0) {\n      questionsToMarkAsSaved.current.forEach(questionId => {\n        if (!saveQuestions.includes(questionId)) {\n          addQuestion(questionId);\n        }\n      });\n      questionsToMarkAsSaved.current.clear();\n    }\n  }, [saveQuestions, addQuestion]);\n\n  // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\n  useEffect(() => {\n    // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\n    const frameId = requestAnimationFrame(() => {\n      if (questionsToMarkAsSaved.current.size > 0) {\n        const questionIds = [...questionsToMarkAsSaved.current];\n        questionsToMarkAsSaved.current.clear();\n\n        // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\n        questionIds.forEach(questionId => {\n          if (!saveQuestions.includes(questionId)) {\n            addQuestion(questionId);\n          }\n        });\n      }\n    });\n    return () => cancelAnimationFrame(frameId);\n  });\n  const isTNSelected = useCallback((questionId, statementId) => {\n    const isSelected = answerTN.some(ans => ans.questionId === questionId && ans.answerContent && String(ans.answerContent) === String(statementId));\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.includes(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [answerTN, saveQuestions]);\n  const isDSChecked = useCallback((questionId, statementId, bool) => {\n    var _dsAnswers$questionId, _dsAnswers$questionId2;\n    const isSelected = ((_dsAnswers$questionId = dsAnswers[questionId]) === null || _dsAnswers$questionId === void 0 ? void 0 : _dsAnswers$questionId.some(a => a.statementId === statementId && a.answer === bool)) || false;\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.includes(questionId) && ((_dsAnswers$questionId2 = dsAnswers[questionId]) === null || _dsAnswers$questionId2 === void 0 ? void 0 : _dsAnswers$questionId2.length) === 4) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [dsAnswers, saveQuestions]);\n  const getTLNDefaultValue = useCallback(questionId => {\n    var _matched$answerConten;\n    const matched = answerTLN.find(ans => ans.questionId === questionId);\n    const content = (matched === null || matched === void 0 ? void 0 : (_matched$answerConten = matched.answerContent) === null || _matched$answerConten === void 0 ? void 0 : _matched$answerConten.replace(/^\"|\"$/g, \"\")) || \"\";\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (content && !saveQuestions.includes(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return content;\n  }, [answerTLN, saveQuestions]);\n\n  // useEffect(() => {\n  //     if (examId) {\n  //         dispatch(fetchPublicQuestionsByExamId(examId));\n  //     }\n  // }, [dispatch, examId]);\n\n  useEffect(() => {\n    if (questions) {\n      setQuestionTN(questions.filter(question => question.typeOfQuestion === \"TN\"));\n      setQuestionDS(questions.filter(question => question.typeOfQuestion === \"DS\"));\n      setQuestionTLN(questions.filter(question => question.typeOfQuestion === \"TLN\"));\n    }\n  }, [questions]);\n  useEffect(() => {\n    // Kiểm tra answers có phải là mảng không\n    if (!Array.isArray(answers) || answers.length === 0) return;\n    const tn = [];\n    const tln = [];\n    const dsMap = {};\n\n    // Sử dụng for...of thay vì forEach để tránh lỗi\n    for (const answer of answers) {\n      if (answer.typeOfQuestion === \"TN\") {\n        tn.push(answer);\n      } else if (answer.typeOfQuestion === \"TLN\") {\n        tln.push(answer);\n      } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\n        try {\n          const parsed = JSON.parse(answer.answerContent);\n          dsMap[answer.questionId] = parsed;\n        } catch (err) {\n          console.error(\"Lỗi parse DS answerContent:\", err);\n        }\n      }\n    }\n    setAnswerTN(tn);\n    setAnswerTLN(tln);\n    setDsAnswers(dsMap);\n\n    // Note: Score calculation is now handled when submitting exam\n    // No need to calculate score in real-time\n  }, [answers]);\n  useEffect(() => {\n    if (attemptId) {\n      dispatch(fetchAnswersByAttempt(attemptId));\n    }\n  }, [dispatch, attemptId]);\n  useEffect(() => {\n    if (!(exam !== null && exam !== void 0 && exam.testDuration) || remainingTime === null || !isAgree) return;\n\n    // Kiểm tra và hiển thị cảnh báo thời gian\n    const checkTimeWarnings = time => {\n      // Cảnh báo khi còn 5 phút\n      if (time === 300 && !timeWarningShown.fiveMinutes) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          fiveMinutes: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\n\n        // Tắt hiệu ứng nhấp nháy sau 10 giây\n        setTimeout(() => {\n          setIsTimeBlinking(false);\n        }, 10000);\n      }\n\n      // Cảnh báo khi còn 1 phút\n      if (time === 60 && !timeWarningShown.oneMinute) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          oneMinute: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\n\n        // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\n      }\n    };\n\n    // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API\n    const syncTimeInterval = setInterval(() => {\n      if (attemptId) {\n        dispatch(getRemainingTime({\n          examId,\n          attemptId\n        })).then(result => {\n          var _result$payload2;\n          if (((_result$payload2 = result.payload) === null || _result$payload2 === void 0 ? void 0 : _result$payload2.remainingTime) !== undefined) {\n            dispatch(setRemainingTime(result.payload.remainingTime));\n          }\n        }).catch(error => {\n          console.error(\"Lỗi khi đồng bộ thời gian:\", error);\n        });\n      }\n    }, 30000); // Đồng bộ thời gian mỗi 30 giây\n\n    const interval = setInterval(() => {\n      dispatch(setRemainingTime(prev => {\n        if (prev <= 1) {\n          // dùng <=1 để đảm bảo không bị âm\n          clearInterval(interval);\n          clearInterval(syncTimeInterval);\n          // Đánh dấu là đã hết thời gian\n          setIsTimeUp(true);\n          setIsTimeBlinking(false);\n          // Thử nộp bài\n          handleAutoSubmit();\n          return 0;\n        }\n\n        // Kiểm tra cảnh báo thời gian\n        checkTimeWarnings(prev);\n        return prev - 1;\n      }));\n    }, 1000);\n    return () => {\n      clearInterval(interval);\n      clearInterval(syncTimeInterval);\n    };\n  }, [exam === null || exam === void 0 ? void 0 : exam.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId]); // Chỉ phụ thuộc vào các giá trị cần thiết\n\n  // Removed socket connection management - using API only\n\n  // frontend\n  useEffect(() => {\n    if (!attemptId || !(user !== null && user !== void 0 && user.id) || !examId || attemptId === null || attemptId === undefined) return;\n    if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) return;\n    console.log(\"Đã bật theo dõi hành vi gian lận\");\n    const recentLogs = new Set(); // chống log lặp\n    const logOnce = (key, payload) => {\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled) || recentLogs.has(key)) return;\n      recentLogs.add(key);\n\n      // Sử dụng API thay vì socket\n      dispatch(logUserActivity({\n        examId,\n        attemptId,\n        activityType: payload.type || 'user_activity',\n        details: {\n          ...payload,\n          name: user.lastName + \" \" + user.firstName\n        }\n      }));\n      setTimeout(() => recentLogs.delete(key), 5000);\n    };\n\n    // 📌 Thoát fullscreen\n    const handleFullscreenChange = () => {\n      if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {\n        logOnce(\"exit_fullscreen\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"EF\",\n          action: \"exit_fullscreen\",\n          detail: JSON.stringify({\n            reason: \"User exited fullscreen mode\"\n          })\n        });\n      }\n    };\n\n    // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === \"hidden\") {\n        logOnce(\"tab_blur\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"TB\",\n          action: \"tab_blur\",\n          detail: JSON.stringify({\n            message: \"User switched tab or minimized window\"\n          })\n        });\n      }\n    };\n\n    // 📌 Copy nội dung\n    const handleCopy = () => {\n      logOnce(\"copy_detected\", {\n        studentId: user.id,\n        attemptId,\n        examId,\n        code: \"COP\",\n        action: \"copy_detected\",\n        detail: JSON.stringify({\n          message: \"User copied content\"\n        })\n      });\n    };\n\n    // 📌 Phím đáng ngờ\n    const handleSuspiciousKey = e => {\n      const suspiciousKeys = [\"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"];\n      const combo = \"\".concat(e.ctrlKey ? \"Ctrl+\" : \"\").concat(e.shiftKey ? \"Shift+\" : \"\").concat(e.altKey ? \"Alt+\" : \"\").concat(e.metaKey ? \"Meta+\" : \"\").concat(e.key);\n      if (suspiciousKeys.includes(e.key) || combo === \"Ctrl+Shift+I\" || combo === \"Ctrl+Shift+C\") {\n        logOnce(\"key_\".concat(combo), {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"SK\",\n          action: \"suspicious_key\",\n          detail: JSON.stringify({\n            key: e.key,\n            code: e.code,\n            combo\n          })\n        });\n      }\n    };\n    document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n    document.addEventListener(\"copy\", handleCopy);\n    document.addEventListener(\"keydown\", handleSuspiciousKey);\n    return () => {\n      document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      document.removeEventListener(\"copy\", handleCopy);\n      document.removeEventListener(\"keydown\", handleSuspiciousKey);\n    };\n  }, [user.id, examId, attemptId]);\n  useEffect(() => {\n    // Removed all socket event listeners - using API responses instead\n    // Answer save/error status is now handled in submitAnswerWithAttempt action responses\n    // Timer updates are handled via getRemainingTime API calls\n    // Auto-submit is handled via client-side timer logic\n  }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\n  useEffect(() => {\n    localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  // Hàm xử lý chuyển đổi câu hỏi\n  const handleKeyDown = useCallback(e => {\n    // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\n    if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\n      // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\n      e.preventDefault();\n\n      // Nếu không có câu hỏi, thoát khỏi hàm\n      if (!questions || questions.length === 0) return;\n      const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\n      const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\n      if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\n        const prevQuestionId = allQuestions[currentIndex - 1].id;\n        console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\n        navigateToQuestion(prevQuestionId);\n      } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\n        const nextQuestionId = allQuestions[currentIndex + 1].id;\n        console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\n        navigateToQuestion(nextQuestionId);\n      }\n    }\n  }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\n  // Lắng nghe sự kiện bàn phím\n  useEffect(() => {\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [handleKeyDown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full \".concat(isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'),\n    children: [/*#__PURE__*/_jsxDEV(HeaderDoExamPage, {\n      nameExam: exam === null || exam === void 0 ? void 0 : exam.name,\n      onExitFullscreen: handleExitFullscreen,\n      isDarkMode: !isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 13\n    }, this), isAgree ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\",\n      children: [/*#__PURE__*/_jsxDEV(ExamContent, {\n        ref: examContentRef,\n        loading1: loadingJoin,\n        isDarkMode: isDarkMode,\n        questionTN: questionTN,\n        questionDS: questionDS,\n        questionTLN: questionTLN,\n        handlers: {\n          handleSelectAnswerTN,\n          handleSelectAnswerDS,\n          handleSelectAnswerTLN,\n          isTNSelected,\n          isDSChecked,\n          getTLNDefaultValue,\n          setQuestionRef: (id, el) => questionRefs.current[id] = el,\n          setSelectedQuestion: id => setSelectedQuestion(id)\n        },\n        settings: {\n          selectedQuestion,\n          isDarkMode,\n          fontSize,\n          imageSize,\n          prefixStatementTN,\n          prefixStatementDS,\n          isTimeUp,\n          markedQuestions,\n          toggleMarkQuestion\n        },\n        isTimeUp: isTimeUp\n        // Để undefined để component tự quyết định dựa trên thiết bị\n        ,\n        initialSingleMode: undefined,\n        handleAutoSubmit: handleAutoSubmit,\n        loadingSubmit: loadingSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 791,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-full shadow-md \".concat(isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"),\n          onClick: () => setIsSidebarOpen(prev => !prev),\n          children: /*#__PURE__*/_jsxDEV(Menu, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 828,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: (isSidebarOpen || window.innerWidth > 1024) && /*#__PURE__*/_jsxDEV(ExamSidebar, {\n          isDarkMode: isDarkMode,\n          setIsDarkMode: setIsDarkMode,\n          fontSize: fontSize,\n          handleFontSizeChange: handleFontSizeChange,\n          imageSize: imageSize,\n          handleImageSizeChange: handleImageSizeChange,\n          questionTN: questionTN,\n          questionDS: questionDS,\n          questionTLN: questionTLN,\n          scrollToQuestion: navigateToQuestion,\n          selectedQuestion: selectedQuestion,\n          markedQuestions: markedQuestions,\n          toggleMarkQuestion: toggleMarkQuestion,\n          handleAutoSubmit: handleAutoSubmit,\n          loadingSubmit: loadingSubmit,\n          loadingLoadExam: loadingJoin,\n          exam: exam,\n          remainingTime: remainingTime,\n          formatTime: formatTime,\n          questions: questions,\n          singleQuestionMode: ((_examContentRef$curre = examContentRef.current) === null || _examContentRef$curre === void 0 ? void 0 : _examContentRef$curre.isSingleQuestionMode()) || false,\n          setSingleQuestionMode: value => {\n            if (examContentRef.current) {\n              // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\n              examContentRef.current.setSingleQuestionMode(value);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 789,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(ExamRegulationModal, {\n        onClose: () => {\n          // Sử dụng API để leave exam nếu có attemptId\n          if (attemptId) {\n            dispatch(leaveExam({\n              examId,\n              attemptId\n            }));\n          }\n          navigate(\"/practice/exam/\".concat(examId));\n        },\n        isOpen: !isAgree,\n        onStartExam: handleFullScreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 875,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 874,\n      columnNumber: 17\n    }, this), (exam === null || exam === void 0 ? void 0 : exam.testDuration) && isAgree && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-2 rounded-md left-2 px-4 py-2\\n                    \".concat(isTimeBlinking ? 'bg-red-600 animate-pulse' : 'bg-slate-700 bg-opacity-80', \"\\n                    text-white z-50 transition-colors duration-300\"),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-bold\",\n          children: [formatTime(remainingTime), \" ph\\xFAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 890,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 786,\n    columnNumber: 9\n  }, this);\n};\n_s(DoExamPage, \"Mvq3tIQcWFuAChBBaErCD2aR1xg=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DoExamPage;\nexport default DoExamPage;\nvar _c;\n$RefreshReg$(_c, \"DoExamPage\");", "map": {"version": 3, "names": ["HeaderDoExamPage", "useDispatch", "useSelector", "useState", "useEffect", "useRef", "useCallback", "fetchPublicQuestionsByExamId", "fetchPublicExamById", "useParams", "setErrorMessage", "setSuccessMessage", "useNavigate", "fetchAnswersByAttempt", "setAnswers", "ExamRegulationModal", "AnimatePresence", "<PERSON><PERSON>", "ExamSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestFullscreen", "exitFullscreen", "isFullscreen", "setRemainingTime", "summitExam", "setSaveQuestions", "setErrorQuestions", "getRemainingTime", "logUserActivity", "submitAnswerWithAttempt", "leaveExam", "joinExam", "jsxDEV", "_jsxDEV", "DoExamPage", "_s", "_examContentRef$curre", "examId", "dispatch", "navigate", "exam", "state", "exams", "questions", "answers", "fontSize", "setFontSize", "imageSize", "setImageSize", "questionRefs", "isAgree", "setIsAgree", "attemptId", "setAttemptId", "attemptRef", "isSidebarOpen", "setIsSidebarOpen", "flag", "setFlag", "startTime1", "setStartTime1", "hasSubmittedRef", "examRef", "examContentRef", "current", "acceptDoExam", "concat", "user", "auth", "remainingTime", "saveQuestions", "errorQuestions", "loadingJoin", "doExam", "markedQuestions", "setMarkedQuestions", "Set", "timeWarningShown", "setTimeWarningShown", "fiveMinutes", "oneMinute", "isTimeBlinking", "setIsTimeBlinking", "prefixStatementTN", "prefixStatementDS", "selectedQuestion", "setSelectedQuestion", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "loadingSubmit", "setLoadingSubmit", "isTimeUp", "setIsTimeUp", "questionTN", "setQuestionTN", "questionDS", "setQuestionDS", "questionTLN", "setQuestionTLN", "answerTN", "setAnswerTN", "answerTLN", "setAnswerTLN", "dsAnswers", "setDsAnswers", "document", "addEventListener", "e", "preventDefault", "addQuestion", "questionId", "includes", "removeErrorQuestion", "addErrorQuestion", "removeQuestion", "filter", "id", "toggleMarkQuestion", "prev", "newSet", "has", "delete", "add", "handleExitFullscreen", "err", "console", "warn", "handleFontSizeChange", "Number", "target", "value", "handleImageSizeChange", "formatTime", "seconds", "min", "String", "Math", "floor", "padStart", "sec", "handleFullScreen", "result", "unwrap", "startTime", "log", "isCheatingCheckEnabled", "success", "error", "alert", "message", "testDuration", "start", "Date", "now", "elapsedSeconds", "totalSeconds", "remaining", "max", "then", "_result$payload", "payload", "undefined", "catch", "handleAutoSubmit", "safeAttemptId", "currentExam", "seeCorrectAnswer", "examNull", "cantSeeAnswer", "setTimeout", "navigateToQuestion", "isSingleQuestionMode", "goToQuestionById", "element", "querySelector", "offset", "y", "getBoundingClientRect", "top", "window", "scrollY", "scrollTo", "behavior", "refElement", "handleSelectAnswerTN", "statementId", "type", "newAnswer", "answerContent", "typeOfQuestion", "endsWith", "handleSelectAnswerDS", "<PERSON><PERSON><PERSON><PERSON>", "currentAnswers", "existing", "find", "ans", "answer", "updatedAnswers", "map", "push", "stringify", "handleSelectAnswerTLN", "trim", "formattedAnswer", "replace", "questionsToMarkAsSaved", "size", "for<PERSON>ach", "clear", "frameId", "requestAnimationFrame", "questionIds", "cancelAnimationFrame", "isTNSelected", "isSelected", "some", "isDSChecked", "bool", "_dsAnswers$questionId", "_dsAnswers$questionId2", "a", "length", "getTLNDefaultValue", "_matched$answerConten", "matched", "content", "question", "Array", "isArray", "tn", "tln", "dsMap", "parsed", "checkTimeWarnings", "time", "syncTimeInterval", "setInterval", "_result$payload2", "interval", "clearInterval", "recentLogs", "logOnce", "key", "activityType", "details", "name", "lastName", "firstName", "handleFullscreenChange", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "studentId", "code", "action", "detail", "reason", "handleVisibilityChange", "visibilityState", "handleCopy", "handleSuspiciousKey", "<PERSON><PERSON><PERSON><PERSON>", "combo", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "removeEventListener", "setItem", "handleKeyDown", "allQuestions", "currentIndex", "findIndex", "q", "prevQuestionId", "nextQuestionId", "className", "children", "nameExam", "onExitFullscreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "loading1", "handlers", "setQuestionRef", "el", "settings", "initialSingleMode", "onClick", "innerWidth", "scrollToQuestion", "loadingLoadExam", "singleQuestionMode", "setSingleQuestionMode", "onClose", "isOpen", "onStartExam", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/DoExamPage.jsx"], "sourcesContent": ["import HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\r\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\r\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\r\nimport { AnimatePresence } from \"framer-motion\";\r\nimport { Menu } from \"lucide-react\";\r\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\r\nimport ExamContent from \"../../../components/questions/ExamContent\";\r\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\r\nimport {\r\n    setRemainingTime,\r\n    summitExam,\r\n    setSaveQuestions,\r\n    setErrorQuestions,\r\n    getRemainingTime,\r\n    logUserActivity,\r\n    submitAnswerWithAttempt,\r\n    leaveExam,\r\n    joinExam,\r\n} from \"../../../features/doExam/doExamSlice\";\r\n\r\nconst DoExamPage = () => {\r\n    const { examId } = useParams();\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { exam } = useSelector(state => state.exams);\r\n    const { questions } = useSelector(state => state.questions);\r\n    const { answers } = useSelector(state => state.answers);\r\n    const [fontSize, setFontSize] = useState(14); // 14px mặc định\r\n    const [imageSize, setImageSize] = useState(12); // đơn vị: rem\r\n    const questionRefs = useRef([]);\r\n    const [isAgree, setIsAgree] = useState(false);\r\n    const [attemptId, setAttemptId] = useState(null);\r\n    const attemptRef = useRef(null);\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const [flag, setFlag] = useState(false);\r\n    const [startTime1, setStartTime1] = useState(null);\r\n    const hasSubmittedRef = useRef(false);\r\n    const examRef = useRef(null);\r\n    const examContentRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        examRef.current = exam;\r\n        if (exam?.acceptDoExam === false) {\r\n            navigate(`/practice/exam/${examId}`)\r\n        }\r\n    }, [exam]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchPublicExamById(examId));\r\n        }\r\n    }, [dispatch, examId]);\r\n\r\n\r\n    const { user } = useSelector((state) => state.auth);\r\n    const { remainingTime, saveQuestions, errorQuestions, loadingJoin } = useSelector((state) => state.doExam);\r\n\r\n\r\n    const [markedQuestions, setMarkedQuestions] = useState(new Set());\r\n    const [timeWarningShown, setTimeWarningShown] = useState({\r\n        fiveMinutes: false,\r\n        oneMinute: false\r\n    });\r\n    const [isTimeBlinking, setIsTimeBlinking] = useState(false);\r\n\r\n    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [isDarkMode, setIsDarkMode] = useState(() => {\r\n        const saved = localStorage.getItem(\"isDarkMode\");\r\n        return saved ? JSON.parse(saved) : false;\r\n    });\r\n\r\n    const [loadingSubmit, setLoadingSubmit] = useState(false);\r\n    const [isTimeUp, setIsTimeUp] = useState(false);\r\n\r\n    const [questionTN, setQuestionTN] = useState([]);\r\n    const [questionDS, setQuestionDS] = useState([]);\r\n    const [questionTLN, setQuestionTLN] = useState([]);\r\n\r\n    const [answerTN, setAnswerTN] = useState([]);\r\n    const [answerTLN, setAnswerTLN] = useState([]);\r\n    const [dsAnswers, setDsAnswers] = useState({});\r\n\r\n    document.addEventListener(\"copy\", (e) => {\r\n        e.preventDefault();\r\n    });\r\n\r\n    const addQuestion = (questionId) => {\r\n        if (!saveQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        removeErrorQuestion(questionId);\r\n    };\r\n\r\n    const addErrorQuestion = (questionId) => {\r\n        if (!errorQuestions.includes(questionId)) {\r\n            dispatch(setErrorQuestions([...errorQuestions, questionId]));\r\n        }\r\n        removeQuestion(questionId);\r\n    };\r\n\r\n    const removeQuestion = (questionId) => {\r\n        dispatch(setSaveQuestions(saveQuestions.filter(id => id !== questionId)));\r\n    };\r\n\r\n    const removeErrorQuestion = (questionId) => {\r\n        dispatch(setErrorQuestions(errorQuestions.filter(id => id !== questionId)));\r\n    };\r\n\r\n    // Hàm đánh dấu câu hỏi để xem lại sau\r\n    const toggleMarkQuestion = (questionId) => {\r\n        setMarkedQuestions(prev => {\r\n            const newSet = new Set(prev);\r\n            if (newSet.has(questionId)) {\r\n                newSet.delete(questionId);\r\n            } else {\r\n                newSet.add(questionId);\r\n            }\r\n            return newSet;\r\n        });\r\n    };\r\n\r\n\r\n    const handleExitFullscreen = () => {\r\n        try {\r\n            exitFullscreen();\r\n        } catch (err) {\r\n            // Chỉ ghi log lỗi, không bắt lỗi\r\n            console.warn(\"Không thể thoát fullscreen:\", err);\r\n        }\r\n    };\r\n\r\n    const handleFontSizeChange = (e) => {\r\n        setFontSize(Number(e.target.value));\r\n    };\r\n\r\n    const handleImageSizeChange = (e) => {\r\n        setImageSize(Number(e.target.value));\r\n    };\r\n\r\n    const formatTime = (seconds) => {\r\n        const min = String(Math.floor(seconds / 60)).padStart(2, '0');\r\n        const sec = String(seconds % 60).padStart(2, '0');\r\n        return `${min}:${sec}`;\r\n    };\r\n\r\n    const handleFullScreen = async () => {\r\n        try {\r\n            // Sử dụng joinExam action thay vì fetch\r\n            const result = await dispatch(joinExam(examId)).unwrap();\r\n\r\n            // Xử lý khi join exam thành công\r\n            const { attemptId, startTime } = result;\r\n            console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\r\n\r\n            setIsAgree(true);\r\n            attemptRef.current = attemptId;\r\n            setAttemptId(attemptId);\r\n\r\n            if (examId) {\r\n                dispatch(fetchPublicQuestionsByExamId(examId));\r\n            }\r\n            setStartTime1(startTime);\r\n\r\n            if (!exam?.isCheatingCheckEnabled) {\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const success = await requestFullscreen();\r\n                if (!success) {\r\n                    console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\r\n                }\r\n            } catch (err) {\r\n                console.error(\"❌ Lỗi khi bật fullscreen:\", err);\r\n                alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi tham gia bài thi:\", error);\r\n            dispatch(setErrorMessage(\"Lỗi: \" + error.message));\r\n            navigate(`/practice/exam/${examId}`);\r\n        }\r\n    };\r\n\r\n    // Removed socket-based exam_started listener - now handled in handleFullScreen\r\n\r\n    useEffect(() => {\r\n        if (exam?.testDuration && startTime1) {\r\n            const start = new Date(startTime1);\r\n            const now = new Date();\r\n            const elapsedSeconds = Math.floor((now - start) / 1000);\r\n            const totalSeconds = exam.testDuration * 60;\r\n            const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\r\n            dispatch(setRemainingTime(remaining));\r\n\r\n            // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\r\n            if (attemptId) {\r\n                dispatch(getRemainingTime({ examId, attemptId }))\r\n                    .then((result) => {\r\n                        if (result.payload?.remainingTime !== undefined) {\r\n                            dispatch(setRemainingTime(result.payload.remainingTime));\r\n                        }\r\n                    })\r\n                    .catch((error) => {\r\n                        console.error(\"Lỗi khi lấy thời gian từ server:\", error);\r\n                    });\r\n            }\r\n        }\r\n    }, [startTime1, exam, attemptId, examId, dispatch]);\r\n\r\n    useEffect(() => {\r\n        if (flag) return\r\n        if (!remainingTime) setFlag(true)\r\n    }, [remainingTime])\r\n\r\n    const handleAutoSubmit = async () => {\r\n        if (hasSubmittedRef.current) {\r\n            console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\r\n            return;\r\n        }\r\n        hasSubmittedRef.current = true; // Đánh dấu đã submit\r\n        console.log(\"Kiểm tra attemptId:\", attemptId);\r\n        if (!attemptId) {\r\n            console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\r\n            return;\r\n        }\r\n\r\n        console.log(\"Đang nộp bài với attemptId:\", attemptId);\r\n        dispatch(setSaveQuestions([]));\r\n        setLoadingSubmit(true);\r\n\r\n        try {\r\n            // Sử dụng API thay vì socket để nộp bài\r\n            const result = await dispatch(summitExam(attemptId)).unwrap();\r\n            console.log(\"Nộp bài thành công:\", result);\r\n\r\n            // Xử lý khi nộp bài thành công\r\n            dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\r\n\r\n            // Thoát fullscreen mà không bắt lỗi\r\n            try {\r\n                exitFullscreen();\r\n            } catch (err) {\r\n                // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\r\n                console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\r\n            }\r\n\r\n            const safeAttemptId = attemptRef.current;\r\n            const currentExam = examRef.current;\r\n\r\n            if (!safeAttemptId) {\r\n                console.error(\"Không có attemptId khi navigate!\");\r\n                return;\r\n            }\r\n\r\n            // Log để debug\r\n            console.log(\"Current exam state:\", currentExam);\r\n            console.log(\"Attempt ID:\", safeAttemptId);\r\n\r\n            if (!currentExam || !currentExam.seeCorrectAnswer) {\r\n                console.log(\"Chuyển về trang danh sách do:\", {\r\n                    examNull: !currentExam,\r\n                    cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\r\n                });\r\n                navigate(`/practice/exam/${examId}`);\r\n                return;\r\n            }\r\n\r\n            navigate(`/practice/exam/attempt/${safeAttemptId}/score`);\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi nộp bài:\", error);\r\n            setLoadingSubmit(false);\r\n            dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\r\n            hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\r\n\r\n            // Thử nộp lại sau 3 giây nếu lỗi xảy ra\r\n            setTimeout(() => {\r\n                if (!loadingSubmit && attemptRef.current) {\r\n                    console.log(\"Thử nộp bài lại sau lỗi...\");\r\n                    handleAutoSubmit();\r\n                }\r\n            }, 5000);\r\n        }\r\n    };\r\n\r\n    // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\r\n    const navigateToQuestion = useCallback((questionId) => {\r\n        setSelectedQuestion(questionId);\r\n\r\n        // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\r\n        if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\r\n            // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\r\n            examContentRef.current.goToQuestionById(questionId);\r\n        } else {\r\n            // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\r\n            // Tìm phần tử câu hỏi bằng querySelector\r\n            setTimeout(() => {\r\n                // Thử tìm phần tử bằng data-question-id\r\n                const element = document.querySelector(`[data-question-id=\"${questionId}\"]`);\r\n\r\n                if (element) {\r\n                    const offset = 80; // chiều cao của header sticky\r\n                    const y = element.getBoundingClientRect().top + window.scrollY - offset;\r\n                    window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                } else {\r\n                    // Fallback: Sử dụng refs\r\n                    const refElement = questionRefs.current[questionId];\r\n\r\n                    if (refElement) {\r\n                        const offset = 80; // chiều cao của header sticky\r\n                        const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\r\n                        window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                    }\r\n                }\r\n            }, 0);\r\n        }\r\n    }, [questionRefs, examContentRef]);\r\n\r\n    // navigateToQuestion is used directly in components\r\n\r\n    const handleSelectAnswerTN = (questionId, statementId, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const newAnswer = {\r\n            questionId,\r\n            answerContent: statementId,\r\n            typeOfQuestion: type,\r\n        };\r\n        dispatch(setAnswers(newAnswer));\r\n\r\n        // Sử dụng API thay vì socket\r\n        dispatch(submitAnswerWithAttempt({\r\n            questionId,\r\n            answerContent: statementId,\r\n            type,\r\n            attemptId\r\n        })).then((result) => {\r\n            if (result.type.endsWith('/fulfilled')) {\r\n                // Answer submitted successfully\r\n                console.log(\"Đã lưu câu trả lời thành công\");\r\n            } else {\r\n                // Handle error\r\n                console.error(\"Lỗi khi lưu câu trả lời:\", result.error);\r\n            }\r\n        });\r\n    };\r\n\r\n    const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const currentAnswers = dsAnswers[questionId] || [];\r\n\r\n        const existing = currentAnswers.find(ans => ans.statementId === statementId);\r\n\r\n        // 🔁 Nếu đáp án đã giống thì không gửi lại\r\n        if (existing && existing.answer === selectedAnswer) {\r\n            return\r\n        }\r\n\r\n        const updatedAnswers = currentAnswers.map(ans =>\r\n            ans.statementId === statementId\r\n                ? { ...ans, answer: selectedAnswer }\r\n                : ans\r\n        );\r\n\r\n        // Nếu chưa có statement này\r\n        if (!existing) {\r\n            updatedAnswers.push({ statementId, answer: selectedAnswer });\r\n        }\r\n\r\n        dispatch(setAnswers({ questionId, answerContent: JSON.stringify(updatedAnswers), typeOfQuestion: \"DS\" }));\r\n\r\n        // Sử dụng API thay vì socket\r\n        dispatch(submitAnswerWithAttempt({\r\n            questionId,\r\n            answerContent: updatedAnswers,\r\n            type: \"DS\",\r\n            attemptId\r\n        })).then((result) => {\r\n            if (result.type.endsWith('/fulfilled')) {\r\n                console.log(\"Đã lưu câu trả lời DS thành công\");\r\n            } else {\r\n                console.error(\"Lỗi khi lưu câu trả lời DS:\", result.error);\r\n            }\r\n        });\r\n    };\r\n\r\n\r\n    const handleSelectAnswerTLN = (questionId, answerContent, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        if (!answerContent || answerContent.trim() === \"\") {\r\n            return;\r\n        }\r\n\r\n        const formattedAnswer = answerContent.trim().replace(\",\", \".\");\r\n        dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));\r\n\r\n        // Sử dụng API thay vì socket\r\n        dispatch(submitAnswerWithAttempt({\r\n            questionId,\r\n            answerContent: formattedAnswer,\r\n            type,\r\n            attemptId\r\n        })).then((result) => {\r\n            if (result.type.endsWith('/fulfilled')) {\r\n                console.log(\"Đã lưu câu trả lời TLN thành công\");\r\n            } else {\r\n                console.error(\"Lỗi khi lưu câu trả lời TLN:\", result.error);\r\n            }\r\n        });\r\n    }\r\n\r\n    // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\r\n    const questionsToMarkAsSaved = useRef(new Set());\r\n\r\n    // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\r\n    useEffect(() => {\r\n        if (questionsToMarkAsSaved.current.size > 0) {\r\n            questionsToMarkAsSaved.current.forEach(questionId => {\r\n                if (!saveQuestions.includes(questionId)) {\r\n                    addQuestion(questionId);\r\n                }\r\n            });\r\n            questionsToMarkAsSaved.current.clear();\r\n        }\r\n    }, [saveQuestions, addQuestion]);\r\n\r\n    // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\r\n    useEffect(() => {\r\n        // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\r\n        const frameId = requestAnimationFrame(() => {\r\n            if (questionsToMarkAsSaved.current.size > 0) {\r\n                const questionIds = [...questionsToMarkAsSaved.current];\r\n                questionsToMarkAsSaved.current.clear();\r\n\r\n                // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\r\n                questionIds.forEach(questionId => {\r\n                    if (!saveQuestions.includes(questionId)) {\r\n                        addQuestion(questionId);\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        return () => cancelAnimationFrame(frameId);\r\n    });\r\n\r\n    const isTNSelected = useCallback((questionId, statementId) => {\r\n        const isSelected = answerTN.some(\r\n            (ans) =>\r\n                ans.questionId === questionId &&\r\n                ans.answerContent &&\r\n                String(ans.answerContent) === String(statementId)\r\n        );\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.includes(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [answerTN, saveQuestions]);\r\n\r\n    const isDSChecked = useCallback((questionId, statementId, bool) => {\r\n        const isSelected = dsAnswers[questionId]?.some(\r\n            (a) => a.statementId === statementId && a.answer === bool\r\n        ) || false;\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.includes(questionId) && dsAnswers[questionId]?.length === 4) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [dsAnswers, saveQuestions]);\r\n\r\n    const getTLNDefaultValue = useCallback((questionId) => {\r\n        const matched = answerTLN.find((ans) => ans.questionId === questionId);\r\n        const content = matched?.answerContent?.replace(/^\"|\"$/g, \"\") || \"\";\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (content && !saveQuestions.includes(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return content;\r\n    }, [answerTLN, saveQuestions]);\r\n\r\n    // useEffect(() => {\r\n    //     if (examId) {\r\n    //         dispatch(fetchPublicQuestionsByExamId(examId));\r\n    //     }\r\n    // }, [dispatch, examId]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            setQuestionTN(questions.filter((question) => question.typeOfQuestion === \"TN\"));\r\n            setQuestionDS(questions.filter((question) => question.typeOfQuestion === \"DS\"));\r\n            setQuestionTLN(questions.filter((question) => question.typeOfQuestion === \"TLN\"));\r\n        }\r\n    }, [questions]);\r\n\r\n    useEffect(() => {\r\n        // Kiểm tra answers có phải là mảng không\r\n        if (!Array.isArray(answers) || answers.length === 0) return;\r\n\r\n        const tn = [];\r\n        const tln = [];\r\n        const dsMap = {};\r\n\r\n        // Sử dụng for...of thay vì forEach để tránh lỗi\r\n        for (const answer of answers) {\r\n            if (answer.typeOfQuestion === \"TN\") {\r\n                tn.push(answer);\r\n            } else if (answer.typeOfQuestion === \"TLN\") {\r\n                tln.push(answer);\r\n            } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\r\n                try {\r\n                    const parsed = JSON.parse(answer.answerContent);\r\n                    dsMap[answer.questionId] = parsed;\r\n                } catch (err) {\r\n                    console.error(\"Lỗi parse DS answerContent:\", err);\r\n                }\r\n            }\r\n        }\r\n\r\n        setAnswerTN(tn);\r\n        setAnswerTLN(tln);\r\n        setDsAnswers(dsMap);\r\n\r\n        // Note: Score calculation is now handled when submitting exam\r\n        // No need to calculate score in real-time\r\n    }, [answers]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (attemptId) {\r\n            dispatch(fetchAnswersByAttempt(attemptId));\r\n        }\r\n    }, [dispatch, attemptId]);\r\n\r\n    useEffect(() => {\r\n        if (!exam?.testDuration || remainingTime === null || !isAgree) return;\r\n\r\n        // Kiểm tra và hiển thị cảnh báo thời gian\r\n        const checkTimeWarnings = (time) => {\r\n            // Cảnh báo khi còn 5 phút\r\n            if (time === 300 && !timeWarningShown.fiveMinutes) {\r\n                setTimeWarningShown(prev => ({ ...prev, fiveMinutes: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Tắt hiệu ứng nhấp nháy sau 10 giây\r\n                setTimeout(() => {\r\n                    setIsTimeBlinking(false);\r\n                }, 10000);\r\n            }\r\n\r\n            // Cảnh báo khi còn 1 phút\r\n            if (time === 60 && !timeWarningShown.oneMinute) {\r\n                setTimeWarningShown(prev => ({ ...prev, oneMinute: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\r\n            }\r\n        };\r\n\r\n        // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API\r\n        const syncTimeInterval = setInterval(() => {\r\n            if (attemptId) {\r\n                dispatch(getRemainingTime({ examId, attemptId }))\r\n                    .then((result) => {\r\n                        if (result.payload?.remainingTime !== undefined) {\r\n                            dispatch(setRemainingTime(result.payload.remainingTime));\r\n                        }\r\n                    })\r\n                    .catch((error) => {\r\n                        console.error(\"Lỗi khi đồng bộ thời gian:\", error);\r\n                    });\r\n            }\r\n        }, 30000); // Đồng bộ thời gian mỗi 30 giây\r\n\r\n        const interval = setInterval(() => {\r\n            dispatch(setRemainingTime((prev) => {\r\n                if (prev <= 1) { // dùng <=1 để đảm bảo không bị âm\r\n                    clearInterval(interval);\r\n                    clearInterval(syncTimeInterval);\r\n                    // Đánh dấu là đã hết thời gian\r\n                    setIsTimeUp(true);\r\n                    setIsTimeBlinking(false);\r\n                    // Thử nộp bài\r\n                    handleAutoSubmit();\r\n                    return 0;\r\n                }\r\n\r\n                // Kiểm tra cảnh báo thời gian\r\n                checkTimeWarnings(prev);\r\n\r\n                return prev - 1;\r\n            }));\r\n        }, 1000);\r\n\r\n        return () => {\r\n            clearInterval(interval);\r\n            clearInterval(syncTimeInterval);\r\n        };\r\n    }, [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId]);// Chỉ phụ thuộc vào các giá trị cần thiết\r\n\r\n    // Removed socket connection management - using API only\r\n\r\n    // frontend\r\n    useEffect(() => {\r\n        if (!attemptId || !user?.id || !examId || attemptId === null || attemptId === undefined) return;\r\n        if (!exam?.isCheatingCheckEnabled) return;\r\n        console.log(\"Đã bật theo dõi hành vi gian lận\");\r\n\r\n\r\n        const recentLogs = new Set(); // chống log lặp\r\n        const logOnce = (key, payload) => {\r\n            if (!exam?.isCheatingCheckEnabled || recentLogs.has(key)) return;\r\n\r\n            recentLogs.add(key);\r\n\r\n            // Sử dụng API thay vì socket\r\n            dispatch(logUserActivity({\r\n                examId,\r\n                attemptId,\r\n                activityType: payload.type || 'user_activity',\r\n                details: {\r\n                    ...payload,\r\n                    name: user.lastName + \" \" + user.firstName\r\n                }\r\n            }));\r\n\r\n            setTimeout(() => recentLogs.delete(key), 5000);\r\n        };\r\n\r\n        // 📌 Thoát fullscreen\r\n        const handleFullscreenChange = () => {\r\n            if (!document.fullscreenElement &&\r\n                !document.webkitFullscreenElement &&\r\n                !document.mozFullScreenElement &&\r\n                !document.msFullscreenElement) {\r\n                logOnce(\"exit_fullscreen\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"EF\",\r\n                    action: \"exit_fullscreen\",\r\n                    detail: JSON.stringify({ reason: \"User exited fullscreen mode\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\r\n        const handleVisibilityChange = () => {\r\n            if (document.visibilityState === \"hidden\") {\r\n                logOnce(\"tab_blur\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"TB\",\r\n                    action: \"tab_blur\",\r\n                    detail: JSON.stringify({ message: \"User switched tab or minimized window\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Copy nội dung\r\n        const handleCopy = () => {\r\n            logOnce(\"copy_detected\", {\r\n                studentId: user.id,\r\n                attemptId,\r\n                examId,\r\n                code: \"COP\",\r\n                action: \"copy_detected\",\r\n                detail: JSON.stringify({ message: \"User copied content\" }),\r\n            });\r\n        };\r\n\r\n        // 📌 Phím đáng ngờ\r\n        const handleSuspiciousKey = (e) => {\r\n            const suspiciousKeys = [\r\n                \"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"\r\n            ];\r\n            const combo = `${e.ctrlKey ? \"Ctrl+\" : \"\"}${e.shiftKey ? \"Shift+\" : \"\"}${e.altKey ? \"Alt+\" : \"\"}${e.metaKey ? \"Meta+\" : \"\"}${e.key}`;\r\n\r\n            if (\r\n                suspiciousKeys.includes(e.key) ||\r\n                combo === \"Ctrl+Shift+I\" ||\r\n                combo === \"Ctrl+Shift+C\"\r\n            ) {\r\n                logOnce(`key_${combo}`, {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"SK\",\r\n                    action: \"suspicious_key\",\r\n                    detail: JSON.stringify({ key: e.key, code: e.code, combo }),\r\n                });\r\n            }\r\n        };\r\n\r\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n        document.addEventListener(\"copy\", handleCopy);\r\n        document.addEventListener(\"keydown\", handleSuspiciousKey);\r\n\r\n        return () => {\r\n            document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n            document.removeEventListener(\"copy\", handleCopy);\r\n            document.removeEventListener(\"keydown\", handleSuspiciousKey);\r\n        };\r\n    }, [user.id, examId, attemptId]);\r\n\r\n\r\n    useEffect(() => {\r\n        // Removed all socket event listeners - using API responses instead\r\n        // Answer save/error status is now handled in submitAnswerWithAttempt action responses\r\n        // Timer updates are handled via getRemainingTime API calls\r\n        // Auto-submit is handled via client-side timer logic\r\n    }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\r\n\r\n    useEffect(() => {\r\n        localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\r\n    }, [isDarkMode]);\r\n\r\n    // Hàm xử lý chuyển đổi câu hỏi\r\n    const handleKeyDown = useCallback((e) => {\r\n        // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\r\n        if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\r\n            // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\r\n            e.preventDefault();\r\n\r\n            // Nếu không có câu hỏi, thoát khỏi hàm\r\n            if (!questions || questions.length === 0) return;\r\n\r\n            const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\r\n            const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\r\n\r\n            if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\r\n                const prevQuestionId = allQuestions[currentIndex - 1].id;\r\n                console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\r\n                navigateToQuestion(prevQuestionId);\r\n            } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\r\n                const nextQuestionId = allQuestions[currentIndex + 1].id;\r\n                console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\r\n                navigateToQuestion(nextQuestionId);\r\n            }\r\n        }\r\n    }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\r\n    // Lắng nghe sự kiện bàn phím\r\n    useEffect(() => {\r\n        document.addEventListener(\"keydown\", handleKeyDown);\r\n        return () => {\r\n            document.removeEventListener(\"keydown\", handleKeyDown);\r\n        };\r\n    }, [handleKeyDown]);\r\n\r\n    return (\r\n        <div className={`flex flex-col h-full ${isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'}`}>\r\n            <HeaderDoExamPage nameExam={exam?.name} onExitFullscreen={handleExitFullscreen} isDarkMode={!isDarkMode} />\r\n            {isAgree ? (\r\n                <div className=\"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\">\r\n                    {/* Main Content */}\r\n                    <ExamContent\r\n                        ref={examContentRef}\r\n                        loading1={loadingJoin}\r\n                        isDarkMode={isDarkMode}\r\n                        questionTN={questionTN}\r\n                        questionDS={questionDS}\r\n                        questionTLN={questionTLN}\r\n                        handlers={{\r\n                            handleSelectAnswerTN,\r\n                            handleSelectAnswerDS,\r\n                            handleSelectAnswerTLN,\r\n                            isTNSelected,\r\n                            isDSChecked,\r\n                            getTLNDefaultValue,\r\n                            setQuestionRef: (id, el) => (questionRefs.current[id] = el),\r\n                            setSelectedQuestion: (id) => setSelectedQuestion(id)\r\n                        }}\r\n                        settings={{\r\n                            selectedQuestion,\r\n                            isDarkMode,\r\n                            fontSize,\r\n                            imageSize,\r\n                            prefixStatementTN,\r\n                            prefixStatementDS,\r\n                            isTimeUp,\r\n                            markedQuestions,\r\n                            toggleMarkQuestion\r\n                        }}\r\n                        isTimeUp={isTimeUp}\r\n                        // Để undefined để component tự quyết định dựa trên thiết bị\r\n                        initialSingleMode={undefined}\r\n                        handleAutoSubmit={handleAutoSubmit}\r\n                        loadingSubmit={loadingSubmit}\r\n                    />\r\n\r\n\r\n                    {/* Button toggle cho mobile */}\r\n                    <div className=\"fixed bottom-4 right-4 z-50 lg:hidden\">\r\n                        <button\r\n                            className={`p-2 rounded-full shadow-md ${isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"}`}\r\n                            onClick={() => setIsSidebarOpen(prev => !prev)}\r\n                        >\r\n                            <Menu />\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Sidebar chính */}\r\n                    <AnimatePresence>\r\n                        {(isSidebarOpen || window.innerWidth > 1024) && (\r\n                            <ExamSidebar\r\n                                isDarkMode={isDarkMode}\r\n                                setIsDarkMode={setIsDarkMode}\r\n                                fontSize={fontSize}\r\n                                handleFontSizeChange={handleFontSizeChange}\r\n                                imageSize={imageSize}\r\n                                handleImageSizeChange={handleImageSizeChange}\r\n                                questionTN={questionTN}\r\n                                questionDS={questionDS}\r\n                                questionTLN={questionTLN}\r\n                                scrollToQuestion={navigateToQuestion}\r\n                                selectedQuestion={selectedQuestion}\r\n                                markedQuestions={markedQuestions}\r\n                                toggleMarkQuestion={toggleMarkQuestion}\r\n                                handleAutoSubmit={handleAutoSubmit}\r\n                                loadingSubmit={loadingSubmit}\r\n                                loadingLoadExam={loadingJoin}\r\n                                exam={exam}\r\n                                remainingTime={remainingTime}\r\n                                formatTime={formatTime}\r\n                                questions={questions}\r\n                                singleQuestionMode={examContentRef.current?.isSingleQuestionMode() || false}\r\n                                setSingleQuestionMode={(value) => {\r\n                                    if (examContentRef.current) {\r\n                                        // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\r\n                                        examContentRef.current.setSingleQuestionMode(value);\r\n                                    }\r\n                                }}\r\n                            />\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                </div>\r\n            ) : (\r\n                <div className=\"flex items-center justify-center\">\r\n                    <ExamRegulationModal\r\n                        onClose={() => {\r\n                            // Sử dụng API để leave exam nếu có attemptId\r\n                            if (attemptId) {\r\n                                dispatch(leaveExam({ examId, attemptId }));\r\n                            }\r\n                            navigate(`/practice/exam/${examId}`);\r\n                        }}\r\n                        isOpen={!isAgree}\r\n                        onStartExam={handleFullScreen}\r\n                    />\r\n                </div>\r\n            )}\r\n\r\n            {exam?.testDuration && isAgree && (\r\n                <div className={`fixed bottom-2 rounded-md left-2 px-4 py-2\r\n                    ${isTimeBlinking\r\n                        ? 'bg-red-600 animate-pulse'\r\n                        : 'bg-slate-700 bg-opacity-80'}\r\n                    text-white z-50 transition-colors duration-300`}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <div className=\"text-sm font-bold\">{formatTime(remainingTime)} phút</div>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DoExamPage;\r\n"], "mappings": ";;AAAA,OAAOA,gBAAgB,MAAM,6CAA6C;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,uCAAuC;AAC1F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,sCAAsC;AACxF,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,WAAW,MAAM,2CAA2C;AACnE,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gCAAgC;AAChG,SACIC,gBAAgB,EAChBC,UAAU,EACVC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,uBAAuB,EACvBC,SAAS,EACTC,QAAQ,QACL,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAMsC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAK,CAAC,GAAGtC,WAAW,CAACuC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAClD,MAAM;IAAEC;EAAU,CAAC,GAAGzC,WAAW,CAACuC,KAAK,IAAIA,KAAK,CAACE,SAAS,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAG1C,WAAW,CAACuC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;EACvD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM8C,YAAY,GAAG5C,MAAM,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMmD,UAAU,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsD,IAAI,EAAEC,OAAO,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM0D,eAAe,GAAGxD,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMyD,OAAO,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM0D,cAAc,GAAG1D,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACZ0D,OAAO,CAACE,OAAO,GAAGxB,IAAI;IACtB,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,YAAY,MAAK,KAAK,EAAE;MAC9B1B,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;EAEVpC,SAAS,CAAC,MAAM;IACZ,IAAIiC,MAAM,EAAE;MACRC,QAAQ,CAAC9B,mBAAmB,CAAC6B,MAAM,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,CAACC,QAAQ,EAAED,MAAM,CAAC,CAAC;EAGtB,MAAM;IAAE8B;EAAK,CAAC,GAAGjE,WAAW,CAAEuC,KAAK,IAAKA,KAAK,CAAC2B,IAAI,CAAC;EACnD,MAAM;IAAEC,aAAa;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGtE,WAAW,CAAEuC,KAAK,IAAKA,KAAK,CAACgC,MAAM,CAAC;EAG1G,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,IAAIyE,GAAG,CAAC,CAAC,CAAC;EACjE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAC;IACrD4E,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMgF,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtF,MAAMC,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEtF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,MAAM;IAC/C,MAAMsF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC5C,CAAC,CAAC;EAEF,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM,CAAC+F,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmG,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuG,SAAS,EAAEC,YAAY,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyG,SAAS,EAAEC,YAAY,CAAC,GAAG1G,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C2G,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAGC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAIC,UAAU,IAAK;IAChC,IAAI,CAAC7C,aAAa,CAAC8C,QAAQ,CAACD,UAAU,CAAC,EAAE;MACrC7E,QAAQ,CAACb,gBAAgB,CAAC,CAAC,GAAG6C,aAAa,EAAE6C,UAAU,CAAC,CAAC,CAAC;IAC9D;IACAE,mBAAmB,CAACF,UAAU,CAAC;EACnC,CAAC;EAED,MAAMG,gBAAgB,GAAIH,UAAU,IAAK;IACrC,IAAI,CAAC5C,cAAc,CAAC6C,QAAQ,CAACD,UAAU,CAAC,EAAE;MACtC7E,QAAQ,CAACZ,iBAAiB,CAAC,CAAC,GAAG6C,cAAc,EAAE4C,UAAU,CAAC,CAAC,CAAC;IAChE;IACAI,cAAc,CAACJ,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMI,cAAc,GAAIJ,UAAU,IAAK;IACnC7E,QAAQ,CAACb,gBAAgB,CAAC6C,aAAa,CAACkD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,UAAU,CAAC,CAAC,CAAC;EAC7E,CAAC;EAED,MAAME,mBAAmB,GAAIF,UAAU,IAAK;IACxC7E,QAAQ,CAACZ,iBAAiB,CAAC6C,cAAc,CAACiD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,UAAU,CAAC,CAAC,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIP,UAAU,IAAK;IACvCxC,kBAAkB,CAACgD,IAAI,IAAI;MACvB,MAAMC,MAAM,GAAG,IAAIhD,GAAG,CAAC+C,IAAI,CAAC;MAC5B,IAAIC,MAAM,CAACC,GAAG,CAACV,UAAU,CAAC,EAAE;QACxBS,MAAM,CAACE,MAAM,CAACX,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHS,MAAM,CAACG,GAAG,CAACZ,UAAU,CAAC;MAC1B;MACA,OAAOS,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAGD,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACA3G,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO4G,GAAG,EAAE;MACV;MACAC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,GAAG,CAAC;IACpD;EACJ,CAAC;EAED,MAAMG,oBAAoB,GAAIpB,CAAC,IAAK;IAChClE,WAAW,CAACuF,MAAM,CAACrB,CAAC,CAACsB,MAAM,CAACC,KAAK,CAAC,CAAC;EACvC,CAAC;EAED,MAAMC,qBAAqB,GAAIxB,CAAC,IAAK;IACjChE,YAAY,CAACqF,MAAM,CAACrB,CAAC,CAACsB,MAAM,CAACC,KAAK,CAAC,CAAC;EACxC,CAAC;EAED,MAAME,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,MAAMC,GAAG,GAAGJ,MAAM,CAACF,OAAO,GAAG,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjD,UAAA7E,MAAA,CAAUyE,GAAG,OAAAzE,MAAA,CAAI8E,GAAG;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA;MACA,MAAMC,MAAM,GAAG,MAAM5G,QAAQ,CAACP,QAAQ,CAACM,MAAM,CAAC,CAAC,CAAC8G,MAAM,CAAC,CAAC;;MAExD;MACA,MAAM;QAAE/F,SAAS;QAAEgG;MAAU,CAAC,GAAGF,MAAM;MACvChB,OAAO,CAACmB,GAAG,CAAC,+CAA+C,EAAEjG,SAAS,CAAC;MAEvED,UAAU,CAAC,IAAI,CAAC;MAChBG,UAAU,CAACU,OAAO,GAAGZ,SAAS;MAC9BC,YAAY,CAACD,SAAS,CAAC;MAEvB,IAAIf,MAAM,EAAE;QACRC,QAAQ,CAAC/B,4BAA4B,CAAC8B,MAAM,CAAC,CAAC;MAClD;MACAuB,aAAa,CAACwF,SAAS,CAAC;MAExB,IAAI,EAAC5G,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8G,sBAAsB,GAAE;QAC/B;MACJ;MAEA,IAAI;QACA,MAAMC,OAAO,GAAG,MAAMnI,iBAAiB,CAAC,CAAC;QACzC,IAAI,CAACmI,OAAO,EAAE;UACVrB,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;QACxE;MACJ,CAAC,CAAC,OAAOF,GAAG,EAAE;QACVC,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEvB,GAAG,CAAC;QAC/CwB,KAAK,CAAC,yDAAyD,CAAC;MACpE;IAEJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZtB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlH,QAAQ,CAAC5B,eAAe,CAAC,OAAO,GAAG8I,KAAK,CAACE,OAAO,CAAC,CAAC;MAClDnH,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC;;EAED;;EAEAjC,SAAS,CAAC,MAAM;IACZ,IAAIoC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmH,YAAY,IAAIhG,UAAU,EAAE;MAClC,MAAMiG,KAAK,GAAG,IAAIC,IAAI,CAAClG,UAAU,CAAC;MAClC,MAAMmG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,cAAc,GAAGlB,IAAI,CAACC,KAAK,CAAC,CAACgB,GAAG,GAAGF,KAAK,IAAI,IAAI,CAAC;MACvD,MAAMI,YAAY,GAAGxH,IAAI,CAACmH,YAAY,GAAG,EAAE;MAC3C,MAAMM,SAAS,GAAGpB,IAAI,CAACqB,GAAG,CAACF,YAAY,GAAGD,cAAc,EAAE,CAAC,CAAC;MAC5DzH,QAAQ,CAACf,gBAAgB,CAAC0I,SAAS,CAAC,CAAC;;MAErC;MACA,IAAI7G,SAAS,EAAE;QACXd,QAAQ,CAACX,gBAAgB,CAAC;UAAEU,MAAM;UAAEe;QAAU,CAAC,CAAC,CAAC,CAC5C+G,IAAI,CAAEjB,MAAM,IAAK;UAAA,IAAAkB,eAAA;UACd,IAAI,EAAAA,eAAA,GAAAlB,MAAM,CAACmB,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgB/F,aAAa,MAAKiG,SAAS,EAAE;YAC7ChI,QAAQ,CAACf,gBAAgB,CAAC2H,MAAM,CAACmB,OAAO,CAAChG,aAAa,CAAC,CAAC;UAC5D;QACJ,CAAC,CAAC,CACDkG,KAAK,CAAEf,KAAK,IAAK;UACdtB,OAAO,CAACsB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC5D,CAAC,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAC7F,UAAU,EAAEnB,IAAI,EAAEY,SAAS,EAAEf,MAAM,EAAEC,QAAQ,CAAC,CAAC;EAEnDlC,SAAS,CAAC,MAAM;IACZ,IAAIqD,IAAI,EAAE;IACV,IAAI,CAACY,aAAa,EAAEX,OAAO,CAAC,IAAI,CAAC;EACrC,CAAC,EAAE,CAACW,aAAa,CAAC,CAAC;EAEnB,MAAMmG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI3G,eAAe,CAACG,OAAO,EAAE;MACzBkE,OAAO,CAACC,IAAI,CAAC,sCAAsC,CAAC;MACpD;IACJ;IACAtE,eAAe,CAACG,OAAO,GAAG,IAAI,CAAC,CAAC;IAChCkE,OAAO,CAACmB,GAAG,CAAC,qBAAqB,EAAEjG,SAAS,CAAC;IAC7C,IAAI,CAACA,SAAS,EAAE;MACZ8E,OAAO,CAACmB,GAAG,CAAC,8EAA8E,CAAC;MAC3F;IACJ;IAEAnB,OAAO,CAACmB,GAAG,CAAC,6BAA6B,EAAEjG,SAAS,CAAC;IACrDd,QAAQ,CAACb,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC9BsE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACA;MACA,MAAMmD,MAAM,GAAG,MAAM5G,QAAQ,CAACd,UAAU,CAAC4B,SAAS,CAAC,CAAC,CAAC+F,MAAM,CAAC,CAAC;MAC7DjB,OAAO,CAACmB,GAAG,CAAC,qBAAqB,EAAEH,MAAM,CAAC;;MAE1C;MACA5G,QAAQ,CAAC3B,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;;MAElD;MACA,IAAI;QACAU,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,OAAO4G,GAAG,EAAE;QACV;QACAC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAChE;MAEA,MAAMwC,aAAa,GAAGnH,UAAU,CAACU,OAAO;MACxC,MAAM0G,WAAW,GAAG5G,OAAO,CAACE,OAAO;MAEnC,IAAI,CAACyG,aAAa,EAAE;QAChBvC,OAAO,CAACsB,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACJ;;MAEA;MACAtB,OAAO,CAACmB,GAAG,CAAC,qBAAqB,EAAEqB,WAAW,CAAC;MAC/CxC,OAAO,CAACmB,GAAG,CAAC,aAAa,EAAEoB,aAAa,CAAC;MAEzC,IAAI,CAACC,WAAW,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QAC/CzC,OAAO,CAACmB,GAAG,CAAC,+BAA+B,EAAE;UACzCuB,QAAQ,EAAE,CAACF,WAAW;UACtBG,aAAa,EAAEH,WAAW,IAAI,CAACA,WAAW,CAACC;QAC/C,CAAC,CAAC;QACFpI,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACpC;MACJ;MAEAE,QAAQ,2BAAA2B,MAAA,CAA2BuG,aAAa,WAAQ,CAAC;IAC7D,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACZtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCzD,gBAAgB,CAAC,KAAK,CAAC;MACvBzD,QAAQ,CAAC5B,eAAe,CAAC,oCAAoC,CAAC,CAAC;MAC/DmD,eAAe,CAACG,OAAO,GAAG,KAAK,CAAC,CAAC;;MAEjC;MACA8G,UAAU,CAAC,MAAM;QACb,IAAI,CAAChF,aAAa,IAAIxC,UAAU,CAACU,OAAO,EAAE;UACtCkE,OAAO,CAACmB,GAAG,CAAC,4BAA4B,CAAC;UACzCmB,gBAAgB,CAAC,CAAC;QACtB;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAGzK,WAAW,CAAE6G,UAAU,IAAK;IACnD7B,mBAAmB,CAAC6B,UAAU,CAAC;;IAE/B;IACA,IAAIpD,cAAc,CAACC,OAAO,IAAID,cAAc,CAACC,OAAO,CAACgH,oBAAoB,CAAC,CAAC,EAAE;MACzE;MACAjH,cAAc,CAACC,OAAO,CAACiH,gBAAgB,CAAC9D,UAAU,CAAC;IACvD,CAAC,MAAM;MACH;MACA;MACA2D,UAAU,CAAC,MAAM;QACb;QACA,MAAMI,OAAO,GAAGpE,QAAQ,CAACqE,aAAa,wBAAAjH,MAAA,CAAuBiD,UAAU,QAAI,CAAC;QAE5E,IAAI+D,OAAO,EAAE;UACT,MAAME,MAAM,GAAG,EAAE,CAAC,CAAC;UACnB,MAAMC,CAAC,GAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;UACvEI,MAAM,CAACE,QAAQ,CAAC;YAAEH,GAAG,EAAEF,CAAC;YAAEM,QAAQ,EAAE;UAAS,CAAC,CAAC;QACnD,CAAC,MAAM;UACH;UACA,MAAMC,UAAU,GAAG3I,YAAY,CAACe,OAAO,CAACmD,UAAU,CAAC;UAEnD,IAAIyE,UAAU,EAAE;YACZ,MAAMR,MAAM,GAAG,EAAE,CAAC,CAAC;YACnB,MAAMC,CAAC,GAAGO,UAAU,CAACN,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;YAC1EI,MAAM,CAACE,QAAQ,CAAC;cAAEH,GAAG,EAAEF,CAAC;cAAEM,QAAQ,EAAE;YAAS,CAAC,CAAC;UACnD;QACJ;MACJ,CAAC,EAAE,CAAC,CAAC;IACT;EACJ,CAAC,EAAE,CAAC1I,YAAY,EAAEc,cAAc,CAAC,CAAC;;EAElC;;EAEA,MAAM8H,oBAAoB,GAAGA,CAAC1E,UAAU,EAAE2E,WAAW,EAAEC,IAAI,KAAK;IAC5D;IACA,IAAI/F,QAAQ,EAAE;MACV1D,QAAQ,CAAC5B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAMsL,SAAS,GAAG;MACd7E,UAAU;MACV8E,aAAa,EAAEH,WAAW;MAC1BI,cAAc,EAAEH;IACpB,CAAC;IACDzJ,QAAQ,CAACxB,UAAU,CAACkL,SAAS,CAAC,CAAC;;IAE/B;IACA1J,QAAQ,CAACT,uBAAuB,CAAC;MAC7BsF,UAAU;MACV8E,aAAa,EAAEH,WAAW;MAC1BC,IAAI;MACJ3I;IACJ,CAAC,CAAC,CAAC,CAAC+G,IAAI,CAAEjB,MAAM,IAAK;MACjB,IAAIA,MAAM,CAAC6C,IAAI,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC;QACAjE,OAAO,CAACmB,GAAG,CAAC,+BAA+B,CAAC;MAChD,CAAC,MAAM;QACH;QACAnB,OAAO,CAACsB,KAAK,CAAC,0BAA0B,EAAEN,MAAM,CAACM,KAAK,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EAED,MAAM4C,oBAAoB,GAAGA,CAACjF,UAAU,EAAE2E,WAAW,EAAEO,cAAc,KAAK;IACtE;IACA,IAAIrG,QAAQ,EAAE;MACV1D,QAAQ,CAAC5B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAM4L,cAAc,GAAG1F,SAAS,CAACO,UAAU,CAAC,IAAI,EAAE;IAElD,MAAMoF,QAAQ,GAAGD,cAAc,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACX,WAAW,KAAKA,WAAW,CAAC;;IAE5E;IACA,IAAIS,QAAQ,IAAIA,QAAQ,CAACG,MAAM,KAAKL,cAAc,EAAE;MAChD;IACJ;IAEA,MAAMM,cAAc,GAAGL,cAAc,CAACM,GAAG,CAACH,GAAG,IACzCA,GAAG,CAACX,WAAW,KAAKA,WAAW,GACzB;MAAE,GAAGW,GAAG;MAAEC,MAAM,EAAEL;IAAe,CAAC,GAClCI,GACV,CAAC;;IAED;IACA,IAAI,CAACF,QAAQ,EAAE;MACXI,cAAc,CAACE,IAAI,CAAC;QAAEf,WAAW;QAAEY,MAAM,EAAEL;MAAe,CAAC,CAAC;IAChE;IAEA/J,QAAQ,CAACxB,UAAU,CAAC;MAAEqG,UAAU;MAAE8E,aAAa,EAAErG,IAAI,CAACkH,SAAS,CAACH,cAAc,CAAC;MAAET,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC;;IAEzG;IACA5J,QAAQ,CAACT,uBAAuB,CAAC;MAC7BsF,UAAU;MACV8E,aAAa,EAAEU,cAAc;MAC7BZ,IAAI,EAAE,IAAI;MACV3I;IACJ,CAAC,CAAC,CAAC,CAAC+G,IAAI,CAAEjB,MAAM,IAAK;MACjB,IAAIA,MAAM,CAAC6C,IAAI,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpCjE,OAAO,CAACmB,GAAG,CAAC,kCAAkC,CAAC;MACnD,CAAC,MAAM;QACHnB,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAEN,MAAM,CAACM,KAAK,CAAC;MAC9D;IACJ,CAAC,CAAC;EACN,CAAC;EAGD,MAAMuD,qBAAqB,GAAGA,CAAC5F,UAAU,EAAE8E,aAAa,EAAEF,IAAI,KAAK;IAC/D;IACA,IAAI/F,QAAQ,EAAE;MACV1D,QAAQ,CAAC5B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,IAAI,CAACuL,aAAa,IAAIA,aAAa,CAACe,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/C;IACJ;IAEA,MAAMC,eAAe,GAAGhB,aAAa,CAACe,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC9D5K,QAAQ,CAACxB,UAAU,CAAC;MAAEqG,UAAU;MAAE8E,aAAa;MAAEC,cAAc,EAAEH;IAAK,CAAC,CAAC,CAAC;;IAEzE;IACAzJ,QAAQ,CAACT,uBAAuB,CAAC;MAC7BsF,UAAU;MACV8E,aAAa,EAAEgB,eAAe;MAC9BlB,IAAI;MACJ3I;IACJ,CAAC,CAAC,CAAC,CAAC+G,IAAI,CAAEjB,MAAM,IAAK;MACjB,IAAIA,MAAM,CAAC6C,IAAI,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpCjE,OAAO,CAACmB,GAAG,CAAC,mCAAmC,CAAC;MACpD,CAAC,MAAM;QACHnB,OAAO,CAACsB,KAAK,CAAC,8BAA8B,EAAEN,MAAM,CAACM,KAAK,CAAC;MAC/D;IACJ,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAM2D,sBAAsB,GAAG9M,MAAM,CAAC,IAAIuE,GAAG,CAAC,CAAC,CAAC;;EAEhD;EACAxE,SAAS,CAAC,MAAM;IACZ,IAAI+M,sBAAsB,CAACnJ,OAAO,CAACoJ,IAAI,GAAG,CAAC,EAAE;MACzCD,sBAAsB,CAACnJ,OAAO,CAACqJ,OAAO,CAAClG,UAAU,IAAI;QACjD,IAAI,CAAC7C,aAAa,CAAC8C,QAAQ,CAACD,UAAU,CAAC,EAAE;UACrCD,WAAW,CAACC,UAAU,CAAC;QAC3B;MACJ,CAAC,CAAC;MACFgG,sBAAsB,CAACnJ,OAAO,CAACsJ,KAAK,CAAC,CAAC;IAC1C;EACJ,CAAC,EAAE,CAAChJ,aAAa,EAAE4C,WAAW,CAAC,CAAC;;EAEhC;EACA9G,SAAS,CAAC,MAAM;IACZ;IACA,MAAMmN,OAAO,GAAGC,qBAAqB,CAAC,MAAM;MACxC,IAAIL,sBAAsB,CAACnJ,OAAO,CAACoJ,IAAI,GAAG,CAAC,EAAE;QACzC,MAAMK,WAAW,GAAG,CAAC,GAAGN,sBAAsB,CAACnJ,OAAO,CAAC;QACvDmJ,sBAAsB,CAACnJ,OAAO,CAACsJ,KAAK,CAAC,CAAC;;QAEtC;QACAG,WAAW,CAACJ,OAAO,CAAClG,UAAU,IAAI;UAC9B,IAAI,CAAC7C,aAAa,CAAC8C,QAAQ,CAACD,UAAU,CAAC,EAAE;YACrCD,WAAW,CAACC,UAAU,CAAC;UAC3B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IAEF,OAAO,MAAMuG,oBAAoB,CAACH,OAAO,CAAC;EAC9C,CAAC,CAAC;EAEF,MAAMI,YAAY,GAAGrN,WAAW,CAAC,CAAC6G,UAAU,EAAE2E,WAAW,KAAK;IAC1D,MAAM8B,UAAU,GAAGpH,QAAQ,CAACqH,IAAI,CAC3BpB,GAAG,IACAA,GAAG,CAACtF,UAAU,KAAKA,UAAU,IAC7BsF,GAAG,CAACR,aAAa,IACjBrD,MAAM,CAAC6D,GAAG,CAACR,aAAa,CAAC,KAAKrD,MAAM,CAACkD,WAAW,CACxD,CAAC;;IAED;IACA,IAAI8B,UAAU,IAAI,CAACtJ,aAAa,CAAC8C,QAAQ,CAACD,UAAU,CAAC,EAAE;MACnDgG,sBAAsB,CAACnJ,OAAO,CAAC+D,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAOyG,UAAU;EACrB,CAAC,EAAE,CAACpH,QAAQ,EAAElC,aAAa,CAAC,CAAC;EAE7B,MAAMwJ,WAAW,GAAGxN,WAAW,CAAC,CAAC6G,UAAU,EAAE2E,WAAW,EAAEiC,IAAI,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC/D,MAAML,UAAU,GAAG,EAAAI,qBAAA,GAAApH,SAAS,CAACO,UAAU,CAAC,cAAA6G,qBAAA,uBAArBA,qBAAA,CAAuBH,IAAI,CACzCK,CAAC,IAAKA,CAAC,CAACpC,WAAW,KAAKA,WAAW,IAAIoC,CAAC,CAACxB,MAAM,KAAKqB,IACzD,CAAC,KAAI,KAAK;;IAEV;IACA,IAAIH,UAAU,IAAI,CAACtJ,aAAa,CAAC8C,QAAQ,CAACD,UAAU,CAAC,IAAI,EAAA8G,sBAAA,GAAArH,SAAS,CAACO,UAAU,CAAC,cAAA8G,sBAAA,uBAArBA,sBAAA,CAAuBE,MAAM,MAAK,CAAC,EAAE;MAC1FhB,sBAAsB,CAACnJ,OAAO,CAAC+D,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAOyG,UAAU;EACrB,CAAC,EAAE,CAAChH,SAAS,EAAEtC,aAAa,CAAC,CAAC;EAE9B,MAAM8J,kBAAkB,GAAG9N,WAAW,CAAE6G,UAAU,IAAK;IAAA,IAAAkH,qBAAA;IACnD,MAAMC,OAAO,GAAG5H,SAAS,CAAC8F,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACtF,UAAU,KAAKA,UAAU,CAAC;IACtE,MAAMoH,OAAO,GAAG,CAAAD,OAAO,aAAPA,OAAO,wBAAAD,qBAAA,GAAPC,OAAO,CAAErC,aAAa,cAAAoC,qBAAA,uBAAtBA,qBAAA,CAAwBnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAI,EAAE;;IAEnE;IACA,IAAIqB,OAAO,IAAI,CAACjK,aAAa,CAAC8C,QAAQ,CAACD,UAAU,CAAC,EAAE;MAChDgG,sBAAsB,CAACnJ,OAAO,CAAC+D,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAOoH,OAAO;EAClB,CAAC,EAAE,CAAC7H,SAAS,EAAEpC,aAAa,CAAC,CAAC;;EAE9B;EACA;EACA;EACA;EACA;;EAEAlE,SAAS,CAAC,MAAM;IACZ,IAAIuC,SAAS,EAAE;MACXwD,aAAa,CAACxD,SAAS,CAAC6E,MAAM,CAAEgH,QAAQ,IAAKA,QAAQ,CAACtC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/E7F,aAAa,CAAC1D,SAAS,CAAC6E,MAAM,CAAEgH,QAAQ,IAAKA,QAAQ,CAACtC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/E3F,cAAc,CAAC5D,SAAS,CAAC6E,MAAM,CAAEgH,QAAQ,IAAKA,QAAQ,CAACtC,cAAc,KAAK,KAAK,CAAC,CAAC;IACrF;EACJ,CAAC,EAAE,CAACvJ,SAAS,CAAC,CAAC;EAEfvC,SAAS,CAAC,MAAM;IACZ;IACA,IAAI,CAACqO,KAAK,CAACC,OAAO,CAAC9L,OAAO,CAAC,IAAIA,OAAO,CAACuL,MAAM,KAAK,CAAC,EAAE;IAErD,MAAMQ,EAAE,GAAG,EAAE;IACb,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,KAAK,MAAMnC,MAAM,IAAI9J,OAAO,EAAE;MAC1B,IAAI8J,MAAM,CAACR,cAAc,KAAK,IAAI,EAAE;QAChCyC,EAAE,CAAC9B,IAAI,CAACH,MAAM,CAAC;MACnB,CAAC,MAAM,IAAIA,MAAM,CAACR,cAAc,KAAK,KAAK,EAAE;QACxC0C,GAAG,CAAC/B,IAAI,CAACH,MAAM,CAAC;MACpB,CAAC,MAAM,IAAIA,MAAM,CAACR,cAAc,KAAK,IAAI,IAAIQ,MAAM,CAACT,aAAa,EAAE;QAC/D,IAAI;UACA,MAAM6C,MAAM,GAAGlJ,IAAI,CAACC,KAAK,CAAC6G,MAAM,CAACT,aAAa,CAAC;UAC/C4C,KAAK,CAACnC,MAAM,CAACvF,UAAU,CAAC,GAAG2H,MAAM;QACrC,CAAC,CAAC,OAAO7G,GAAG,EAAE;UACVC,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAEvB,GAAG,CAAC;QACrD;MACJ;IACJ;IAEAxB,WAAW,CAACkI,EAAE,CAAC;IACfhI,YAAY,CAACiI,GAAG,CAAC;IACjB/H,YAAY,CAACgI,KAAK,CAAC;;IAEnB;IACA;EACJ,CAAC,EAAE,CAACjM,OAAO,CAAC,CAAC;EAGbxC,SAAS,CAAC,MAAM;IACZ,IAAIgD,SAAS,EAAE;MACXd,QAAQ,CAACzB,qBAAqB,CAACuC,SAAS,CAAC,CAAC;IAC9C;EACJ,CAAC,EAAE,CAACd,QAAQ,EAAEc,SAAS,CAAC,CAAC;EAEzBhD,SAAS,CAAC,MAAM;IACZ,IAAI,EAACoC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmH,YAAY,KAAItF,aAAa,KAAK,IAAI,IAAI,CAACnB,OAAO,EAAE;;IAE/D;IACA,MAAM6L,iBAAiB,GAAIC,IAAI,IAAK;MAChC;MACA,IAAIA,IAAI,KAAK,GAAG,IAAI,CAACnK,gBAAgB,CAACE,WAAW,EAAE;QAC/CD,mBAAmB,CAAC6C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE5C,WAAW,EAAE;QAAK,CAAC,CAAC,CAAC;QAC7DG,iBAAiB,CAAC,IAAI,CAAC;QACvB5C,QAAQ,CAAC5B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;QACAoK,UAAU,CAAC,MAAM;UACb5F,iBAAiB,CAAC,KAAK,CAAC;QAC5B,CAAC,EAAE,KAAK,CAAC;MACb;;MAEA;MACA,IAAI8J,IAAI,KAAK,EAAE,IAAI,CAACnK,gBAAgB,CAACG,SAAS,EAAE;QAC5CF,mBAAmB,CAAC6C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE3C,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC;QAC3DE,iBAAiB,CAAC,IAAI,CAAC;QACvB5C,QAAQ,CAAC5B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;MACJ;IACJ,CAAC;;IAED;IACA,MAAMuO,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACvC,IAAI9L,SAAS,EAAE;QACXd,QAAQ,CAACX,gBAAgB,CAAC;UAAEU,MAAM;UAAEe;QAAU,CAAC,CAAC,CAAC,CAC5C+G,IAAI,CAAEjB,MAAM,IAAK;UAAA,IAAAiG,gBAAA;UACd,IAAI,EAAAA,gBAAA,GAAAjG,MAAM,CAACmB,OAAO,cAAA8E,gBAAA,uBAAdA,gBAAA,CAAgB9K,aAAa,MAAKiG,SAAS,EAAE;YAC7ChI,QAAQ,CAACf,gBAAgB,CAAC2H,MAAM,CAACmB,OAAO,CAAChG,aAAa,CAAC,CAAC;UAC5D;QACJ,CAAC,CAAC,CACDkG,KAAK,CAAEf,KAAK,IAAK;UACdtB,OAAO,CAACsB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACtD,CAAC,CAAC;MACV;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,MAAM4F,QAAQ,GAAGF,WAAW,CAAC,MAAM;MAC/B5M,QAAQ,CAACf,gBAAgB,CAAEoG,IAAI,IAAK;QAChC,IAAIA,IAAI,IAAI,CAAC,EAAE;UAAE;UACb0H,aAAa,CAACD,QAAQ,CAAC;UACvBC,aAAa,CAACJ,gBAAgB,CAAC;UAC/B;UACAhJ,WAAW,CAAC,IAAI,CAAC;UACjBf,iBAAiB,CAAC,KAAK,CAAC;UACxB;UACAsF,gBAAgB,CAAC,CAAC;UAClB,OAAO,CAAC;QACZ;;QAEA;QACAuE,iBAAiB,CAACpH,IAAI,CAAC;QAEvB,OAAOA,IAAI,GAAG,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACT0H,aAAa,CAACD,QAAQ,CAAC;MACvBC,aAAa,CAACJ,gBAAgB,CAAC;IACnC,CAAC;EACL,CAAC,EAAE,CAACzM,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmH,YAAY,EAAEzG,OAAO,EAAEmB,aAAa,EAAEQ,gBAAgB,EAAEvC,QAAQ,EAAEc,SAAS,EAAEf,MAAM,CAAC,CAAC,CAAC;;EAEhG;;EAEA;EACAjC,SAAS,CAAC,MAAM;IACZ,IAAI,CAACgD,SAAS,IAAI,EAACe,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsD,EAAE,KAAI,CAACpF,MAAM,IAAIe,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKkH,SAAS,EAAE;IACzF,IAAI,EAAC9H,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8G,sBAAsB,GAAE;IACnCpB,OAAO,CAACmB,GAAG,CAAC,kCAAkC,CAAC;IAG/C,MAAMiG,UAAU,GAAG,IAAI1K,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM2K,OAAO,GAAGA,CAACC,GAAG,EAAEnF,OAAO,KAAK;MAC9B,IAAI,EAAC7H,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8G,sBAAsB,KAAIgG,UAAU,CAACzH,GAAG,CAAC2H,GAAG,CAAC,EAAE;MAE1DF,UAAU,CAACvH,GAAG,CAACyH,GAAG,CAAC;;MAEnB;MACAlN,QAAQ,CAACV,eAAe,CAAC;QACrBS,MAAM;QACNe,SAAS;QACTqM,YAAY,EAAEpF,OAAO,CAAC0B,IAAI,IAAI,eAAe;QAC7C2D,OAAO,EAAE;UACL,GAAGrF,OAAO;UACVsF,IAAI,EAAExL,IAAI,CAACyL,QAAQ,GAAG,GAAG,GAAGzL,IAAI,CAAC0L;QACrC;MACJ,CAAC,CAAC,CAAC;MAEH/E,UAAU,CAAC,MAAMwE,UAAU,CAACxH,MAAM,CAAC0H,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC;;IAED;IACA,MAAMM,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAAChJ,QAAQ,CAACiJ,iBAAiB,IAC3B,CAACjJ,QAAQ,CAACkJ,uBAAuB,IACjC,CAAClJ,QAAQ,CAACmJ,oBAAoB,IAC9B,CAACnJ,QAAQ,CAACoJ,mBAAmB,EAAE;QAC/BX,OAAO,CAAC,iBAAiB,EAAE;UACvBY,SAAS,EAAEhM,IAAI,CAACsD,EAAE;UAClBrE,SAAS;UACTf,MAAM;UACN+N,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,iBAAiB;UACzBC,MAAM,EAAE1K,IAAI,CAACkH,SAAS,CAAC;YAAEyD,MAAM,EAAE;UAA8B,CAAC;QACpE,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAI1J,QAAQ,CAAC2J,eAAe,KAAK,QAAQ,EAAE;QACvClB,OAAO,CAAC,UAAU,EAAE;UAChBY,SAAS,EAAEhM,IAAI,CAACsD,EAAE;UAClBrE,SAAS;UACTf,MAAM;UACN+N,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,UAAU;UAClBC,MAAM,EAAE1K,IAAI,CAACkH,SAAS,CAAC;YAAEpD,OAAO,EAAE;UAAwC,CAAC;QAC/E,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMgH,UAAU,GAAGA,CAAA,KAAM;MACrBnB,OAAO,CAAC,eAAe,EAAE;QACrBY,SAAS,EAAEhM,IAAI,CAACsD,EAAE;QAClBrE,SAAS;QACTf,MAAM;QACN+N,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAE1K,IAAI,CAACkH,SAAS,CAAC;UAAEpD,OAAO,EAAE;QAAsB,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC;;IAED;IACA,MAAMiH,mBAAmB,GAAI3J,CAAC,IAAK;MAC/B,MAAM4J,cAAc,GAAG,CACnB,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CACjE;MACD,MAAMC,KAAK,MAAA3M,MAAA,CAAM8C,CAAC,CAAC8J,OAAO,GAAG,OAAO,GAAG,EAAE,EAAA5M,MAAA,CAAG8C,CAAC,CAAC+J,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAA7M,MAAA,CAAG8C,CAAC,CAACgK,MAAM,GAAG,MAAM,GAAG,EAAE,EAAA9M,MAAA,CAAG8C,CAAC,CAACiK,OAAO,GAAG,OAAO,GAAG,EAAE,EAAA/M,MAAA,CAAG8C,CAAC,CAACwI,GAAG,CAAE;MAEpI,IACIoB,cAAc,CAACxJ,QAAQ,CAACJ,CAAC,CAACwI,GAAG,CAAC,IAC9BqB,KAAK,KAAK,cAAc,IACxBA,KAAK,KAAK,cAAc,EAC1B;QACEtB,OAAO,QAAArL,MAAA,CAAQ2M,KAAK,GAAI;UACpBV,SAAS,EAAEhM,IAAI,CAACsD,EAAE;UAClBrE,SAAS;UACTf,MAAM;UACN+N,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,gBAAgB;UACxBC,MAAM,EAAE1K,IAAI,CAACkH,SAAS,CAAC;YAAE0C,GAAG,EAAExI,CAAC,CAACwI,GAAG;YAAEY,IAAI,EAAEpJ,CAAC,CAACoJ,IAAI;YAAES;UAAM,CAAC;QAC9D,CAAC,CAAC;MACN;IACJ,CAAC;IAED/J,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE+I,sBAAsB,CAAC;IACrEhJ,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEyJ,sBAAsB,CAAC;IACrE1J,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAE2J,UAAU,CAAC;IAC7C5J,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE4J,mBAAmB,CAAC;IAEzD,OAAO,MAAM;MACT7J,QAAQ,CAACoK,mBAAmB,CAAC,kBAAkB,EAAEpB,sBAAsB,CAAC;MACxEhJ,QAAQ,CAACoK,mBAAmB,CAAC,kBAAkB,EAAEV,sBAAsB,CAAC;MACxE1J,QAAQ,CAACoK,mBAAmB,CAAC,MAAM,EAAER,UAAU,CAAC;MAChD5J,QAAQ,CAACoK,mBAAmB,CAAC,SAAS,EAAEP,mBAAmB,CAAC;IAChE,CAAC;EACL,CAAC,EAAE,CAACxM,IAAI,CAACsD,EAAE,EAAEpF,MAAM,EAAEe,SAAS,CAAC,CAAC;EAGhChD,SAAS,CAAC,MAAM;IACZ;IACA;IACA;IACA;EAAA,CACH,EAAE,CAACoC,IAAI,EAAEH,MAAM,EAAEE,QAAQ,EAAED,QAAQ,EAAE4E,WAAW,EAAEK,cAAc,EAAEF,mBAAmB,EAAEC,gBAAgB,CAAC,CAAC;EAE1GlH,SAAS,CAAC,MAAM;IACZsF,YAAY,CAACyL,OAAO,CAAC,YAAY,EAAEvL,IAAI,CAACkH,SAAS,CAACvH,UAAU,CAAC,CAAC;EAClE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM6L,aAAa,GAAG9Q,WAAW,CAAE0G,CAAC,IAAK;IACrC;IACA,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAACI,QAAQ,CAACJ,CAAC,CAACwI,GAAG,CAAC,EAAE;MACrE;MACAxI,CAAC,CAACC,cAAc,CAAC,CAAC;;MAElB;MACA,IAAI,CAACtE,SAAS,IAAIA,SAAS,CAACwL,MAAM,KAAK,CAAC,EAAE;MAE1C,MAAMkD,YAAY,GAAG,CAAC,GAAGnL,UAAU,EAAE,GAAGE,UAAU,EAAE,GAAGE,WAAW,CAAC;MACnE,MAAMgL,YAAY,GAAGD,YAAY,CAACE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/J,EAAE,KAAKpC,gBAAgB,CAAC;MAE3E,IAAI,CAAC2B,CAAC,CAACwI,GAAG,KAAK,SAAS,IAAIxI,CAAC,CAACwI,GAAG,KAAK,WAAW,KAAK8B,YAAY,GAAG,CAAC,EAAE;QACpE,MAAMG,cAAc,GAAGJ,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC7J,EAAE;QACxDS,OAAO,CAACmB,GAAG,CAAC,+CAA+C,EAAEoI,cAAc,CAAC;QAC5E1G,kBAAkB,CAAC0G,cAAc,CAAC;MACtC,CAAC,MAAM,IAAI,CAACzK,CAAC,CAACwI,GAAG,KAAK,WAAW,IAAIxI,CAAC,CAACwI,GAAG,KAAK,YAAY,KAAK8B,YAAY,GAAGD,YAAY,CAAClD,MAAM,GAAG,CAAC,EAAE;QACpG,MAAMuD,cAAc,GAAGL,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC7J,EAAE;QACxDS,OAAO,CAACmB,GAAG,CAAC,kDAAkD,EAAEqI,cAAc,CAAC;QAC/E3G,kBAAkB,CAAC2G,cAAc,CAAC;MACtC;IACJ;EACJ,CAAC,EAAE,CAAC/O,SAAS,EAAEuD,UAAU,EAAEE,UAAU,EAAEE,WAAW,EAAEjB,gBAAgB,EAAE0F,kBAAkB,CAAC,CAAC;EAC1F;EACA3K,SAAS,CAAC,MAAM;IACZ0G,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEqK,aAAa,CAAC;IACnD,OAAO,MAAM;MACTtK,QAAQ,CAACoK,mBAAmB,CAAC,SAAS,EAAEE,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACInP,OAAA;IAAK0P,SAAS,0BAAAzN,MAAA,CAA0BqB,UAAU,GAAG,yBAAyB,GAAG,uBAAuB,CAAG;IAAAqM,QAAA,gBACvG3P,OAAA,CAACjC,gBAAgB;MAAC6R,QAAQ,EAAErP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmN,IAAK;MAACmC,gBAAgB,EAAE9J,oBAAqB;MAACzC,UAAU,EAAE,CAACA;IAAW;MAAAwM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1GhP,OAAO,gBACJjB,OAAA;MAAK0P,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAEhF3P,OAAA,CAACd,WAAW;QACRgR,GAAG,EAAEpO,cAAe;QACpBqO,QAAQ,EAAE5N,WAAY;QACtBe,UAAU,EAAEA,UAAW;QACvBW,UAAU,EAAEA,UAAW;QACvBE,UAAU,EAAEA,UAAW;QACvBE,WAAW,EAAEA,WAAY;QACzB+L,QAAQ,EAAE;UACNxG,oBAAoB;UACpBO,oBAAoB;UACpBW,qBAAqB;UACrBY,YAAY;UACZG,WAAW;UACXM,kBAAkB;UAClBkE,cAAc,EAAEA,CAAC7K,EAAE,EAAE8K,EAAE,KAAMtP,YAAY,CAACe,OAAO,CAACyD,EAAE,CAAC,GAAG8K,EAAG;UAC3DjN,mBAAmB,EAAGmC,EAAE,IAAKnC,mBAAmB,CAACmC,EAAE;QACvD,CAAE;QACF+K,QAAQ,EAAE;UACNnN,gBAAgB;UAChBE,UAAU;UACV1C,QAAQ;UACRE,SAAS;UACToC,iBAAiB;UACjBC,iBAAiB;UACjBY,QAAQ;UACRtB,eAAe;UACfgD;QACJ,CAAE;QACF1B,QAAQ,EAAEA;QACV;QAAA;QACAyM,iBAAiB,EAAEnI,SAAU;QAC7BE,gBAAgB,EAAEA,gBAAiB;QACnC1E,aAAa,EAAEA;MAAc;QAAAiM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAIFjQ,OAAA;QAAK0P,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClD3P,OAAA;UACI0P,SAAS,gCAAAzN,MAAA,CAAgCqB,UAAU,GAAG,wBAAwB,GAAG,qBAAqB,CAAG;UACzGmN,OAAO,EAAEA,CAAA,KAAMlP,gBAAgB,CAACmE,IAAI,IAAI,CAACA,IAAI,CAAE;UAAAiK,QAAA,eAE/C3P,OAAA,CAAChB,IAAI;YAAA8Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGNjQ,OAAA,CAACjB,eAAe;QAAA4Q,QAAA,EACX,CAACrO,aAAa,IAAIiI,MAAM,CAACmH,UAAU,GAAG,IAAI,kBACvC1Q,OAAA,CAACf,WAAW;UACRqE,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7B3C,QAAQ,EAAEA,QAAS;UACnBuF,oBAAoB,EAAEA,oBAAqB;UAC3CrF,SAAS,EAAEA,SAAU;UACrByF,qBAAqB,EAAEA,qBAAsB;UAC7CtC,UAAU,EAAEA,UAAW;UACvBE,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzBsM,gBAAgB,EAAE7H,kBAAmB;UACrC1F,gBAAgB,EAAEA,gBAAiB;UACnCX,eAAe,EAAEA,eAAgB;UACjCgD,kBAAkB,EAAEA,kBAAmB;UACvC8C,gBAAgB,EAAEA,gBAAiB;UACnC1E,aAAa,EAAEA,aAAc;UAC7B+M,eAAe,EAAErO,WAAY;UAC7BhC,IAAI,EAAEA,IAAK;UACX6B,aAAa,EAAEA,aAAc;UAC7BoE,UAAU,EAAEA,UAAW;UACvB9F,SAAS,EAAEA,SAAU;UACrBmQ,kBAAkB,EAAE,EAAA1Q,qBAAA,GAAA2B,cAAc,CAACC,OAAO,cAAA5B,qBAAA,uBAAtBA,qBAAA,CAAwB4I,oBAAoB,CAAC,CAAC,KAAI,KAAM;UAC5E+H,qBAAqB,EAAGxK,KAAK,IAAK;YAC9B,IAAIxE,cAAc,CAACC,OAAO,EAAE;cACxB;cACAD,cAAc,CAACC,OAAO,CAAC+O,qBAAqB,CAACxK,KAAK,CAAC;YACvD;UACJ;QAAE;UAAAwJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CAAC,gBAENjQ,OAAA;MAAK0P,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC7C3P,OAAA,CAAClB,mBAAmB;QAChBiS,OAAO,EAAEA,CAAA,KAAM;UACX;UACA,IAAI5P,SAAS,EAAE;YACXd,QAAQ,CAACR,SAAS,CAAC;cAAEO,MAAM;cAAEe;YAAU,CAAC,CAAC,CAAC;UAC9C;UACAb,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACxC,CAAE;QACF4Q,MAAM,EAAE,CAAC/P,OAAQ;QACjBgQ,WAAW,EAAEjK;MAAiB;QAAA8I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA,CAAA1P,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmH,YAAY,KAAIzG,OAAO,iBAC1BjB,OAAA;MAAK0P,SAAS,qEAAAzN,MAAA,CACRe,cAAc,GACV,0BAA0B,GAC1B,4BAA4B,yEACc;MAAA2M,QAAA,eAChD3P,OAAA;QAAK0P,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpC3P,OAAA;UAAK0P,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAAEnJ,UAAU,CAACpE,aAAa,CAAC,EAAC,UAAK;QAAA;UAAA0N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEA,CAAC;AAEd,CAAC;AAAC/P,EAAA,CA32BID,UAAU;EAAA,QACOzB,SAAS,EACXR,WAAW,EACXW,WAAW,EACXV,WAAW,EACNA,WAAW,EACbA,WAAW,EA4BdA,WAAW,EAC0CA,WAAW;AAAA;AAAAiT,EAAA,GAnC/EjR,UAAU;AA62BhB,eAAeA,UAAU;AAAC,IAAAiR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}