{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\admin\\\\exam\\\\PreviewExamAdmin.jsx\",\n  _s = $RefreshSig$();\nimport AdminLayout from \"../../../layouts/AdminLayout\";\nimport FunctionBarAdmin from \"../../../components/bar/FunctionBarAdmin\";\nimport { useParams } from \"react-router-dom\";\nimport { fetchExamQuestions } from \"../../../features/question/questionSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport AdminModal from \"../../../components/modal/AdminModal\";\nimport AddQuestionModal from \"../../../components/modal/AddQuestionModal\";\nimport { setIsAddView } from \"../../../features/filter/filterSlice\";\nimport PreviewExam from \"../../../components/detail/PreviewExam\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PreviewExamAdmin = () => {\n  _s();\n  const {\n    examId\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    questions\n  } = useSelector(state => state.questions);\n  const {\n    exam\n  } = useSelector(state => state.exams);\n  useEffect(() => {\n    dispatch(fetchExamQuestions({\n      id: examId,\n      pageSize: 1000,\n      sortOrder: 'desc'\n    }));\n  }, [dispatch, examId]);\n  const handleClickedDetail = () => {\n    navigate(\"/admin/exam-management/\".concat(examId));\n  };\n  const handleClickedQuestions = () => {\n    navigate(\"/admin/exam-management/\".concat(examId, \"/questions\"));\n  };\n  const handleClickedTracking = () => {\n    navigate(\"/admin/exam-management/\".concat(examId, \"/tracking\"));\n  };\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-4 h-full w-full overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2 items-center border-b border-[#E7E7ED]\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate(-1),\n          className: \"flex items-center justify-center w-10 h-10 hover:bg-[#F6FAFD] rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"32\",\n            height: \"32\",\n            viewBox: \"0 0 32 32\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12.6667 8.66675L5.50292 15.8289C5.38989 15.94 5.33337 16.0856 5.33337 16.2312M12.6667 23.3334L5.50292 16.6335C5.38989 16.5224 5.33337 16.3768 5.33337 16.2312M5.33337 16.2312H26.6667\",\n              stroke: \"#131214\",\n              strokeWidth: \"1.5\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose\",\n          children: [\"Chi ti\\u1EBFt \\u0111\\u1EC1 thi - \", examId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2 items-center border-b border-[#E7E7ED]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: handleClickedDetail,\n          className: \"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose cursor-pointer\",\n          children: \"Chi ti\\u1EBFt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-[#090a0a]\\\"}\",\n          children: \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: handleClickedQuestions,\n          className: \"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose cursor-pointer\",\n          children: \"Danh s\\xE1ch c\\xE2u h\\u1ECFi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-[#090a0a]\\\"}\",\n          children: \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative justify-center text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-gray-500 underline\",\n          children: \"Xem \\u0111\\u1EC1 thi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-[#090a0a]\\\"}\",\n          children: \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: handleClickedTracking,\n          className: \"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose cursor-pointer\",\n          children: \"Theo d\\xF5i\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(PreviewExam, {\n        exam: exam,\n        questions: questions\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 9\n  }, this);\n};\n_s(PreviewExamAdmin, \"7E8yriHXLDYx8Fsr0XylTvXL3nY=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector, useSelector];\n});\n_c = PreviewExamAdmin;\nexport default PreviewExamAdmin;\nvar _c;\n$RefreshReg$(_c, \"PreviewExamAdmin\");", "map": {"version": 3, "names": ["AdminLayout", "FunctionBarAdmin", "useParams", "fetchExamQuestions", "useNavigate", "AdminModal", "AddQuestionModal", "setIsAddView", "PreviewExam", "useDispatch", "useSelector", "useEffect", "jsxDEV", "_jsxDEV", "PreviewExamAdmin", "_s", "examId", "navigate", "dispatch", "questions", "state", "exam", "exams", "id", "pageSize", "sortOrder", "handleClickedDetail", "concat", "handleClickedQuestions", "handleClickedTracking", "children", "className", "onClick", "xmlns", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/admin/exam/PreviewExamAdmin.jsx"], "sourcesContent": ["import AdminLayout from \"../../../layouts/AdminLayout\";\r\nimport FunctionBarAdmin from \"../../../components/bar/FunctionBarAdmin\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { fetchExamQuestions } from \"../../../features/question/questionSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AdminModal from \"../../../components/modal/AdminModal\";\r\nimport AddQuestionModal from \"../../../components/modal/AddQuestionModal\";\r\nimport { setIsAddView } from \"../../../features/filter/filterSlice\";\r\nimport PreviewExam from \"../../../components/detail/PreviewExam\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useEffect } from \"react\";\r\n\r\nconst PreviewExamAdmin = () => {\r\n    const { examId } = useParams();\r\n    const navigate = useNavigate();\r\n    const dispatch = useDispatch();\r\n    const { questions } = useSelector((state) => state.questions);\r\n    const { exam } = useSelector((state) => state.exams);\r\n\r\n    useEffect(() => {\r\n        dispatch(fetchExamQuestions({ id: examId, pageSize: 1000, sortOrder: 'desc' }));\r\n    }, [dispatch, examId]);\r\n\r\n    const handleClickedDetail = () => {\r\n        navigate(`/admin/exam-management/${examId}`);\r\n    }\r\n    const handleClickedQuestions = () => {\r\n        navigate(`/admin/exam-management/${examId}/questions`);\r\n    }\r\n    const handleClickedTracking = () => {\r\n        navigate(`/admin/exam-management/${examId}/tracking`);\r\n    }\r\n    return (\r\n        <AdminLayout>\r\n            <div className=\"flex flex-col gap-4 h-full w-full overflow-hidden\">\r\n                <div className=\"flex gap-2 items-center border-b border-[#E7E7ED]\">\r\n                    <button onClick={() => navigate(-1)} className=\"flex items-center justify-center w-10 h-10 hover:bg-[#F6FAFD] rounded-lg\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\r\n                            <path d=\"M12.6667 8.66675L5.50292 15.8289C5.38989 15.94 5.33337 16.0856 5.33337 16.2312M12.6667 23.3334L5.50292 16.6335C5.38989 16.5224 5.33337 16.3768 5.33337 16.2312M5.33337 16.2312H26.6667\" stroke=\"#131214\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\r\n                        </svg>\r\n                    </button>\r\n                    <div className=\"relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose\">Chi tiết đề thi - {examId}</div>\r\n                </div>\r\n                <div className=\"flex gap-2 items-center border-b border-[#E7E7ED]\">\r\n                    <div\r\n                        onClick={handleClickedDetail}\r\n                        className={`relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose cursor-pointer`}>\r\n                        Chi tiết\r\n                    </div>\r\n                    <div\r\n                        className={`relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-[#090a0a]\"}`}>\r\n                        -\r\n                    </div>\r\n                    <div\r\n                        onClick={handleClickedQuestions}\r\n                        className={`relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose cursor-pointer`}>\r\n                        Danh sách câu hỏi\r\n                    </div>\r\n                    <div\r\n                        className={`relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-[#090a0a]\"}`}>\r\n                        -\r\n                    </div>\r\n                    <div\r\n                        className={`relative justify-center text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-gray-500 underline`}>\r\n                        Xem đề thi\r\n                    </div>\r\n                    <div\r\n                        className={`relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose text-[#090a0a]\"}`}>\r\n                        -\r\n                    </div>\r\n                    <div\r\n                        onClick={handleClickedTracking}\r\n                        className={`relative justify-center text-[#090a0a] text-2xl font-bold font-['Be_Vietnam_Pro'] leading-loose cursor-pointer`}>\r\n                        Theo dõi\r\n                    </div>\r\n                </div>\r\n                <PreviewExam exam={exam} questions={questions} />\r\n            </div>\r\n        </AdminLayout>\r\n    )\r\n}\r\n\r\nexport default PreviewExamAdmin;\r\n"], "mappings": ";;AAAA,OAAOA,WAAW,MAAM,8BAA8B;AACtD,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,UAAU,MAAM,sCAAsC;AAC7D,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,SAASC,YAAY,QAAQ,sCAAsC;AACnE,OAAOC,WAAW,MAAM,wCAAwC;AAChE,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAO,CAAC,GAAGd,SAAS,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAU,CAAC,GAAGT,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EAC7D,MAAM;IAAEE;EAAK,CAAC,GAAGX,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACE,KAAK,CAAC;EAEpDX,SAAS,CAAC,MAAM;IACZO,QAAQ,CAACf,kBAAkB,CAAC;MAAEoB,EAAE,EAAEP,MAAM;MAAEQ,QAAQ,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAO,CAAC,CAAC,CAAC;EACnF,CAAC,EAAE,CAACP,QAAQ,EAAEF,MAAM,CAAC,CAAC;EAEtB,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAC9BT,QAAQ,2BAAAU,MAAA,CAA2BX,MAAM,CAAE,CAAC;EAChD,CAAC;EACD,MAAMY,sBAAsB,GAAGA,CAAA,KAAM;IACjCX,QAAQ,2BAAAU,MAAA,CAA2BX,MAAM,eAAY,CAAC;EAC1D,CAAC;EACD,MAAMa,qBAAqB,GAAGA,CAAA,KAAM;IAChCZ,QAAQ,2BAAAU,MAAA,CAA2BX,MAAM,cAAW,CAAC;EACzD,CAAC;EACD,oBACIH,OAAA,CAACb,WAAW;IAAA8B,QAAA,eACRjB,OAAA;MAAKkB,SAAS,EAAC,mDAAmD;MAAAD,QAAA,gBAC9DjB,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAD,QAAA,gBAC9DjB,OAAA;UAAQmB,OAAO,EAAEA,CAAA,KAAMf,QAAQ,CAAC,CAAC,CAAC,CAAE;UAACc,SAAS,EAAC,0EAA0E;UAAAD,QAAA,eACrHjB,OAAA;YAAKoB,KAAK,EAAC,4BAA4B;YAACC,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAP,QAAA,eAC1FjB,OAAA;cAAMyB,CAAC,EAAC,wLAAwL;cAACC,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC,KAAK;cAACC,aAAa,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3P;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACThC,OAAA;UAAKkB,SAAS,EAAC,iGAAiG;UAAAD,QAAA,GAAC,mCAAkB,EAACd,MAAM;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChJ,CAAC,eACNhC,OAAA;QAAKkB,SAAS,EAAC,mDAAmD;QAAAD,QAAA,gBAC9DjB,OAAA;UACImB,OAAO,EAAEN,mBAAoB;UAC7BK,SAAS,kHAAmH;UAAAD,QAAA,EAAC;QAEjI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhC,OAAA;UACIkB,SAAS,qHAAqH;UAAAD,QAAA,EAAC;QAEnI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhC,OAAA;UACImB,OAAO,EAAEJ,sBAAuB;UAChCG,SAAS,kHAAmH;UAAAD,QAAA,EAAC;QAEjI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhC,OAAA;UACIkB,SAAS,qHAAqH;UAAAD,QAAA,EAAC;QAEnI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhC,OAAA;UACIkB,SAAS,4GAA6G;UAAAD,QAAA,EAAC;QAE3H;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhC,OAAA;UACIkB,SAAS,qHAAqH;UAAAD,QAAA,EAAC;QAEnI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhC,OAAA;UACImB,OAAO,EAAEH,qBAAsB;UAC/BE,SAAS,kHAAmH;UAAAD,QAAA,EAAC;QAEjI;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNhC,OAAA,CAACL,WAAW;QAACa,IAAI,EAAEA,IAAK;QAACF,SAAS,EAAEA;MAAU;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAA9B,EAAA,CApEKD,gBAAgB;EAAA,QACCZ,SAAS,EACXE,WAAW,EACXK,WAAW,EACNC,WAAW,EAChBA,WAAW;AAAA;AAAAoC,EAAA,GAL1BhC,gBAAgB;AAsEtB,eAAeA,gBAAgB;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}