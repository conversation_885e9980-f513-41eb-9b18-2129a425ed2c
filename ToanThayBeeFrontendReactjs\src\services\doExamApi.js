import api from "./api";

export const joinExamApi = async (examId) => {
    return await api.get(`/v1/user/join-exam/${examId}`);
}

export const submitAnswerApi = async ({ questionId, answerContent, type }) => {
    return await api.post('/v1/user/submit-answer', { questionId, answerContent, type });
};

export const calculateScoreApi = async ({ attemptId, answers }) => {
    return await api.post(`/v1/user/calculate-score/${attemptId}`, { answers });
};

export const summitExamAPI = async ({ attemptId }) => {
    return await api.post(`/v1/user/submit-exam`, { attemptId });
}