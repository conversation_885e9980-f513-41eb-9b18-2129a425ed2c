# 🚀 Debounce Feature for Exam Answers

## 📋 Overview
Đã thêm tính năng debounce để tối ưu hóa việc lưu câu trả lời trong quá trình làm bài thi, gi<PERSON><PERSON> thiểu số lượng API calls và cải thiện trải nghiệm người dùng.

## ⚡ Benefits

### 🔄 Performance Improvements
- **Giảm API calls**: Thay vì gọi API mỗi lần user thay đổi answer, chỉ gọi sau khi user dừng thao tác
- **Tiết kiệm bandwidth**: Ít request hơn = ít data transfer
- **Giảm tải server**: Server không bị spam requests

### 👤 User Experience
- **Smooth typing**: User có thể gõ liên tục mà không bị lag
- **Visual feedback**: Hiển thị trạng thái "đang lưu" cho user
- **No interruption**: Không gián đoạn quá trình làm bài

## 🎯 Implementation Details

### ⏱️ Debounce Delays
```javascript
// Thời gian debounce khác nhau cho từng loại câu hỏi
TN (Trắc nghiệm):     500ms  // Ngắn vì chỉ click 1 lần
DS (Đúng/Sai):        800ms  // Trung bình vì có thể thay đổi nhiều lần
TLN (Trả lời ngắn): 1500ms  // Dài vì user gõ liên tục
```

### 🔧 Technical Implementation
```javascript
// Tạo debounced functions với error handling
const submitAnswerTNDebounced = useCallback(
    debounce((payload) => {
        // Add to saving state
        setSavingQuestions(prev => new Set(prev).add(payload.questionId));

        // Call API
        dispatch(submitAnswerWithAttempt(payload)).then((result) => {
            // Remove from saving state
            setSavingQuestions(prev => {
                const newSet = new Set(prev);
                newSet.delete(payload.questionId);
                return newSet;
            });

            if (result.type.endsWith('/fulfilled')) {
                // API success - Redux slice tự động:
                // - Add questionId vào saveQuestions
                // - Remove questionId khỏi errorQuestions
            } else {
                // API failed - Redux slice tự động:
                // - Add questionId vào errorQuestions
                // - Remove questionId khỏi saveQuestions
            }
        });
    }, 500),
    [dispatch, attemptId]
);
```

## 📊 State Management

### 🔄 Saving State Tracking
- **savingQuestions**: Set chứa IDs của các câu hỏi đang được lưu
- **Visual indicator**: Hiển thị trạng thái "đang lưu" trên UI
- **Auto cleanup**: Tự động xóa khỏi saving state khi API hoàn thành

### 🎨 UI Indicators
- **Question buttons**: Có thể hiển thị spinner hoặc màu khác khi đang lưu
- **Counter**: "Số câu đang lưu" trong sidebar (đã loại bỏ theo yêu cầu)
- **Progress feedback**: User biết được trạng thái lưu của từng câu

## 🧪 Testing Scenarios

### ✅ Test Cases
1. **Rapid TN selection**: Click nhiều đáp án liên tục → chỉ lưu đáp án cuối
2. **DS multiple changes**: Thay đổi nhiều statement → chỉ lưu trạng thái cuối
3. **TLN continuous typing**: Gõ liên tục → chỉ lưu khi dừng gõ 1.5s
4. **Mixed question types**: Làm nhiều loại câu hỏi cùng lúc → mỗi loại có delay riêng

### 🔍 Expected Behavior
- **No duplicate API calls** cho cùng 1 câu hỏi trong thời gian debounce
- **Latest answer saved** khi có nhiều thay đổi liên tục
- **Proper cleanup** khi component unmount
- **Smart state management**:
  - API success: questionId → `saveQuestions` ✅, remove from `errorQuestions` ❌
  - API failure: questionId → `errorQuestions` ❌, remove from `saveQuestions` ✅
  - Saving process: questionId → `savingQuestions` ⏳ (temporary)

### 📊 State Flow Example
```
User answers Question 1:
1. questionId=1 added to savingQuestions ⏳
2. API call dispatched (debounced)
3. API success → questionId=1 removed from savingQuestions
4. Redux slice automatically:
   - Adds questionId=1 to saveQuestions ✅
   - Removes questionId=1 from errorQuestions (if existed)

User answers Question 2 (API fails):
1. questionId=2 added to savingQuestions ⏳
2. API call dispatched (debounced)
3. API failure → questionId=2 removed from savingQuestions
4. Redux slice automatically:
   - Adds questionId=2 to errorQuestions ❌
   - Removes questionId=2 from saveQuestions (if existed)
```

## 🚨 Error Handling

### 🛡️ Safeguards
- **Cleanup on unmount**: Cancel pending debounced calls
- **Automatic error state management**: Redux slice tự động xử lý success/error states
- **Smart state transitions**:
  - API success → add to `saveQuestions`, remove from `errorQuestions`
  - API failure → add to `errorQuestions`, remove from `saveQuestions`
- **Saving state tracking**: Track questions being saved với `savingQuestions`
- **Fallback behavior**: Đảm bảo answer vẫn được lưu local state

## 🔮 Future Enhancements

### 💡 Possible Improvements
- **Smart debounce**: Điều chỉnh delay dựa trên network speed
- **Batch API calls**: Gộp nhiều answers thành 1 request
- **Offline support**: Cache answers khi mất mạng
- **Real-time sync**: Sync với server khi có kết nối

## 📝 Usage Examples

### 🎯 For Developers
```javascript
// Sử dụng debounced function
handleSelectAnswerTLN(questionId, answerContent, type) {
    // Update local state immediately
    dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));
    
    // Debounced API call
    submitAnswerTLNDebounced({
        questionId,
        answerContent: formattedAnswer,
        type,
        attemptId
    });
}
```

### 👥 For Users
- Gõ câu trả lời tự luận liên tục mà không lo bị lag
- Thay đổi đáp án trắc nghiệm nhiều lần mà không lo spam server
- Nhìn thấy trạng thái "đang lưu" để biết hệ thống đang xử lý

---

## 🎉 Summary
Tính năng debounce đã được implement thành công, mang lại trải nghiệm mượt mà hơn cho user và giảm tải cho server. Hệ thống bây giờ thông minh hơn trong việc quản lý API calls và cung cấp feedback tốt hơn cho người dùng.
