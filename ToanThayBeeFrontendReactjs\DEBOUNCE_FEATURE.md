# 🚀 Debounce Feature for Exam Answers

## 📋 Overview
Đã thêm tính năng debounce để tối ưu hóa việc lưu câu trả lời trong quá trình làm bài thi, gi<PERSON><PERSON> thiểu số lượng API calls và cải thiện trải nghiệm người dùng.

## ⚡ Benefits

### 🔄 Performance Improvements
- **Giảm API calls**: Thay vì gọi API mỗi lần user thay đổi answer, chỉ gọi sau khi user dừng thao tác
- **Tiết kiệm bandwidth**: Ít request hơn = ít data transfer
- **Giảm tải server**: Server không bị spam requests

### 👤 User Experience
- **Smooth typing**: User có thể gõ liên tục mà không bị lag
- **Visual feedback**: Hiển thị trạng thái "đang lưu" cho user
- **No interruption**: Không gián đoạn quá trình làm bài

## 🎯 Implementation Details

### ⏱️ Debounce Delays
```javascript
// Thời gian debounce khác nhau cho từng loại câu hỏi
TN (Trắc nghiệm):     500ms  // Ngắn vì chỉ click 1 lần
DS (Đúng/Sai):        800ms  // Trung bình vì có thể thay đổi nhiều lần
TLN (Trả lời ngắn): 1500ms  // Dài vì user gõ liên tục
```

### 🔧 Technical Implementation
```javascript
// Tạo debounced functions
const submitAnswerTNDebounced = useCallback(
    debounce((payload) => {
        // Add to saving state
        setSavingQuestions(prev => new Set(prev).add(payload.questionId));
        
        // Call API
        dispatch(submitAnswerWithAttempt(payload)).then((result) => {
            // Remove from saving state
            setSavingQuestions(prev => {
                const newSet = new Set(prev);
                newSet.delete(payload.questionId);
                return newSet;
            });
        });
    }, 500),
    [dispatch, attemptId]
);
```

## 📊 State Management

### 🔄 Saving State Tracking
- **savingQuestions**: Set chứa IDs của các câu hỏi đang được lưu
- **Visual indicator**: Hiển thị trạng thái "đang lưu" trên UI
- **Auto cleanup**: Tự động xóa khỏi saving state khi API hoàn thành

### 🎨 UI Indicators
- **Question buttons**: Có thể hiển thị spinner hoặc màu khác khi đang lưu
- **Counter**: "Số câu đang lưu" trong sidebar (đã loại bỏ theo yêu cầu)
- **Progress feedback**: User biết được trạng thái lưu của từng câu

## 🧪 Testing Scenarios

### ✅ Test Cases
1. **Rapid TN selection**: Click nhiều đáp án liên tục → chỉ lưu đáp án cuối
2. **DS multiple changes**: Thay đổi nhiều statement → chỉ lưu trạng thái cuối
3. **TLN continuous typing**: Gõ liên tục → chỉ lưu khi dừng gõ 1.5s
4. **Mixed question types**: Làm nhiều loại câu hỏi cùng lúc → mỗi loại có delay riêng

### 🔍 Expected Behavior
- **No duplicate API calls** cho cùng 1 câu hỏi trong thời gian debounce
- **Latest answer saved** khi có nhiều thay đổi liên tục
- **Proper cleanup** khi component unmount
- **Error handling** khi API call fails

## 🚨 Error Handling

### 🛡️ Comprehensive Error Management
- **Cleanup on unmount**: Cancel pending debounced calls
- **Error state management**: Handle API failures properly
- **Network error detection**: Phân biệt lỗi mạng và lỗi server
- **User feedback**: Hiển thị thông báo lỗi phù hợp
- **Question state tracking**: Add vào errorQuestions khi API fail
- **Saving state cleanup**: Remove khỏi savingQuestions khi có lỗi

### 🔧 Error Handling Implementation
```javascript
const handleApiError = (error, payload, questionType) => {
    // Remove from saving state
    setSavingQuestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(payload.questionId);
        return newSet;
    });

    // Add to error questions
    addErrorQuestion(payload.questionId);

    // Show appropriate error message
    if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
        dispatch(setErrorMessage("Lỗi mạng khi lưu câu trả lời. Vui lòng kiểm tra kết nối và thử lại."));
    } else {
        dispatch(setErrorMessage("Lỗi khi lưu câu trả lời. Vui lòng thử lại."));
    }
};
```

### 📊 Error Types Handled
1. **Network Errors** (`ERR_NETWORK`): Lỗi kết nối mạng
2. **Server Errors** (5xx): Lỗi từ phía server
3. **Client Errors** (4xx): Lỗi từ phía client
4. **Timeout Errors**: Request timeout
5. **Unknown Errors**: Các lỗi không xác định

### 🎯 Error Recovery
- **Visual indicators**: Button màu đỏ cho câu hỏi lỗi
- **Retry capability**: User có thể thử lại bằng cách thay đổi answer
- **State consistency**: Đảm bảo UI state luôn đồng bộ với server state
- **Graceful degradation**: App vẫn hoạt động khi có lỗi

## 🔮 Future Enhancements

### 💡 Possible Improvements
- **Smart debounce**: Điều chỉnh delay dựa trên network speed
- **Batch API calls**: Gộp nhiều answers thành 1 request
- **Offline support**: Cache answers khi mất mạng
- **Real-time sync**: Sync với server khi có kết nối

## 📝 Usage Examples

### 🎯 For Developers
```javascript
// Sử dụng debounced function
handleSelectAnswerTLN(questionId, answerContent, type) {
    // Update local state immediately
    dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));
    
    // Debounced API call
    submitAnswerTLNDebounced({
        questionId,
        answerContent: formattedAnswer,
        type,
        attemptId
    });
}
```

### 👥 For Users
- Gõ câu trả lời tự luận liên tục mà không lo bị lag
- Thay đổi đáp án trắc nghiệm nhiều lần mà không lo spam server
- Nhìn thấy trạng thái "đang lưu" để biết hệ thống đang xử lý

---

## 🎉 Summary
Tính năng debounce đã được implement thành công, mang lại trải nghiệm mượt mà hơn cho user và giảm tải cho server. Hệ thống bây giờ thông minh hơn trong việc quản lý API calls và cung cấp feedback tốt hơn cho người dùng.
