import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import html2canvas from "html2canvas";
import { find } from "lodash";
import ClassSearchInput from "../../../components/ClassSearchInput";
import UserSearchInput from "../../../components/UserSearchInput";
import {
  fetchTuitionPayments,
  deleteTuitionPayment,
  createTuitionPayment,
  createBatchTuitionPayments,
  fetchTuitionStatistics,
  updateTuitionPayment,
  fetchTuitionPaymentByIdAdmin,
  fetchStudentClassTuitionsByMonthAdmin
} from "src/features/tuition/tuitionSlice";
import { formatCurrency, formatNumberWithDots, parseCurrencyInput } from "src/utils/formatters";
import { findUsers } from "src/features/user/userSlice";
import { findClasses } from "src/features/class/classSlice";
import { setCurrentPage, setSearch, resetFilters } from "src/features/filter/filterSlice";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
import Pagination from "src/components/Pagination";
import ConfirmModal from "src/components/modal/ConfirmDeleteModal";
import { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from "lucide-react";
import AdminLayout from "src/layouts/AdminLayout";
import FunctionBarAdmin from "src/components/bar/FunctionBarAdmin";
import Chart from 'chart.js/auto';

const TuitionPaymentList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);

  const { currentPage, totalPages, totalItems, limit } = useSelector(
    (state) => state.filter
  );
  const [inputValue, setInputValue] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [paymentToDelete, setPaymentToDelete] = useState(null);
  const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên
  const [showRightPanel, setShowRightPanel] = useState(false);
  const [rightPanelType, setRightPanelType] = useState(""); // "batch", "batchByClass", "batchByMonth"

  // State cho bộ lọc
  const [filterMonth, setFilterMonth] = useState("");
  const [filterStatus, setFilterStatus] = useState("");
  const [filterOverdue, setFilterOverdue] = useState("");
  const [filterClass, setFilterClass] = useState("");
  const [filterClassId, setFilterClassId] = useState("");
  const [classSearchTerm, setClassSearchTerm] = useState("");

  const [selectedUserId, setSelectedUserId] = useState("");
  const [userSearchTerm, setUserSearchTerm] = useState("");

  // const [selectedClassId, setSelectedClassId] = useState("");
  // const [classSearchTerm1, setClassSearchTerm1] = useState("");

  // State cho chế độ xem
  const [viewMode, setViewMode] = useState("table"); // "table" hoặc "statistics"
  const [startMonth, setStartMonth] = useState("");
  const [endMonth, setEndMonth] = useState("");

  // Refs cho biểu đồ
  const monthlyChartRef = useRef(null);
  const classChartRef = useRef(null);
  const monthlyChartInstance = useRef(null);
  const classChartInstance = useRef(null);

  // State cho form tạo học phí hàng loạt
  const [batchMonth, setBatchMonth] = useState("");
  const [batchAmount, setBatchAmount] = useState("");
  const [batchAmountFormatted, setBatchAmountFormatted] = useState("");
  const [batchDueDate, setBatchDueDate] = useState("");
  const [batchClass, setBatchClass] = useState("");
  const [batchNote, setBatchNote] = useState("");
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State cho form thêm học phí
  const [addUserId, setAddUserId] = useState("");
  const [addUserSearchTerm, setAddUserSearchTerm] = useState("");
  const [addMonth, setAddMonth] = useState("");
  const [addExpectedAmount, setAddExpectedAmount] = useState("");
  const [addExpectedAmountFormatted, setAddExpectedAmountFormatted] = useState("");
  const [addPaidAmount, setAddPaidAmount] = useState("");
  const [addPaidAmountFormatted, setAddPaidAmountFormatted] = useState("");
  const [addPaymentDate, setAddPaymentDate] = useState("");
  const [addDueDate, setAddDueDate] = useState("");
  const [addStatus, setAddStatus] = useState("");
  const [addNote, setAddNote] = useState("");
  const [addCalculateExpected, setAddCalculateExpected] = useState(false);

  // State cho form chỉnh sửa học phí
  const [editId, setEditId] = useState(null);
  const [editExpectedAmount, setEditExpectedAmount] = useState("");
  const [editExpectedAmountFormatted, setEditExpectedAmountFormatted] = useState("");
  const [editPaidAmount, setEditPaidAmount] = useState("");
  const [editPaidAmountFormatted, setEditPaidAmountFormatted] = useState("");
  const [editPaymentDate, setEditPaymentDate] = useState("");
  const [editStatus, setEditStatus] = useState("");
  const [editNote, setEditNote] = useState("");
  const [calculateExpected, setCalculateExpected] = useState(false);

  // State cho xem chi tiết học phí
  const [viewPayment, setViewPayment] = useState(null);
  const [viewClassTuitions, setViewClassTuitions] = useState(null);
  const [viewLoading, setViewLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const detailsContainerRef = useRef(null);

  useEffect(() => {
    if (!didInit) {
      dispatch(resetFilters());
      setDidInit(true);
    }
  }, [dispatch, didInit]);

  // Hiệu ứng để vẽ biểu đồ khi có dữ liệu thống kê
  useEffect(() => {
    if (viewMode === 'statistics' && tuitionStatistics && monthlyChartRef.current && classChartRef.current) {
      // Hủy biểu đồ cũ nếu có
      if (monthlyChartInstance.current) {
        monthlyChartInstance.current.destroy();
      }
      if (classChartInstance.current) {
        classChartInstance.current.destroy();
      }

      // Chuẩn bị dữ liệu cho biểu đồ theo tháng
      const monthlyData = {
        labels: tuitionStatistics.monthlyStatistics?.map(stat => stat.monthFormatted) || [],
        datasets: [
          {
            label: 'Số tiền cần thu',
            data: tuitionStatistics.monthlyStatistics?.map(stat => stat.totalExpectedAmount) || [],
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
          },
          {
            label: 'Số tiền đã thu',
            data: tuitionStatistics.monthlyStatistics?.map(stat => stat.totalPaidAmount) || [],
            backgroundColor: 'rgba(75, 192, 192, 0.5)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
          }
        ]
      };

      // Chuẩn bị dữ liệu cho biểu đồ theo lớp
      const classData = {
        labels: tuitionStatistics.classStatistics?.map(stat => `Lớp ${stat.userClass}`) || [],
        datasets: [
          {
            label: 'Số tiền cần thu',
            data: tuitionStatistics.classStatistics?.map(stat => stat.totalExpectedAmount) || [],
            backgroundColor: 'rgba(153, 102, 255, 0.5)',
            borderColor: 'rgba(153, 102, 255, 1)',
            borderWidth: 1
          },
          {
            label: 'Số tiền đã thu',
            data: tuitionStatistics.classStatistics?.map(stat => stat.totalPaidAmount) || [],
            backgroundColor: 'rgba(255, 159, 64, 0.5)',
            borderColor: 'rgba(255, 159, 64, 1)',
            borderWidth: 1
          }
        ]
      };

      // Vẽ biểu đồ theo tháng
      const monthlyCtx = monthlyChartRef.current.getContext('2d');
      monthlyChartInstance.current = new Chart(monthlyCtx, {
        type: 'bar',
        data: monthlyData,
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function (value) {
                  return formatCurrency(value);
                }
              }
            }
          },
          plugins: {
            tooltip: {
              callbacks: {
                label: function (context) {
                  return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
                }
              }
            }
          }
        }
      });

      // Vẽ biểu đồ theo lớp
      const classCtx = classChartRef.current.getContext('2d');
      classChartInstance.current = new Chart(classCtx, {
        type: 'bar',
        data: classData,
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function (value) {
                  return formatCurrency(value);
                }
              }
            }
          },
          plugins: {
            tooltip: {
              callbacks: {
                label: function (context) {
                  return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
                }
              }
            }
          }
        }
      });
    }
  }, [viewMode, tuitionStatistics]);

  useEffect(() => {
    dispatch(findClasses(""));
    dispatch(findUsers(""));
  }, [dispatch]);

  // Xử lý khi người dùng chọn một lớp
  const handleSelectClass = (classItem) => {
    setFilterClassId(classItem.id);
    setClassSearchTerm(classItem.name);
  };

  // Xử lý khi người dùng xóa lựa chọn lớp
  const handleClearClassSelection = () => {
    setFilterClassId("");
    setClassSearchTerm("");
  };

  // Xử lý khi người dùng chọn một người dùng
  const handleSelectUser = (userItem) => {
    setSelectedUserId(userItem.id);
    setUserSearchTerm(userItem.firstName + " " + userItem.lastName);
  };

  // Xử lý khi người dùng xóa lựa chọn người dùng
  const handleClearUserSelection = () => {
    setSelectedUserId("");
    setUserSearchTerm("");
  };



  // Hàm xử lý tìm kiếm
  const handleSearch = () => {
    dispatch(
      fetchTuitionPayments({
        search: inputValue,
        page: 1, // Reset về trang 1 khi tìm kiếm
        limit,
        sortOrder: "DESC",
        status: filterStatus,
        month: filterMonth,
        overdue: filterOverdue,
        userClass: filterClass,
        classId: filterClassId
      })
    );
  };

  // Hàm xử lý thêm học phí mới
  const handleAddTuitionSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const errors = {};

    // Validate userId (required)
    if (!addUserId) {
      errors.userId = "Vui lòng chọn học sinh";
    }

    // Validate month (required and format YYYY-MM)
    if (!addMonth) {
      errors.month = "Tháng không được để trống";
    } else if (!/^\d{4}-\d{2}$/.test(addMonth)) {
      errors.month = "Định dạng tháng không hợp lệ";
    }

    // Validate expected amount (required if not calculating automatically)
    if (!addCalculateExpected && !addExpectedAmount) {
      errors.expectedAmount = "Vui lòng nhập số tiền cần đóng hoặc chọn tính tự động";
    } else if (addExpectedAmount && (isNaN(addExpectedAmount) || Number(addExpectedAmount) < 0)) {
      errors.expectedAmount = "Số tiền phải là số dương";
    }

    // Validate paid amount (must be a positive number if provided)
    if (addPaidAmount && (isNaN(addPaidAmount) || Number(addPaidAmount) < 0)) {
      errors.paidAmount = "Số tiền phải là số dương";
    }

    // Validate due date (required)
    if (!addDueDate) {
      errors.dueDate = "Vui lòng chọn hạn thanh toán";
    }

    // Validate status (required)
    if (!addStatus) {
      errors.status = "Vui lòng chọn trạng thái";
    }

    // If there are errors, display them and stop submission
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API call
      const paymentData = {
        userId: addUserId,
        month: addMonth,
        status: addStatus,
        note: addNote,
        dueDate: addDueDate
      };

      // Only include expectedAmount if not calculating automatically
      if (!addCalculateExpected && addExpectedAmount) {
        paymentData.expectedAmount = Number(addExpectedAmount);
      }

      // Include paidAmount if provided
      if (addPaidAmount) {
        paymentData.paidAmount = Number(addPaidAmount);
      }

      // Include paymentDate if provided
      if (addPaymentDate) {
        paymentData.paymentDate = addPaymentDate;
      }

      // Call API to create tuition payment
      await dispatch(createTuitionPayment(paymentData));

      // Close panel and refresh data
      closeRightPanel();
      dispatch(fetchTuitionPayments({
        page: currentPage,
        limit,
        search: inputValue,
        sortOrder: "DESC"
      }));

      // Reset form
      setAddUserId("");
      setAddUserSearchTerm("");
      setAddMonth("");
      setAddExpectedAmount("");
      setAddExpectedAmountFormatted("");
      setAddPaidAmount("");
      setAddPaidAmountFormatted("");
      setAddPaymentDate("");
      setAddDueDate("");
      setAddStatus("");
      setAddNote("");
      setAddCalculateExpected(false);

    } catch (error) {
      console.error("Error creating tuition payment:", error);
      setFormErrors({ submit: "Có lỗi xảy ra khi tạo học phí" });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Hàm xử lý khi thay đổi chế độ xem
  const handleViewModeChange = (mode) => {
    setViewMode(mode);

    // Nếu chuyển sang chế độ thống kê, tải dữ liệu thống kê
    if (mode === 'statistics') {
      // Khởi tạo giá trị mặc định cho startMonth và endMonth nếu chưa có
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      // Nếu chưa có startMonth, đặt là tháng hiện tại của năm trước
      if (!startMonth) {
        const formattedMonth = currentMonth < 10 ? `0${currentMonth}` : `${currentMonth}`;
        setStartMonth(`${currentYear - 1}-${formattedMonth}`);
      }

      // Nếu chưa có endMonth, đặt là tháng hiện tại
      if (!endMonth) {
        const formattedMonth = currentMonth < 10 ? `0${currentMonth}` : `${currentMonth}`;
        setEndMonth(`${currentYear}-${formattedMonth}`);
      }

      // Tải dữ liệu thống kê
      dispatch(fetchTuitionStatistics({
        startMonth: startMonth || `${currentYear - 1}-${currentMonth < 10 ? `0${currentMonth}` : currentMonth}`,
        endMonth: endMonth || `${currentYear}-${currentMonth < 10 ? `0${currentMonth}` : currentMonth}`,
        userClass: filterClass
      }));
    }
  };

  useEffect(() => {
    if (!didInit) return;
    dispatch(
      fetchTuitionPayments({
        search: inputValue,
        page: currentPage, // Reset về trang 1 khi tìm kiếm
        limit,
        sortOrder: "DESC",
        status: filterStatus,
        month: filterMonth,
        overdue: filterOverdue,
        userClass: filterClass
      })
    );
  }, [dispatch, currentPage, limit, didInit]);

  const handleEdit = async (id) => {
    setIsSubmitting(true);
    try {
      // Fetch the payment details
      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();
      const payment = response.data;

      // Set the edit form state
      setEditId(id);
      setEditExpectedAmount(payment.expectedAmount || "");
      setEditExpectedAmountFormatted(payment.expectedAmount ? formatNumberWithDots(payment.expectedAmount) : "");
      setEditPaidAmount(payment.paidAmount || "");
      setEditPaidAmountFormatted(payment.paidAmount ? formatNumberWithDots(payment.paidAmount) : "");
      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : "");
      setEditStatus(payment.status || "");
      setEditNote(payment.note || "");
      setCalculateExpected(false);

      // Show the right panel
      setRightPanelType("edit");
      setShowRightPanel(true);
    } catch (error) {
      console.error("Error fetching payment details:", error);
    } finally {
      setIsSubmitting(false);
    }
  };



  const handleView = async (id, userId, month) => {
    setViewLoading(true);
    try {
      // Fetch the payment details
      await dispatch(fetchTuitionPaymentByIdAdmin(id))

      // Fetch the class tuitions for the student in the payment month

      await dispatch(fetchStudentClassTuitionsByMonthAdmin({
        userId,
        month
      }))

      // Show the right panel
      setRightPanelType("view");
      setShowRightPanel(true);
    } catch (error) {
      console.error("Error fetching payment details:", error);
    } finally {
      setViewLoading(false);
    }
  };

  // Hàm xuất chi tiết thanh toán thành ảnh
  const handleExportImage = async () => {
    if (!detailsContainerRef.current) return;

    setExportLoading(true);
    try {
      // Lưu trữ style hiện tại
      const originalStyle = detailsContainerRef.current.getAttribute('style') || '';

      // Thêm style tạm thời cho việc xuất ảnh
      detailsContainerRef.current.setAttribute(
        'style',
        `${originalStyle};
         background-color: white;
         border-radius: 8px;
         box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);`
      );

      const element = detailsContainerRef.current;
      const canvas = await html2canvas(element, {
        scale: 2, // Tăng độ phân giải
        useCORS: true, // Cho phép tải hình ảnh từ các domain khác
        logging: false,
        backgroundColor: '#ffffff',
        margin: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 20
        }
      });

      // Khôi phục style ban đầu
      detailsContainerRef.current.setAttribute('style', originalStyle);

      // Chuyển canvas thành URL
      const imageUrl = canvas.toDataURL('image/png');

      // Tạo link tải xuống
      const link = document.createElement('a');
      const fileName = tuitionPayment?.user
        ? `hoc-phi-${tuitionPayment.user.lastName}-${tuitionPayment.user.firstName}-${tuitionPayment.monthFormatted}.png`
        : `hoc-phi-${new Date().toISOString()}.png`;

      link.download = fileName;
      link.href = imageUrl;
      link.click();
    } catch (error) {
      console.error("Error exporting image:", error);
    } finally {
      setExportLoading(false);
    }
  };

  const handleAdd = () => {
    // Reset form state
    setAddUserId("");
    setAddUserSearchTerm("");
    setAddMonth("");
    setAddExpectedAmount("");
    setAddExpectedAmountFormatted("");
    setAddPaidAmount("");
    setAddPaidAmountFormatted("");
    setAddPaymentDate("");
    setAddDueDate("");
    setAddStatus("");
    setAddNote("");
    setAddCalculateExpected(false);
    setFormErrors({});

    // Set current date as default for dueDate
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    setAddDueDate(formattedDate);

    // Set current month as default for month
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;
    setAddMonth(formattedMonth);

    // Open panel
    setRightPanelType("add");
    setShowRightPanel(true);
  };

  const handleBatchAdd = () => {
    setRightPanelType("batch");
    setShowRightPanel(true);
  };

  const handleCreateBatchTuition = () => {
    setRightPanelType("batchByMonth");
    setShowRightPanel(true);
  };

  const handleCreateByClass = () => {
    setRightPanelType("batchByClass");
    setShowRightPanel(true);
  };

  const closeRightPanel = () => {
    setShowRightPanel(false);
    setRightPanelType("");
    // Reset form state
    setBatchMonth("");
    setBatchAmount("");
    setBatchAmountFormatted("");
    setBatchDueDate("");
    setBatchClass("");
    setBatchNote("");
    setEditId(null);
    setEditExpectedAmount("");
    setEditExpectedAmountFormatted("");
    setEditPaidAmount("");
    setEditPaidAmountFormatted("");
    setEditPaymentDate("");
    setEditStatus("");
    setEditNote("");
    setCalculateExpected(false);
    // Reset view state
    setViewPayment(null);
    setViewClassTuitions(null);
    setFormErrors({});
    setIsSubmitting(false);
    setViewLoading(false);
  };

  // Validate edit form data
  const validateEditForm = () => {
    const errors = {};

    // Validate paid amount (must be a positive number)
    if (editPaidAmount && (isNaN(editPaidAmount) || Number(editPaidAmount) < 0)) {
      errors.editPaidAmount = "Số tiền phải là số dương";
    }

    // Validate expected amount (must be a positive number if provided)
    if (editExpectedAmount && (isNaN(editExpectedAmount) || Number(editExpectedAmount) < 0)) {
      errors.editExpectedAmount = "Số tiền phải là số dương";
    }

    // Validate status (required)
    if (!editStatus) {
      errors.editStatus = "Trạng thái không được để trống";
    }

    return errors;
  };

  // Handle edit form submission
  const handleUpdateTuitionPayment = async (e) => {
    e.preventDefault();

    // Parse formatted values to numbers
    const parsedPaidAmount = editPaidAmountFormatted ? parseCurrencyInput(editPaidAmountFormatted) : editPaidAmount;
    const parsedExpectedAmount = editExpectedAmountFormatted ? parseCurrencyInput(editExpectedAmountFormatted) : editExpectedAmount;

    // Update the actual values with parsed values
    if (editPaidAmountFormatted) {
      setEditPaidAmount(parsedPaidAmount);
    }

    if (editExpectedAmountFormatted && !calculateExpected) {
      setEditExpectedAmount(parsedExpectedAmount);
    }

    // Validate form
    const errors = validateEditForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API call
      const paymentData = {
        paidAmount: parsedPaidAmount ? Number(parsedPaidAmount) : 0,
        status: editStatus,
        note: editNote,
        calculateExpected: calculateExpected
      };

      // Only include expectedAmount if it's provided and not calculating automatically
      if (parsedExpectedAmount && !calculateExpected) {
        paymentData.expectedAmount = Number(parsedExpectedAmount);
      }

      // Only include paymentDate if it's provided
      if (editPaymentDate) {
        paymentData.paymentDate = editPaymentDate;
      }

      // Call API to update tuition payment
      await dispatch(updateTuitionPayment({
        id: editId,
        paymentData
      }));

      // Close panel and refresh data
      closeRightPanel();
      dispatch(fetchTuitionPayments({
        page: currentPage,
        limit,
        search: inputValue,
        sortOrder: "DESC",
        status: filterStatus,
        month: filterMonth,
        overdue: filterOverdue,
        userClass: filterClass
      }));
    } catch (error) {
      console.error("Error updating tuition payment:", error);
      setFormErrors({ submit: "Có lỗi xảy ra khi cập nhật học phí" });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Validate form data
  const validateBatchTuitionForm = () => {
    const errors = {};

    // Validate month (required and format YYYY-MM)
    if (!batchMonth) {
      errors.batchMonth = "Tháng không được để trống";
    } else if (!/^\d{4}-\d{2}$/.test(batchMonth)) {
      errors.batchMonth = "Định dạng tháng không hợp lệ";
    }

    // Validate amount (optional but must be positive if provided)
    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {
      errors.batchAmount = "Số tiền phải là số dương";
    }

    // Validate class selection (required)
    if (!batchClass) {
      errors.batchClass = "Vui lòng chọn lớp";
    }

    // Validate due date (required)
    if (!batchDueDate) {
      errors.batchDueDate = "Hạn thanh toán không được để trống";
    }

    return errors;
  };

  // Handle batch tuition form submission
  const handleBatchTuitionSubmit = async (e) => {
    e.preventDefault();

    // Parse formatted values to numbers
    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;

    // Update the actual value with parsed value
    if (batchAmountFormatted) {
      setBatchAmount(parsedBatchAmount);
    }

    // Validate form
    const errors = validateBatchTuitionForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API call
      const batchData = {
        month: batchMonth,
        dueDate: batchDueDate,
        batchClass,
        note: batchNote
      };

      // Only include expectedAmount if it's provided
      if (parsedBatchAmount) {
        batchData.expectedAmount = Number(parsedBatchAmount);
      }

      // Call API to create batch tuition payments
      await dispatch(createBatchTuitionPayments(batchData));

      // Close panel and refresh data
      closeRightPanel();
      dispatch(fetchTuitionPayments({
        page: currentPage,
        limit,
        search: inputValue,
        sortOrder: "DESC"
      }));
    } catch (error) {
      console.error("Error creating batch tuition payments:", error);
      setFormErrors({ submit: "Có lỗi xảy ra khi tạo học phí hàng loạt" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = (id) => {
    setPaymentToDelete(id);
    setShowConfirmModal(true);
  };

  const confirmDelete = async () => {
    dispatch(deleteTuitionPayment(paymentToDelete));
    setShowConfirmModal(false);
  };

  const cancelDelete = () => {
    setShowConfirmModal(false);
    setPaymentToDelete(null);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "PAID":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
            Đã thanh toán
          </span>
        );
      case "UNPAID":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
            Chưa thanh toán
          </span>
        );
      case "OVERDUE":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
            Quá hạn
          </span>
        );
      case "PARTIAL":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
            Thanh toán một phần
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {
    return (
      <button
        onClick={onClick}
        className="flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam"
      >
        {icon}
        <span>{text}</span>
      </button>
    );
  };

  const iconAdd = (
    <div data-svg-wrapper className="relative">
      <Plus size={16} />
    </div>
  );

  const iconBatch = (
    <div data-svg-wrapper className="relative">
      <FileText size={16} />
    </div>
  );

  const iconCalendar = (
    <div data-svg-wrapper className="relative">
      <Calendar size={16} />
    </div>
  );

  const iconUsers = (
    <div data-svg-wrapper className="relative">
      <Users size={16} />
    </div>
  );

  return (
    <AdminLayout>
      <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4">
        Quản lý thanh toán học phí
      </div>

      <div className="flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6">
        <div className="flex-1">
          <div className="flex flex-wrap gap-2 items-center mb-4">
            <div className="w-[300px] relative">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                className="absolute left-[1rem] top-1/2 transform -translate-y-1/2"
              >
                <path
                  d="M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z"
                  stroke="#131214"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <input
                type="text"
                placeholder="Tìm kiếm theo tên học sinh, lớp học..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam"
              />
            </div>

            {/* Bộ lọc */}
            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterMonth}
                onChange={(e) => setFilterMonth(e.target.value)}
              >
                <option value="">Tháng</option>
                {Array.from({ length: 12 }, (_, i) => {
                  const month = i + 1;
                  const year = new Date().getFullYear();
                  const monthStr = month < 10 ? `0${month}` : `${month}`;
                  return (
                    <option key={month} value={`${year}-${monthStr}`}>
                      {`Tháng ${month}/${year}`}
                    </option>
                  );
                })}
              </select>
            </div>

            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="">Trạng thái</option>
                <option value="PAID">Đã thanh toán</option>
                <option value="UNPAID">Chưa thanh toán</option>
                <option value="PARTIAL">Thanh toán một phần</option>
              </select>
            </div>

            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterOverdue}
                onChange={(e) => setFilterOverdue(e.target.value)}
              >
                <option value="">Tình trạng hạn</option>
                <option value="true">Đã quá hạn</option>
                <option value="false">Chưa quá hạn</option>
              </select>
            </div>

            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterClass}
                onChange={(e) => setFilterClass(e.target.value)}
              >
                <option value="">Khối</option>
                <option value="10">Khối 10</option>
                <option value="11">Khối 11</option>
                <option value="12">Khối 12</option>
              </select>
            </div>

            {/* Tìm kiếm lớp học */}
            <div className="w-[250px]">
              <ClassSearchInput
                value={classSearchTerm}
                selectedClassId={filterClassId}
                onChange={setClassSearchTerm}
                onSelect={handleSelectClass}
                onClear={handleClearClassSelection}
                placeholder="Tìm kiếm lớp học..."
              />
            </div>

            <button
              onClick={handleSearch}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              Tìm kiếm
            </button>
            <div className="flex border border-gray-300 rounded-md overflow-hidden">
              <button
                className={`px-4 py-2 flex items-center ${viewMode === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
                onClick={() => handleViewModeChange('table')}
              >
                <List size={16} className="mr-2" />
                Danh sách
              </button>
              <button
                className={`px-4 py-2 flex items-center ${viewMode === 'statistics' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
                onClick={() => handleViewModeChange('statistics')}
              >
                <BarChart2 size={16} className="mr-2" />
                Thống kê
              </button>
            </div>


          </div>

          <div className="flex flex-wrap gap-2 items-center">
            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />
            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />
            <ButtonFunctionBarAdmin icon={iconUsers} text={'Tạo học phí theo lớp'} onClick={handleCreateByClass} />
          </div>
        </div>
      </div>

      {viewMode === 'table' ? (
        tuitionPayments.length === 0 ? (
          <div className="bg-white shadow-md rounded-lg p-6 text-center">
            <p className="text-gray-500">Không có dữ liệu thanh toán học phí nào.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-3 px-4 text-left">STT</th>
                  <th className="py-3 px-4 text-left">Học sinh</th>
                  <th className="py-3 px-4 text-left">Lớp</th>
                  <th className="py-3 px-4 text-left">Trường</th>
                  <th className="py-3 px-4 text-left">Số tiền cần</th>
                  <th className="py-3 px-4 text-left">Số tiền đóng</th>
                  <th className="py-3 px-4 text-left">Tháng</th>
                  <th className="py-3 px-4 text-left">Ngày thanh toán</th>
                  <th className="py-3 px-4 text-left">Hạn thanh toán</th>
                  <th className="py-3 px-4 text-left">Trạng thái</th>
                  <th className="py-3 px-4 text-left">Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {tuitionPayments.map((payment, index) => (
                  <tr
                    key={payment.id}
                    className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}
                  >
                    <td className="py-3 px-4">
                      {(currentPage - 1) * limit + index + 1}
                    </td>
                    <td className="py-3 px-4">
                      {payment.user?.lastName + " " + payment.user?.firstName || "N/A"}
                    </td>
                    <td className="py-3 px-4">{payment.user?.class || "N/A"}</td>
                    <td className="py-3 px-4">{payment.user?.highSchool || "N/A"}</td>
                    <td className="py-3 px-4">
                      {formatCurrency(payment.expectedAmount)}
                    </td>
                    <td className="py-3 px-4">
                      {formatCurrency(payment.paidAmount)}
                    </td>
                    <td className="py-3 px-4">{payment.monthFormatted}</td>
                    <td className="py-3 px-4">
                      {payment.paymentDate
                        ? new Date(payment.paymentDate).toLocaleDateString("vi-VN")
                        : "Chưa thanh toán"}
                    </td>
                    <td className="py-3 px-4">
                      {new Date(payment.dueDate).toLocaleDateString("vi-VN")}
                    </td>
                    <td className="py-3 px-4">
                      {getStatusBadge(payment.status === 'PAID' ? 'PAID' : payment.isOverdue ? 'OVERDUE' : payment.status === 'PARTIAL' ? 'PARTIAL' : payment.status)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleView(payment.id, payment.userId, payment.month)}
                          className="text-blue-500 hover:text-blue-700"
                          title="Xem chi tiết"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => handleEdit(payment.id)}
                          className="text-yellow-500 hover:text-yellow-700"
                          title="Chỉnh sửa"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(payment.id)}
                          className="text-red-500 hover:text-red-700"
                          title="Xóa"
                        >
                          <Trash size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )
      ) : (
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Bộ lọc thống kê</h2>
            <div className="flex flex-wrap gap-4">
              <div className="w-64">
                <label className="block text-sm font-medium text-gray-700 mb-1">Từ tháng</label>
                <input
                  type="month"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  value={startMonth}
                  onChange={(e) => setStartMonth(e.target.value)}
                />
              </div>
              <div className="w-64">
                <label className="block text-sm font-medium text-gray-700 mb-1">Đến tháng</label>
                <input
                  type="month"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  value={endMonth}
                  onChange={(e) => setEndMonth(e.target.value)}
                />
              </div>
              <div className="w-64">
                <label className="block text-sm font-medium text-gray-700 mb-1">Khối</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  value={filterClass}
                  onChange={(e) => setFilterClass(e.target.value)}
                >
                  <option value="">Tất cả</option>
                  <option value="10">Khối 10</option>
                  <option value="11">Khối 11</option>
                  <option value="12">Khối 12</option>
                </select>
              </div>

              <div className="w-64">
                <label className="block text-sm font-medium text-gray-700 mb-1">Lớp học</label>
                <ClassSearchInput
                  value={classSearchTerm}
                  selectedClassId={filterClassId}
                  onChange={setClassSearchTerm}
                  onSelect={handleSelectClass}
                  onClear={handleClearClassSelection}
                  placeholder="Tìm kiếm lớp học..."
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => {
                    dispatch(fetchTuitionStatistics({
                      startMonth,
                      endMonth,
                      userClass: filterClass,
                      classId: filterClassId
                    }));
                  }}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
                >
                  <Search size={16} className="mr-2" />
                  Xem thống kê
                </button>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : tuitionStatistics ? (
            <div>
              {/* Thống kê tổng quan */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Thống kê tổng quan</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                    <p className="text-sm text-blue-500 mb-1">Tổng số tiền cần thu</p>
                    <p className="text-2xl font-bold">{formatCurrency(tuitionStatistics.totalStatistics?.totalExpectedAmount || 0)}</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                    <p className="text-sm text-green-500 mb-1">Tổng số tiền đã thu</p>
                    <p className="text-2xl font-bold">{formatCurrency(tuitionStatistics.totalStatistics?.totalPaidAmount || 0)}</p>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg border border-red-100">
                    <p className="text-sm text-red-500 mb-1">Tổng số tiền còn lại</p>
                    <p className="text-2xl font-bold">{formatCurrency(tuitionStatistics.totalStatistics?.remainingAmount || 0)}</p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                    <p className="text-sm text-purple-500 mb-1">Tỷ lệ thu</p>
                    <p className="text-2xl font-bold">{tuitionStatistics.totalStatistics?.collectionRate || 0}%</p>
                  </div>
                </div>
              </div>

              {/* Biểu đồ */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4">Doanh thu theo tháng</h3>
                  <div className="h-80">
                    <canvas ref={monthlyChartRef}></canvas>
                  </div>
                </div>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4">Doanh thu theo lớp</h3>
                  <div className="h-80">
                    <canvas ref={classChartRef}></canvas>
                  </div>
                </div>
              </div>

              {/* Bảng thống kê chi tiết */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Thống kê theo tháng</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="py-3 px-4 text-left">Tháng</th>
                        <th className="py-3 px-4 text-left">Số tiền cần thu</th>
                        <th className="py-3 px-4 text-left">Số tiền đã thu</th>
                        <th className="py-3 px-4 text-left">Số tiền còn lại</th>
                        <th className="py-3 px-4 text-left">Tỷ lệ thu</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tuitionStatistics.monthlyStatistics?.map((stat, index) => (
                        <tr key={stat.month} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                          <td className="py-3 px-4">{stat.monthFormatted}</td>
                          <td className="py-3 px-4">{formatCurrency(stat.totalExpectedAmount)}</td>
                          <td className="py-3 px-4">{formatCurrency(stat.totalPaidAmount)}</td>
                          <td className="py-3 px-4">{formatCurrency(stat.remainingAmount)}</td>
                          <td className="py-3 px-4">{stat.collectionRate}%</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {tuitionStatistics.classStatistics && tuitionStatistics.classStatistics.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Thống kê theo lớp</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="py-3 px-4 text-left">Lớp</th>
                          <th className="py-3 px-4 text-left">Số tiền cần thu</th>
                          <th className="py-3 px-4 text-left">Số tiền đã thu</th>
                          <th className="py-3 px-4 text-left">Số tiền còn lại</th>
                          <th className="py-3 px-4 text-left">Tỷ lệ thu</th>
                        </tr>
                      </thead>
                      <tbody>
                        {tuitionStatistics.classStatistics.map((stat, index) => (
                          <tr key={stat.userClass} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                            <td className="py-3 px-4">Lớp {stat.userClass}</td>
                            <td className="py-3 px-4">{formatCurrency(stat.totalExpectedAmount)}</td>
                            <td className="py-3 px-4">{formatCurrency(stat.totalPaidAmount)}</td>
                            <td className="py-3 px-4">{formatCurrency(stat.remainingAmount)}</td>
                            <td className="py-3 px-4">{stat.collectionRate}%</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 text-center">
              <p className="text-blue-500">Vui lòng chọn khoảng thời gian và nhấn "Xem thống kê" để xem dữ liệu thống kê.</p>
            </div>
          )}
        </div>
      )}
      {viewMode === "table" && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            onPageChange={(page) => dispatch(setCurrentPage(page))}
            totalItems={totalItems}
            limit={limit}
          />

        </div>
      )}


      {/* Right Panel for Batch Operations */}
      {showRightPanel && (
        <div className="fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-semibold">
              {rightPanelType === "add" && "Thêm thanh toán học phí"}
              {rightPanelType === "batchByMonth" && "Tạo học phí hàng loạt"}
              {rightPanelType === "batchByClass" && "Tạo học phí theo lớp"}
              {rightPanelType === "edit" && "Chỉnh sửa thanh toán học phí"}
              {rightPanelType === "view" && "Chi tiết thanh toán học phí"}
            </h2>
            <button
              onClick={closeRightPanel}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
          </div>

          <div>
            {rightPanelType === "add" && (
              <div className="p-4">
                <p className="mb-4">Thêm khoản thanh toán học phí mới cho học sinh.</p>
                {/* Form content for adding new payment */}
                <form className="space-y-4" onSubmit={handleAddTuitionSubmit}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Học sinh <span className="text-red-500">*</span></label>
                    <UserSearchInput
                      value={addUserSearchTerm}
                      selectedUserId={addUserId}
                      onChange={setAddUserSearchTerm}
                      onSelect={(user) => {
                        setAddUserId(user.id);
                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);
                      }}
                      onClear={() => {
                        setAddUserId("");
                        setAddUserSearchTerm("");
                      }}
                      placeholder="Tìm kiếm học sinh..."
                      role="STUDENT"
                    />
                    {formErrors.userId && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.userId}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tháng <span className="text-red-500">*</span></label>
                    <input
                      type="month"
                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={addMonth}
                      onChange={(e) => setAddMonth(e.target.value)}
                    />
                    {formErrors.month && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.month}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Số tiền cần đóng
                      <div className="flex items-center mt-1">
                        <input
                          type="checkbox"
                          id="addCalculateExpected"
                          checked={addCalculateExpected}
                          onChange={(e) => setAddCalculateExpected(e.target.checked)}
                          className="mr-2"
                        />
                        <label htmlFor="addCalculateExpected" className="text-xs text-gray-500">
                          Tự động tính dựa trên các lớp học sinh đã tham gia
                        </label>
                      </div>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border ${formErrors.expectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${addCalculateExpected ? 'bg-gray-100' : ''}`}
                      placeholder="Nhập số tiền cần đóng"
                      value={addExpectedAmountFormatted}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\./g, "");
                        if (!isNaN(value) || value === "") {
                          setAddExpectedAmount(value);
                          setAddExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : "");
                        }
                      }}
                      disabled={addCalculateExpected}
                    />
                    {formErrors.expectedAmount && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.expectedAmount}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền đã đóng</label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border ${formErrors.paidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      placeholder="Nhập số tiền đã đóng"
                      value={addPaidAmountFormatted}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\./g, "");
                        if (!isNaN(value) || value === "") {
                          setAddPaidAmount(value);
                          setAddPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : "");
                        }
                      }}
                    />
                    {formErrors.paidAmount && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.paidAmount}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ngày thanh toán</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      value={addPaymentDate}
                      onChange={(e) => setAddPaymentDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán <span className="text-red-500">*</span></label>
                    <input
                      type="date"
                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={addDueDate}
                      onChange={(e) => setAddDueDate(e.target.value)}
                    />
                    {formErrors.dueDate && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.dueDate}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái <span className="text-red-500">*</span></label>
                    <select
                      className={`w-full px-3 py-2 border ${formErrors.status ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={addStatus}
                      onChange={(e) => setAddStatus(e.target.value)}
                    >
                      <option value="">Chọn trạng thái</option>
                      <option value="PAID">Đã thanh toán</option>
                      <option value="UNPAID">Chưa thanh toán</option>
                      <option value="PARTIAL">Thanh toán một phần</option>
                    </select>
                    {formErrors.status && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.status}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                      value={addNote}
                      onChange={(e) => setAddNote(e.target.value)}
                    ></textarea>
                  </div>
                  {formErrors.submit && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
                      <AlertCircle size={20} className="mr-2 mt-0.5 flex-shrink-0" />
                      <p>{formErrors.submit}</p>
                    </div>
                  )}
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Lưu thanh toán"}
                  </button>
                </form>
              </div>
            )}
            {rightPanelType === "batchByMonth" && (
              <div className="p-4">
                <p className="mb-4">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>
                {/* Form content for batch tuition by month */}
                <form className="space-y-4" onSubmit={(e) => handleBatchTuitionSubmit(e)}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tháng <span className="text-red-500">*</span></label>
                    <input
                      type="month"
                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchMonth}
                      onChange={(e) => setBatchMonth(e.target.value)}
                      required
                    />
                    {formErrors.batchMonth && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchMonth}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Số tiền cần đóng <span className="text-gray-500 text-xs font-normal">(không bắt buộc)</span>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      placeholder="Nhập số tiền hoặc để trống để tự động tính"
                      value={batchAmountFormatted}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\./g, "");
                        if (!isNaN(value) || value === "") {
                          setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : "");
                          setBatchAmount(value ? parseInt(value, 10) : "");
                        }
                      }}
                    />
                    {formErrors.batchAmount && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchAmount}
                      </p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      Nếu để trống, hệ thống sẽ tự động tính dựa trên các lớp học sinh đã tham gia
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Lớp <span className="text-red-500">*</span></label>
                    <select
                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchClass}
                      onChange={(e) => setBatchClass(e.target.value)}
                      required
                    >
                      <option value="">Chọn lớp</option>
                      <option value="10">Lớp 10</option>
                      <option value="11">Lớp 11</option>
                      <option value="12">Lớp 12</option>
                    </select>
                    {formErrors.batchClass && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchClass}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán <span className="text-red-500">*</span></label>
                    <input
                      type="date"
                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchDueDate}
                      onChange={(e) => setBatchDueDate(e.target.value)}
                      required
                    />
                    {formErrors.batchDueDate && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchDueDate}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                      value={batchNote}
                      onChange={(e) => setBatchNote(e.target.value)}
                    ></textarea>
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Tạo học phí hàng loạt"}
                  </button>
                </form>
              </div>
            )}

            {rightPanelType === "batchByClass" && (
              <div className="p-4">
                <p className="mb-4">Tạo học phí cho tất cả học sinh trong một lớp học.</p>
                {/* Form content for batch tuition by class */}
                <form className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Lớp học</label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                      <option value="">Chọn lớp học</option>
                      <option value="1">Lớp 10A1</option>
                      <option value="2">Lớp 11A2</option>
                      <option value="3">Lớp 12A3</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tháng</label>
                    <input
                      type="month"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền cần đóng</label>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Nhập số tiền"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                    ></textarea>
                  </div>
                  <button
                    type="button"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
                  >
                    Tạo học phí theo lớp
                  </button>
                </form>
              </div>
            )}

            {rightPanelType === "edit" && (
              <div className="p-4">
                <p className="mb-4">Chỉnh sửa thông tin thanh toán học phí.</p>
                {/* Form content for editing tuition payment */}
                <form className="space-y-4" onSubmit={(e) => handleUpdateTuitionPayment(e)}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Số tiền cần đóng
                      <div className="flex items-center mt-1">
                        <input
                          type="checkbox"
                          id="calculateExpected"
                          checked={calculateExpected}
                          onChange={(e) => setCalculateExpected(e.target.checked)}
                          className="mr-2"
                        />
                        <label htmlFor="calculateExpected" className="text-xs text-gray-500">
                          Tự động tính lại dựa trên các lớp học sinh đã tham gia
                        </label>
                      </div>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border ${formErrors.editExpectedAmount ? 'border-red-500' : 'border-gray-300'} rounded-md ${calculateExpected ? 'bg-gray-100' : ''}`}
                      placeholder="Nhập số tiền cần đóng"
                      value={editExpectedAmountFormatted}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\./g, "");
                        if (!isNaN(value) || value === "") {
                          setEditExpectedAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : "");
                        }
                      }}
                      disabled={calculateExpected}
                    />
                    {formErrors.editExpectedAmount && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.editExpectedAmount}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền đã đóng</label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border ${formErrors.editPaidAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      placeholder="Nhập số tiền đã đóng"
                      value={editPaidAmountFormatted}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\./g, "");
                        if (!isNaN(value) || value === "") {
                          setEditPaidAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : "");
                        }
                      }}
                    />
                    {formErrors.editPaidAmount && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.editPaidAmount}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ngày thanh toán</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      value={editPaymentDate}
                      onChange={(e) => setEditPaymentDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái <span className="text-red-500">*</span></label>
                    <select
                      className={`w-full px-3 py-2 border ${formErrors.editStatus ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={editStatus}
                      onChange={(e) => setEditStatus(e.target.value)}
                      required
                    >
                      <option value="">Chọn trạng thái</option>
                      <option value="PAID">Đã thanh toán</option>
                      <option value="UNPAID">Chưa thanh toán</option>
                      <option value="PARTIAL">Thanh toán một phần</option>
                    </select>
                    {formErrors.editStatus && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.editStatus}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                      value={editNote}
                      onChange={(e) => setEditNote(e.target.value)}
                    ></textarea>
                  </div>
                  {formErrors.submit && (
                    <p className="text-sm text-red-500 flex items-center">
                      <AlertCircle size={14} className="mr-1" /> {formErrors.submit}
                    </p>
                  )}
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Lưu thay đổi"}
                  </button>
                </form>
              </div>
            )}

            {rightPanelType === "view" && (
              <div>
                {viewLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                  </div>
                ) : tuitionPayment ? (
                  <div className="space-y-6 mb-4">
                    <div ref={detailsContainerRef} className="space-y-6 p-6 bg-white rounded-lg">
                      {/* Header cho bản in */}
                      <div className="flex items-center justify-between border-b pb-4 mb-2">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3">
                            TTB
                          </div>
                          <div>
                            <h2 className="text-xl font-bold text-gray-800">Toán Thầy Bee</h2>
                            <p className="text-sm text-gray-500">Chi tiết thanh toán học phí</p>
                            <p className="text-sm text-gray-500">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>
                          </div>
                        </div>

                      </div>

                      {/* Thông tin học sinh */}
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="text-lg font-semibold mb-2">Thông tin học sinh</h3>
                        <div className="space-y-2">
                          <p><span className="font-medium">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>
                          <p><span className="font-medium">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>
                          <p><span className="font-medium">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>
                          <p><span className="font-medium">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>
                        </div>
                      </div>

                      {/* Thông tin thanh toán */}
                      <div>
                        <h3 className="text-lg font-semibold mb-2">Thông tin thanh toán</h3>
                        <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-2">
                          <p><span className="font-medium">Tháng:</span> {tuitionPayment.monthFormatted}</p>
                          <p><span className="font-medium">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>
                          <p><span className="font-medium">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>
                          <p><span className="font-medium">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>
                          <p>
                            <span className="font-medium">Trạng thái:</span> {' '}
                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :
                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :
                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :
                                  'bg-yellow-100 text-yellow-800'
                              }`}>
                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :
                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :
                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :
                                    'Quá hạn'}
                            </span>
                          </p>
                          <p><span className="font-medium">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>
                          <p><span className="font-medium">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>
                          {tuitionPayment.note && <p><span className="font-medium">Ghi chú:</span> {tuitionPayment.note}</p>}
                        </div>
                      </div>

                      {/* Danh sách học phí lớp */}
                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Danh sách học phí các lớp</h3>
                          <div className="space-y-3">
                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (
                              <div key={tuition.id} className="bg-white border border-gray-200 rounded-lg p-3">
                                <div className="flex justify-between items-center mb-2">
                                  <h4 className="font-medium">{tuition.className}</h4>
                                  <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                    Lớp {tuition.classGrade}
                                  </span>
                                </div>
                                <p><span className="text-sm text-gray-500">Học phí:</span> {formatCurrency(tuition.amount)}</p>
                                <p><span className="text-sm text-gray-500">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>
                                {tuition.note && <p><span className="text-sm text-gray-500">Ghi chú:</span> {tuition.note}</p>}
                              </div>
                            ))}
                          </div>
                          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                            <p className="font-medium">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>
                            <p className="font-medium">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>
                          </div>
                        </div>
                      )}

                      {/* Footer cho bản in */}
                      <div className="border-t pt-4 mt-6">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm text-gray-500">Mọi thắc mắc xin liên hệ:</p>
                            <p className="text-sm font-medium">Hotline: 0333726202</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-500">Chữ ký xác nhận</p>
                            <div className="h-16"></div>
                            <p className="text-sm font-medium">Người thu học phí</p>
                            <p className="text-sm font-medium">Triệu Minh</p>
                          </div>
                        </div>
                      </div>
                    </div> {/* End of detailsContainerRef div */}

                    {/* Nút chỉnh sửa và xuất ảnh */}
                    <div className="flex gap-2 flex-col mx-4">
                      <button
                        onClick={() => handleEdit(tuitionPayment.id)}
                        className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
                      >
                        Chỉnh sửa thanh toán
                      </button>
                      <button
                        onClick={handleExportImage}
                        className="flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 flex items-center justify-center"
                      >
                        {exportLoading ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Đang xuất...
                          </span>
                        ) : (
                          <span>Xuất ảnh</span>
                        )}
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    Không tìm thấy thông tin thanh toán
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      <ConfirmModal
        isOpen={showConfirmModal}
        onConfirm={confirmDelete}
        text="Bạn có chắc chắn muốn xóa khoản đóng học phí này?"
        onClose={cancelDelete}
      />
    </AdminLayout>
  );
};

export default TuitionPaymentList;
