import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import html2canvas from "html2canvas";
import { find } from "lodash";
import ClassSearchInput from "../../../components/ClassSearchInput";
import UserSearchInput from "../../../components/UserSearchInput";
import {
  fetchTuitionPayments,
  deleteTuitionPayment,
  createTuitionPayment,
  createBatchTuitionPayments,
  fetchTuitionStatistics,
  updateTuitionPayment,
  fetchTuitionPaymentByIdAdmin,
} from "src/features/tuition/tuitionSlice";
import { formatCurrency, formatNumberWithDots, parseCurrencyInput } from "src/utils/formatters";
import { findUsers } from "src/features/user/userSlice";
import { findClasses } from "src/features/class/classSlice";
import { setCurrentPage } from "src/features/tuition/tuitionSlice";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
import Pagination from "src/components/Pagination";
import ConfirmModal from "src/components/modal/ConfirmDeleteModal";
import { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from "lucide-react";
import AdminLayout from "src/layouts/AdminLayout";
import FunctionBarAdmin from "src/components/bar/FunctionBarAdmin";
import Chart from 'chart.js/auto';
import { setFilterClassIdSlice, setFilterMonthSlice, setFilterIsPaidSlice, setFilterOverdueSlice, setFilterClassSlice } from "src/features/tuition/tuitionSlice";

const TuitionPaymentList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { tuitionPayments, tuitionStatistics, loading, tuitionPayment, studentClassTuitionsAdmin } = useSelector((state) => state.tuition);

  const { page, totalPages, total, pageSize } = useSelector(
    (state) => state.tuition.pagination
  );
  const [inputValue, setInputValue] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [paymentToDelete, setPaymentToDelete] = useState(null);
  // const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên
  const [showRightPanel, setShowRightPanel] = useState(false);
  const [rightPanelType, setRightPanelType] = useState(""); // "batch", "batchByClass", "batchByMonth"

  // State cho bộ lọc
  // const [filterMonth, setFilterMonth] = useState("");
  // const [filterIsPaid, setFilterIsPaid] = useState(""); // Thay đổi từ filterStatus thành filterIsPaid
  // const [filterOverdue, setFilterOverdue] = useState("");
  // const [filterClass, setFilterClass] = useState("");
  // const [filterClassId, setFilterClassId] = useState("");

  const { filterMonth, filterIsPaid, filterOverdue, filterClass, filterClassId } = useSelector(
    (state) => state.tuition
  );

  const set


  const [classSearchTerm, setClassSearchTerm] = useState("");

  const [selectedUserId, setSelectedUserId] = useState("");
  const [userSearchTerm, setUserSearchTerm] = useState("");

  // const [selectedClassId, setSelectedClassId] = useState("");
  // const [classSearchTerm1, setClassSearchTerm1] = useState("");

  // State cho chế độ xem
  const [viewMode, setViewMode] = useState("table"); // "table" hoặc "statistics"
  const [startMonth, setStartMonth] = useState("");
  const [endMonth, setEndMonth] = useState("");

  // Refs cho biểu đồ
  const monthlyChartRef = useRef(null);
  const classChartRef = useRef(null);
  const monthlyChartInstance = useRef(null);
  const classChartInstance = useRef(null);

  // State cho form tạo học phí hàng loạt
  const [batchMonth, setBatchMonth] = useState("");
  const [batchAmount, setBatchAmount] = useState("");
  const [batchAmountFormatted, setBatchAmountFormatted] = useState("");
  const [batchDueDate, setBatchDueDate] = useState("");
  const [batchClass, setBatchClass] = useState("");
  const [batchNote, setBatchNote] = useState("");
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State cho form thêm học phí
  const [addUserId, setAddUserId] = useState("");
  const [addUserSearchTerm, setAddUserSearchTerm] = useState("");
  const [addMonth, setAddMonth] = useState("");
  const [addPaymentDate, setAddPaymentDate] = useState("");
  const [addDueDate, setAddDueDate] = useState("");
  const [addIsPaid, setAddIsPaid] = useState(false); // Thay đổi từ addStatus thành addIsPaid
  const [addNote, setAddNote] = useState("");

  // State cho form chỉnh sửa học phí
  const [editId, setEditId] = useState(null);
  const [editPaymentDate, setEditPaymentDate] = useState("");
  const [editDueDate, setEditDueDate] = useState("");
  const [editIsPaid, setEditIsPaid] = useState(false); // Thay đổi từ editStatus thành editIsPaid
  const [editNote, setEditNote] = useState("");

  // State cho xem chi tiết học phí
  const [viewPayment, setViewPayment] = useState(null);
  const [viewClassTuitions, setViewClassTuitions] = useState(null);
  const [viewLoading, setViewLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const detailsContainerRef = useRef(null);

  // useEffect(() => {
  //   if (!didInit) {
  //     dispatch(resetFilters());
  //     setDidInit(true);
  //   }
  // }, [dispatch, didInit]);

  useEffect(() => {
    dispatch(findClasses(""));
    dispatch(findUsers(""));
  }, [dispatch]);

  // Xử lý khi người dùng chọn một lớp
  const handleSelectClass = (classItem) => {
    setFilterClassId(classItem.id);
    setClassSearchTerm(classItem.name);
  };

  // Xử lý khi người dùng xóa lựa chọn lớp
  const handleClearClassSelection = () => {
    setFilterClassId("");
    setClassSearchTerm("");
  };

  // Xử lý khi người dùng chọn một người dùng
  const handleSelectUser = (userItem) => {
    setSelectedUserId(userItem.id);
    setUserSearchTerm(userItem.firstName + " " + userItem.lastName);
  };

  // Xử lý khi người dùng xóa lựa chọn người dùng
  const handleClearUserSelection = () => {
    setSelectedUserId("");
    setUserSearchTerm("");
  };

  // Hàm xử lý tìm kiếm
  const handleSearch = () => {
    dispatch(
      fetchTuitionPayments({
        search: inputValue,
        page: 1, // Reset về trang 1 khi tìm kiếm
        pageSize,
        sortOrder: "DESC",
        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid
        month: filterMonth,
        overdue: filterOverdue,
        userClass: filterClass,
        classId: filterClassId
      })
    );
  };

  // Hàm xử lý thêm học phí mới
  const handleAddTuitionSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const errors = {};

    // Validate userId (required)
    if (!addUserId) {
      errors.userId = "Vui lòng chọn học sinh";
    }

    // Validate month (required and format YYYY-MM)
    if (!addMonth) {
      errors.month = "Tháng không được để trống";
    } else if (!/^\d{4}-\d{2}$/.test(addMonth)) {
      errors.month = "Định dạng tháng không hợp lệ";
    }

    // Validate due date (required)
    if (!addDueDate) {
      errors.dueDate = "Vui lòng chọn hạn thanh toán";
    }

    // If there are errors, display them and stop submission
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API call
      const paymentData = {
        userId: addUserId,
        month: addMonth,
        isPaid: addIsPaid, // Sử dụng isPaid thay vì status
        note: addNote,
        dueDate: addDueDate
      };

      // Include paymentDate if provided
      if (addPaymentDate) {
        paymentData.paymentDate = addPaymentDate;
      }

      // Call API to create tuition payment
      await dispatch(createTuitionPayment(paymentData));

      // Close panel and refresh data
      closeRightPanel();
      dispatch(fetchTuitionPayments({
        page: page,
        pageSize,
        search: inputValue,
        sortOrder: "DESC"
      }));

      // Reset form
      setAddUserId("");
      setAddUserSearchTerm("");
      setAddMonth("");
      setAddPaymentDate("");
      setAddDueDate("");
      setAddIsPaid(false);
      setAddNote("");

    } catch (error) {
      console.error("Error creating tuition payment:", error);
      setFormErrors({ submit: "Có lỗi xảy ra khi tạo học phí" });
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    dispatch(
      fetchTuitionPayments({
        search: inputValue,
        page: page, // Reset về trang 1 khi tìm kiếm
        pageSize,
        sortOrder: "DESC",
        isPaid: filterIsPaid, // Thay đổi từ status thành isPaid
        month: filterMonth,
        overdue: filterOverdue,
        userClass: filterClass
      })
    );
  }, [dispatch, page, pageSize]);

  const handleEdit = async (id) => {
    setIsSubmitting(true);
    try {
      // Fetch the payment details
      const response = await dispatch(fetchTuitionPaymentByIdAdmin(id)).unwrap();
      const payment = response.data;

      // Set the edit form state
      setEditId(id);
      setEditPaymentDate(payment.paymentDate ? new Date(payment.paymentDate).toISOString().split('T')[0] : "");
      setEditDueDate(payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : "");
      setEditIsPaid(payment.isPaid || false); // Sử dụng isPaid thay vì status
      setEditNote(payment.note || "");

      // Show the right panel
      setRightPanelType("edit");
      setShowRightPanel(true);
    } catch (error) {
      console.error("Error fetching payment details:", error);
    } finally {
      setIsSubmitting(false);
    }
  };



  const handleView = async (id, userId, month) => {
    setViewLoading(true);
    try {
      // Fetch the payment details
      await dispatch(fetchTuitionPaymentByIdAdmin(id))

      // Fetch the class tuitions for the student in the payment month

      // await dispatch(fetchStudentClassTuitionsByMonthAdmin({
      //   userId,
      //   month
      // }))

      // Show the right panel
      setRightPanelType("view");
      setShowRightPanel(true);
    } catch (error) {
      console.error("Error fetching payment details:", error);
    } finally {
      setViewLoading(false);
    }
  };

  const handleAdd = () => {
    // Reset form state
    setAddUserId("");
    setAddUserSearchTerm("");
    setAddMonth("");
    setAddPaymentDate("");
    setAddDueDate("");
    setAddIsPaid(false); // Reset isPaid thay vì status
    setAddNote("");
    setFormErrors({});

    // Set current date as default for dueDate
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    setAddDueDate(formattedDate);

    // Set current month as default for month
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const formattedMonth = `${year}-${month < 10 ? `0${month}` : month}`;
    setAddMonth(formattedMonth);

    // Open panel
    setRightPanelType("add");
    setShowRightPanel(true);
  };

  const handleBatchAdd = () => {
    setRightPanelType("batch");
    setShowRightPanel(true);
  };

  const handleCreateBatchTuition = () => {
    setRightPanelType("batchByMonth");
    setShowRightPanel(true);
  };
  const closeRightPanel = () => {
    setShowRightPanel(false);
    setRightPanelType("");
    // Reset form state
    setBatchMonth("");
    setBatchAmount("");
    setBatchAmountFormatted("");
    setBatchDueDate("");
    setBatchClass("");
    setBatchNote("");
    setEditId(null);
    setEditPaymentDate("");
    setEditDueDate("");
    setEditIsPaid(false); // Reset isPaid thay vì status
    setEditNote("");
    // Reset view state
    setViewPayment(null);
    setViewClassTuitions(null);
    setFormErrors({});
    setIsSubmitting(false);
    setViewLoading(false);
  };

  // Validate edit form data
  const validateEditForm = () => {
    const errors = {};

    // Validate due date (required)
    if (!editDueDate) {
      errors.editDueDate = "Hạn thanh toán không được để trống";
    }

    return errors;
  };

  // Handle edit form submission
  const handleUpdateTuitionPayment = async (e) => {
    e.preventDefault();

    // Validate form
    const errors = validateEditForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API call
      const paymentData = {
        isPaid: editIsPaid, // Sử dụng isPaid thay vì status
        note: editNote,
        dueDate: editDueDate
      };

      // Only include paymentDate if it's provided
      if (editPaymentDate) {
        paymentData.paymentDate = editPaymentDate;
      }

      // Call API to update tuition payment
      await dispatch(updateTuitionPayment({
        id: editId,
        paymentData
      }));

      // Close panel and refresh data
      closeRightPanel();
      dispatch(fetchTuitionPayments({
        page: page,
        pageSize,
        search: inputValue,
        sortOrder: "DESC",
        isPaid: filterIsPaid, // Sử dụng isPaid thay vì status
        month: filterMonth,
        overdue: filterOverdue,
        userClass: filterClass
      }));
    } catch (error) {
      console.error("Error updating tuition payment:", error);
      setFormErrors({ submit: "Có lỗi xảy ra khi cập nhật học phí" });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Validate form data
  const validateBatchTuitionForm = () => {
    const errors = {};

    // Validate month (required and format YYYY-MM)
    if (!batchMonth) {
      errors.batchMonth = "Tháng không được để trống";
    } else if (!/^\d{4}-\d{2}$/.test(batchMonth)) {
      errors.batchMonth = "Định dạng tháng không hợp lệ";
    }

    // Validate amount (optional but must be positive if provided)
    if (batchAmount && (isNaN(batchAmount) || Number(batchAmount) < 0)) {
      errors.batchAmount = "Số tiền phải là số dương";
    }

    // Validate class selection (required)
    if (!batchClass) {
      errors.batchClass = "Vui lòng chọn lớp";
    }

    // Validate due date (required)
    if (!batchDueDate) {
      errors.batchDueDate = "Hạn thanh toán không được để trống";
    }

    return errors;
  };

  // Handle batch tuition form submission
  const handleBatchTuitionSubmit = async (e) => {
    e.preventDefault();

    // Parse formatted values to numbers
    const parsedBatchAmount = batchAmountFormatted ? parseCurrencyInput(batchAmountFormatted) : batchAmount;

    // Update the actual value with parsed value
    if (batchAmountFormatted) {
      setBatchAmount(parsedBatchAmount);
    }

    // Validate form
    const errors = validateBatchTuitionForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API call
      const batchData = {
        month: batchMonth,
        dueDate: batchDueDate,
        batchClass,
        note: batchNote
      };

      // Only include expectedAmount if it's provided
      if (parsedBatchAmount) {
        batchData.expectedAmount = Number(parsedBatchAmount);
      }

      // Call API to create batch tuition payments
      await dispatch(createBatchTuitionPayments(batchData));

      // Close panel and refresh data
      closeRightPanel();
      dispatch(fetchTuitionPayments({
        page: page,
        pageSize,
        search: inputValue,
        sortOrder: "DESC"
      }));
    } catch (error) {
      console.error("Error creating batch tuition payments:", error);
      setFormErrors({ submit: "Có lỗi xảy ra khi tạo học phí hàng loạt" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = (id) => {
    setPaymentToDelete(id);
    setShowConfirmModal(true);
  };

  const confirmDelete = async () => {
    dispatch(deleteTuitionPayment(paymentToDelete));
    setShowConfirmModal(false);
  };

  const cancelDelete = () => {
    setShowConfirmModal(false);
    setPaymentToDelete(null);
  };

  // getStatusBadge function removed - replaced with inline status display

  if (loading) {
    return <LoadingSpinner />;
  }

  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {
    return (
      <button
        onClick={onClick}
        className="flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam"
      >
        {icon}
        <span>{text}</span>
      </button>
    );
  };

  const iconAdd = (
    <div data-svg-wrapper className="relative">
      <Plus size={16} />
    </div>
  );

  const iconBatch = (
    <div data-svg-wrapper className="relative">
      <FileText size={16} />
    </div>
  );

  const iconCalendar = (
    <div data-svg-wrapper className="relative">
      <Calendar size={16} />
    </div>
  );

  const iconUsers = (
    <div data-svg-wrapper className="relative">
      <Users size={16} />
    </div>
  );

  return (
    <AdminLayout>
      <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4">
        Quản lý thanh toán học phí
      </div>

      <div className="flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6">
        <div className="flex-1">
          <div className="flex flex-wrap gap-2 items-center mb-4">
            <div className="w-[300px] relative">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                className="absolute left-[1rem] top-1/2 transform -translate-y-1/2"
              >
                <path
                  d="M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z"
                  stroke="#131214"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <input
                type="text"
                placeholder="Tìm kiếm theo tên học sinh, lớp học..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam"
              />
            </div>

            {/* Bộ lọc */}
            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterMonth}
                onChange={(e) => setFilterMonth(e.target.value)}
              >
                <option value="">Tháng</option>
                {Array.from({ length: 12 }, (_, i) => {
                  const month = i + 1;
                  const year = new Date().getFullYear();
                  const monthStr = month < 10 ? `0${month}` : `${month}`;
                  return (
                    <option key={month} value={`${year}-${monthStr}`}>
                      {`Tháng ${month}/${year}`}
                    </option>
                  );
                })}
              </select>
            </div>

            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterIsPaid}
                onChange={(e) => setFilterIsPaid(e.target.value)}
              >
                <option value="">Trạng thái</option>
                <option value="true">Đã thanh toán</option>
                <option value="false">Chưa thanh toán</option>
              </select>
            </div>

            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterOverdue}
                onChange={(e) => setFilterOverdue(e.target.value)}
              >
                <option value="">Tình trạng hạn</option>
                <option value="true">Đã quá hạn</option>
                <option value="false">Chưa quá hạn</option>
              </select>
            </div>

            <div className="w-[150px]">
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={filterClass}
                onChange={(e) => setFilterClass(e.target.value)}
              >
                <option value="">Khối</option>
                <option value="10">Khối 10</option>
                <option value="11">Khối 11</option>
                <option value="12">Khối 12</option>
              </select>
            </div>

            {/* Tìm kiếm lớp học */}
            <div className="w-[250px]">
              <ClassSearchInput
                value={classSearchTerm}
                selectedClassId={filterClassId}
                onChange={setClassSearchTerm}
                onSelect={handleSelectClass}
                onClear={handleClearClassSelection}
                placeholder="Tìm kiếm lớp học..."
              />
            </div>

            <button
              onClick={handleSearch}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              Tìm kiếm
            </button>


          </div>

          <div className="flex flex-wrap gap-2 items-center">
            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm thanh toán'} onClick={handleAdd} />
            <ButtonFunctionBarAdmin icon={iconCalendar} text={'Tạo học phí hàng loạt'} onClick={handleCreateBatchTuition} />
          </div>
        </div>
      </div>

      {tuitionPayments.length === 0 ? (
        <div className="bg-white shadow-md rounded-lg p-6 text-center">
          <p className="text-gray-500">Không có dữ liệu thanh toán học phí nào.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
            <thead className="bg-gray-100">
              <tr>
                <th className="py-3 px-4 text-left">STT</th>
                <th className="py-3 px-4 text-left">Học sinh</th>
                <th className="py-3 px-4 text-left">Lớp</th>
                <th className="py-3 px-4 text-left">Trường</th>
                <th className="py-3 px-4 text-left">
                  <div className="flex flex-col">
                    <span className="font-medium">Số điện thoại</span>
                    <span className="text-xs text-gray-500">(Số điện thoại của phụ huynh)</span>
                    <span className="text-xs text-gray-500">(Số điện thoại của học sinh)</span>
                  </div>
                </th>
                <th className="py-3 px-4 text-left">Tháng</th>
                <th className="py-3 px-4 text-left">Ngày thanh toán</th>
                <th className="py-3 px-4 text-left">Hạn thanh toán</th>
                <th className="py-3 px-4 text-left">Trạng thái</th>
                <th className="py-3 px-4 text-left">Ghi chú</th>
                <th className="py-3 px-4 text-left">Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {tuitionPayments.map((payment, index) => (
                <tr
                  key={payment.id}
                  className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}
                >
                  <td className="py-3 px-4">
                    {(page - 1) * pageSize + index + 1}
                  </td>
                  <td className="py-3 px-4">
                    {payment.user?.lastName + " " + payment.user?.firstName || "N/A"}
                  </td>
                  <td className="py-3 px-4">{payment.user?.class || "N/A"}</td>
                  <td className="py-3 px-4">{payment.user?.highSchool || "N/A"}</td>
                  <td className="py-3 px-4 align-top">
                    <div className="flex flex-col gap-y-0.5">
                      <span>{payment.user?.phone || "N/A"}</span>
                      <span>{payment.user?.password || "Không có mật khẩu"}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">{payment.monthFormatted}</td>
                  <td className="py-3 px-4">
                    {payment.paymentDate
                      ? new Date(payment.paymentDate).toLocaleDateString("vi-VN")
                      : "Chưa thanh toán"}
                  </td>
                  <td className="py-3 px-4">
                    {new Date(payment.dueDate).toLocaleDateString("vi-VN")}
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${payment.isPaid
                      ? 'bg-green-100 text-green-800'
                      : payment.isOverdue
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                      }`}>
                      {payment.isPaid
                        ? 'Đã thanh toán'
                        : payment.isOverdue
                          ? 'Quá hạn'
                          : 'Chưa thanh toán'
                      }
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-sm text-gray-600" title={payment.note}>
                      {payment.note ? (payment.note.length > 20 ? payment.note.substring(0, 20) + '...' : payment.note) : '-'}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(payment.id, payment.userId, payment.month)}
                        className="text-blue-500 hover:text-blue-700"
                        title="Xem chi tiết"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEdit(payment.id)}
                        className="text-yellow-500 hover:text-yellow-700"
                        title="Chỉnh sửa"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(payment.id)}
                        className="text-red-500 hover:text-red-700"
                        title="Xóa"
                      >
                        <Trash size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {viewMode === "table" && (
        <div className="mt-6">
          <Pagination
            currentPage={page}
            onPageChange={(page) => dispatch(setCurrentPage(page))}
            totalItems={total}
            limit={pageSize}
          />

        </div>
      )}


      {/* Right Panel for Batch Operations */}
      {showRightPanel && (
        <div className="fixed inset-y-0 right-0 w-100 bg-white shadow-lg z-50 overflow-y-auto">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-semibold">
              {rightPanelType === "add" && "Thêm thanh toán học phí"}
              {rightPanelType === "batchByMonth" && "Tạo học phí hàng loạt"}
              {rightPanelType === "edit" && "Chỉnh sửa thanh toán học phí"}
              {rightPanelType === "view" && "Chi tiết thanh toán học phí"}
            </h2>
            <button
              onClick={closeRightPanel}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
          </div>

          <div>
            {rightPanelType === "add" && (
              <div className="p-4">
                <p className="mb-4">Thêm khoản thanh toán học phí mới cho học sinh.</p>
                {/* Form content for adding new payment */}
                <form className="space-y-4" onSubmit={handleAddTuitionSubmit}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Học sinh <span className="text-red-500">*</span></label>
                    <UserSearchInput
                      value={addUserSearchTerm}
                      selectedUserId={addUserId}
                      onChange={setAddUserSearchTerm}
                      onSelect={(user) => {
                        setAddUserId(user.id);
                        setAddUserSearchTerm(`${user.lastName} ${user.firstName}`);
                      }}
                      onClear={() => {
                        setAddUserId("");
                        setAddUserSearchTerm("");
                      }}
                      placeholder="Tìm kiếm học sinh..."
                      role="STUDENT"
                    />
                    {formErrors.userId && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.userId}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tháng <span className="text-red-500">*</span></label>
                    <input
                      type="month"
                      className={`w-full px-3 py-2 border ${formErrors.month ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={addMonth}
                      onChange={(e) => setAddMonth(e.target.value)}
                    />
                    {formErrors.month && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.month}
                      </p>
                    )}
                  </div>
                  {/* Removed expectedAmount and paidAmount fields - no longer needed */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ngày thanh toán</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      value={addPaymentDate}
                      onChange={(e) => setAddPaymentDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán <span className="text-red-500">*</span></label>
                    <input
                      type="date"
                      className={`w-full px-3 py-2 border ${formErrors.dueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={addDueDate}
                      onChange={(e) => setAddDueDate(e.target.value)}
                    />
                    {formErrors.dueDate && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.dueDate}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái thanh toán</label>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="addIsPaid"
                        checked={addIsPaid}
                        onChange={(e) => setAddIsPaid(e.target.checked)}
                        className="mr-2"
                      />
                      <label htmlFor="addIsPaid" className="text-sm text-gray-700">
                        Đã thanh toán
                      </label>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                      value={addNote}
                      onChange={(e) => setAddNote(e.target.value)}
                    ></textarea>
                  </div>
                  {formErrors.submit && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
                      <AlertCircle size={20} className="mr-2 mt-0.5 flex-shrink-0" />
                      <p>{formErrors.submit}</p>
                    </div>
                  )}
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Lưu thanh toán"}
                  </button>
                </form>
              </div>
            )}
            {rightPanelType === "batchByMonth" && (
              <div className="p-4">
                <p className="mb-4">Tạo học phí hàng loạt cho tất cả học sinh trong một tháng.</p>
                {/* Form content for batch tuition by month */}
                <form className="space-y-4" onSubmit={(e) => handleBatchTuitionSubmit(e)}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tháng <span className="text-red-500">*</span></label>
                    <input
                      type="month"
                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchMonth}
                      onChange={(e) => setBatchMonth(e.target.value)}
                      required
                    />
                    {formErrors.batchMonth && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchMonth}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Số tiền cần đóng <span className="text-gray-500 text-xs font-normal">(không bắt buộc)</span>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      placeholder="Nhập số tiền hoặc để trống để tự động tính"
                      value={batchAmountFormatted}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\./g, "");
                        if (!isNaN(value) || value === "") {
                          setBatchAmountFormatted(value ? formatNumberWithDots(parseInt(value, 10)) : "");
                          setBatchAmount(value ? parseInt(value, 10) : "");
                        }
                      }}
                    />
                    {formErrors.batchAmount && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchAmount}
                      </p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      Nếu để trống, hệ thống sẽ tự động tính dựa trên các lớp học sinh đã tham gia
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Lớp <span className="text-red-500">*</span></label>
                    <select
                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchClass}
                      onChange={(e) => setBatchClass(e.target.value)}
                      required
                    >
                      <option value="">Chọn lớp</option>
                      <option value="10">Lớp 10</option>
                      <option value="11">Lớp 11</option>
                      <option value="12">Lớp 12</option>
                    </select>
                    {formErrors.batchClass && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchClass}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán <span className="text-red-500">*</span></label>
                    <input
                      type="date"
                      className={`w-full px-3 py-2 border ${formErrors.batchDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchDueDate}
                      onChange={(e) => setBatchDueDate(e.target.value)}
                      required
                    />
                    {formErrors.batchDueDate && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchDueDate}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                      value={batchNote}
                      onChange={(e) => setBatchNote(e.target.value)}
                    ></textarea>
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Tạo học phí hàng loạt"}
                  </button>
                </form>
              </div>
            )}

            {rightPanelType === "edit" && (
              <div className="p-4">
                <p className="mb-4">Chỉnh sửa thông tin thanh toán học phí.</p>
                {/* Form content for editing tuition payment */}
                <form className="space-y-4" onSubmit={(e) => handleUpdateTuitionPayment(e)}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ngày thanh toán</label>
                    <input
                      type="date"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      value={editPaymentDate}
                      onChange={(e) => setEditPaymentDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Hạn thanh toán <span className="text-red-500">*</span></label>
                    <input
                      type="date"
                      className={`w-full px-3 py-2 border ${formErrors.editDueDate ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={editDueDate}
                      onChange={(e) => setEditDueDate(e.target.value)}
                    />
                    {formErrors.editDueDate && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.editDueDate}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Trạng thái thanh toán</label>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="editIsPaid"
                        checked={editIsPaid}
                        onChange={(e) => setEditIsPaid(e.target.checked)}
                        className="mr-2"
                      />
                      <label htmlFor="editIsPaid" className="text-sm text-gray-700">
                        Đã thanh toán
                      </label>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                      value={editNote}
                      onChange={(e) => setEditNote(e.target.value)}
                    ></textarea>
                  </div>
                  {formErrors.submit && (
                    <p className="text-sm text-red-500 flex items-center">
                      <AlertCircle size={14} className="mr-1" /> {formErrors.submit}
                    </p>
                  )}
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Lưu thay đổi"}
                  </button>
                </form>
              </div>
            )}

            {rightPanelType === "view" && (
              <div>
                {viewLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                  </div>
                ) : tuitionPayment ? (
                  <div className="space-y-6 mb-4">
                    <div ref={detailsContainerRef} className="space-y-6 p-6 bg-white rounded-lg">
                      {/* Header cho bản in */}
                      <div className="flex items-center justify-between border-b pb-4 mb-2">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3">
                            TTB
                          </div>
                          <div>
                            <h2 className="text-xl font-bold text-gray-800">Toán Thầy Bee</h2>
                            <p className="text-sm text-gray-500">Chi tiết thanh toán học phí</p>
                            <p className="text-sm text-gray-500">Ngày xuất: {new Date().toLocaleDateString('vi-VN')}</p>
                          </div>
                        </div>

                      </div>

                      {/* Thông tin học sinh */}
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="text-lg font-semibold mb-2">Thông tin học sinh</h3>
                        <div className="space-y-2">
                          <p><span className="font-medium">Họ tên:</span> {tuitionPayment.user?.lastName} {tuitionPayment.user?.firstName}</p>
                          <p><span className="font-medium">Số điện thoại:</span> {tuitionPayment.user?.phone || 'Không có'}</p>
                          <p><span className="font-medium">Lớp:</span> {tuitionPayment.user?.class || 'Không có'}</p>
                          <p><span className="font-medium">Trường:</span> {tuitionPayment.user?.highSchool || 'Không có'}</p>
                        </div>
                      </div>

                      {/* Thông tin thanh toán */}
                      <div>
                        <h3 className="text-lg font-semibold mb-2">Thông tin thanh toán</h3>
                        <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-2">
                          <p><span className="font-medium">Tháng:</span> {tuitionPayment.monthFormatted}</p>
                          <p><span className="font-medium">Số tiền cần đóng:</span> {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>
                          <p><span className="font-medium">Số tiền đã đóng:</span> {formatCurrency(tuitionPayment.paidAmount || 0)}</p>
                          <p><span className="font-medium">Còn lại:</span> {formatCurrency((tuitionPayment.expectedAmount || 0) - (tuitionPayment.paidAmount || 0))}</p>
                          <p>
                            <span className="font-medium">Trạng thái:</span> {' '}
                            <span className={`px-2 py-1 rounded-full text-xs ${tuitionPayment.status === 'PAID' ? 'bg-green-100 text-green-800' :
                              tuitionPayment.status === 'UNPAID' ? 'bg-red-100 text-red-800' :
                                tuitionPayment.status === 'PARTIAL' ? 'bg-blue-100 text-blue-800' :
                                  'bg-yellow-100 text-yellow-800'
                              }`}>
                              {tuitionPayment.status === 'PAID' ? 'Đã thanh toán' :
                                tuitionPayment.status === 'UNPAID' ? 'Chưa thanh toán' :
                                  tuitionPayment.status === 'PARTIAL' ? 'Thanh toán một phần' :
                                    'Quá hạn'}
                            </span>
                          </p>
                          <p><span className="font-medium">Ngày thanh toán:</span> {tuitionPayment.paymentDate ? new Date(tuitionPayment.paymentDate).toLocaleDateString() : 'Chưa thanh toán'}</p>
                          <p><span className="font-medium">Hạn thanh toán:</span> {tuitionPayment.dueDate ? new Date(tuitionPayment.dueDate).toLocaleDateString() : 'Không có'}</p>
                          {tuitionPayment.note && <p><span className="font-medium">Ghi chú:</span> {tuitionPayment.note}</p>}
                        </div>
                      </div>

                      {/* Danh sách học phí lớp */}
                      {studentClassTuitionsAdmin && studentClassTuitionsAdmin.classTuitions && studentClassTuitionsAdmin.classTuitions.length > 0 && (
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Danh sách học phí các lớp</h3>
                          <div className="space-y-3">
                            {studentClassTuitionsAdmin.classTuitions.map((tuition) => (
                              <div key={tuition.id} className="bg-white border border-gray-200 rounded-lg p-3">
                                <div className="flex justify-between items-center mb-2">
                                  <h4 className="font-medium">{tuition.className}</h4>
                                  <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                    Lớp {tuition.classGrade}
                                  </span>
                                </div>
                                <p><span className="text-sm text-gray-500">Học phí:</span> {formatCurrency(tuition.amount)}</p>
                                <p><span className="text-sm text-gray-500">Ngày tham gia:</span> {new Date(tuition.joinDate).toLocaleDateString()}</p>
                                {tuition.note && <p><span className="text-sm text-gray-500">Ghi chú:</span> {tuition.note}</p>}
                              </div>
                            ))}
                          </div>
                          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                            <p className="font-medium">Tổng học phí các lớp: {formatCurrency(studentClassTuitionsAdmin.totalAmount || 0)}</p>
                            <p className="font-medium">Số tiền cần đóng: {formatCurrency(tuitionPayment.expectedAmount || 0)}</p>
                          </div>
                        </div>
                      )}

                      {/* Footer cho bản in */}
                      <div className="border-t pt-4 mt-6">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm text-gray-500">Mọi thắc mắc xin liên hệ:</p>
                            <p className="text-sm font-medium">Hotline: 0333726202</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-500">Chữ ký xác nhận</p>
                            <div className="h-16"></div>
                            <p className="text-sm font-medium">Người thu học phí</p>
                            <p className="text-sm font-medium">Triệu Minh</p>
                          </div>
                        </div>
                      </div>
                    </div> {/* End of detailsContainerRef div */}

                    {/* Nút chỉnh sửa và xuất ảnh */}
                    <div className="flex gap-2 flex-col mx-4">
                      <button
                        onClick={() => handleEdit(tuitionPayment.id)}
                        className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
                      >
                        Chỉnh sửa thanh toán
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    Không tìm thấy thông tin thanh toán
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      <ConfirmModal
        isOpen={showConfirmModal}
        onConfirm={confirmDelete}
        text="Bạn có chắc chắn muốn xóa khoản đóng học phí này?"
        onClose={cancelDelete}
      />
    </AdminLayout>
  );
};

export default TuitionPaymentList;
