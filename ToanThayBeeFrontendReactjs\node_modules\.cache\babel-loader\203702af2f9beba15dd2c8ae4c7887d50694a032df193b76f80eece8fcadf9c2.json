{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\notification\\\\NotificationPanel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bell, X, Check, ExternalLink, ChevronDown } from 'lucide-react';\nimport { useNavigate } from 'react-router-dom';\nimport { socket } from '../../services/socket';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { formatDistanceToNow } from 'date-fns';\nimport { vi } from 'date-fns/locale';\nimport { fetchNotifications, markAsRead, markAllAsRead, addNotification, updateUnreadCount, resetNotifications } from '../../features/notification/notificationSlice';\nimport { CreditCard } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NotificationPanel = _ref => {\n  _s();\n  let {\n    isOpen,\n    onClose\n  } = _ref;\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const user = useSelector(state => state.auth.user);\n  const {\n    notifications,\n    unreadCount,\n    loading,\n    hasMore\n  } = useSelector(state => state.notifications);\n  const [page, setPage] = useState(1);\n  const [isLoadingMore, setIsLoadingMore] = useState(false);\n  const ITEMS_PER_PAGE = 10;\n\n  // Mark a notification as read\n  const handleMarkAsRead = id => {\n    if (!user) return;\n\n    // Also update via Redux/API\n    dispatch(markAsRead([id]));\n  };\n\n  // Mark all notifications as read\n  const handleMarkAllAsRead = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!user || unreadCount === 0) return;\n    // Also update via Redux/API\n    dispatch(markAllAsRead());\n  };\n\n  // Handle notification click\n  const handleNotificationClick = notification => {\n    handleMarkAsRead(notification.id);\n    if (notification.link) {\n      navigate(notification.link);\n      onClose();\n    }\n  };\n\n  // Load more notifications\n  const loadMore = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    setPage(prevPage => prevPage + 1);\n  };\n\n  // Fetch initial notifications when panel is opened\n  useEffect(() => {\n    if (isOpen && user) {\n      // Reset page and notifications when panel is opened\n      setPage(1);\n      dispatch(resetNotifications());\n\n      // Fetch first page of notifications\n      dispatch(fetchNotifications({\n        limit: ITEMS_PER_PAGE,\n        offset: 0\n      }));\n    }\n  }, [isOpen, user, dispatch]);\n\n  // Load more notifications when page changes\n  useEffect(() => {\n    if (page > 1 && isOpen && user) {\n      setIsLoadingMore(true);\n      dispatch(fetchNotifications({\n        limit: ITEMS_PER_PAGE,\n        offset: (page - 1) * ITEMS_PER_PAGE\n      })).finally(() => {\n        setIsLoadingMore(false);\n      });\n    }\n  }, [page, isOpen, user, dispatch, ITEMS_PER_PAGE]);\n\n  // Get icon based on notification type\n  const getNotificationIcon = type => {\n    switch (type) {\n      case 'exam':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-1.5 md:p-2 bg-blue-100 rounded-full\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:h-5 text-blue-600\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"8\",\n              y: \"2\",\n              width: \"8\",\n              height: \"4\",\n              rx: \"1\",\n              ry: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 14l2 2 4-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this);\n      case 'reminder':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-1.5 md:p-2 bg-yellow-100 rounded-full\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:h-5 text-yellow-600\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M13.73 21a2 2 0 0 1-3.46 0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this);\n      case 'result':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-1.5 md:p-2 bg-green-100 rounded-full\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:h-5 text-green-600\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n              points: \"22 4 12 14.01 9 11.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this);\n      case 'tuition':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-1.5 md:p-2 bg-yellow-100 rounded-full\",\n          children: /*#__PURE__*/_jsxDEV(CreditCard, {\n            className: \"w-4 h-4 md:w-5 md:h-5 text-yellow-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-1.5 md:p-2 bg-gray-100 rounded-full\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 md:w-5 md:h-5 text-gray-600\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"8\",\n              x2: \"12\",\n              y2: \"12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"12\",\n              y1: \"16\",\n              x2: \"12.01\",\n              y2: \"16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        transition: {\n          duration: 0.2\n        },\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -10,\n          x: 0\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          x: 0\n        },\n        exit: {\n          opacity: 0,\n          y: -10,\n          x: 0\n        },\n        transition: {\n          duration: 0.2\n        },\n        className: \"fixed\\r md:right-16 md:top-16 md:w-[28rem] md:max-h-[80vh] md:rounded-lg\\r right-0 top-0 w-full h-full max-h-screen rounded-none\\r bg-white shadow-lg border border-gray-200 z-50 flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center p-3 md:p-4 border-b border-gray-100 bg-white sticky top-0 z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-base md:text-lg font-semibold text-gray-800\",\n            children: \"Th\\xF4ng b\\xE1o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 md:gap-2\",\n            children: [unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleMarkAllAsRead,\n              className: \"text-xs text-sky-600 hover:text-sky-700 flex items-center gap-1 px-2 py-1 rounded-md hover:bg-sky-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Check, {\n                size: 12,\n                className: \"md:w-3.5 md:h-3.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"hidden sm:inline\",\n                children: \"\\u0110\\xE1nh d\\u1EA5u t\\u1EA5t c\\u1EA3 \\u0111\\xE3 \\u0111\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"sm:hidden\",\n                children: \"\\u0110\\xE3 \\u0111\\u1ECDc\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"text-gray-400 hover:text-gray-600 p-1 rounded-md hover:bg-gray-100 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                size: 16,\n                className: \"md:w-4.5 md:h-4.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-y-auto flex-1 overscroll-contain\",\n          children: loading && page === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 md:p-6 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 md:h-6 md:w-6 border-b-2 border-sky-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 17\n          }, this) : notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 md:p-6 text-center text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(Bell, {\n              size: 32,\n              className: \"mx-auto mb-3 text-gray-300 md:w-10 md:h-10\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm md:text-base\",\n              children: \"Kh\\xF4ng c\\xF3 th\\xF4ng b\\xE1o n\\xE0o.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"divide-y divide-gray-100\",\n              children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n                onClick: () => handleNotificationClick(notification),\n                className: \"p-3 md:p-4 hover:bg-gray-50 cursor-pointer transition-colors \".concat(!notification.isRead ? 'bg-sky-50' : ''),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2 md:gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: getNotificationIcon(notification.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-start gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-sm font-semibold line-clamp-2 \".concat(!notification.isRead ? 'text-sky-700' : 'text-gray-800'),\n                        children: notification.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 214,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500 flex-shrink-0\",\n                        children: notification.time\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mt-1 line-clamp-3\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 29\n                    }, this), notification.link && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mt-2 text-xs text-sky-600 hover:text-sky-700\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"Xem chi ti\\u1EBFt\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(ExternalLink, {\n                        size: 10,\n                        className: \"ml-1 md:w-3 md:h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 31\n                    }, this), !notification.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-2 h-2 bg-sky-500 rounded-full mt-2 md:hidden\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 25\n                }, this)\n              }, notification.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this), hasMore && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 text-center border-t border-gray-100 bg-white\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: loadMore,\n                disabled: isLoadingMore,\n                className: \"px-3 py-2 bg-sky-50 hover:bg-sky-100 text-sky-600 rounded-md transition-colors flex items-center gap-1.5 mx-auto text-xs md:text-sm\",\n                children: isLoadingMore ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-sky-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u0110ang t\\u1EA3i...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(ChevronDown, {\n                    size: 12,\n                    className: \"md:w-3.5 md:h-3.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"T\\u1EA3i th\\xEAm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 border-t border-gray-100 text-center bg-white\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.preventDefault();\n              e.stopPropagation();\n              navigate('/notifications');\n              onClose();\n            },\n            className: \"text-sm text-sky-600 hover:text-sky-700 px-3 py-1 rounded-md hover:bg-sky-50 transition-colors\",\n            children: \"Xem t\\u1EA5t c\\u1EA3 th\\xF4ng b\\xE1o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationPanel, \"OfK3P7S0/VeycjbvpU9im4dLb6M=\", false, function () {\n  return [useNavigate, useDispatch, useSelector, useSelector];\n});\n_c = NotificationPanel;\nexport default NotificationPanel;\nvar _c;\n$RefreshReg$(_c, \"NotificationPanel\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "AnimatePresence", "Bell", "X", "Check", "ExternalLink", "ChevronDown", "useNavigate", "socket", "useSelector", "useDispatch", "formatDistanceToNow", "vi", "fetchNotifications", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "addNotification", "updateUnreadCount", "resetNotifications", "CreditCard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NotificationPanel", "_ref", "_s", "isOpen", "onClose", "navigate", "dispatch", "user", "state", "auth", "notifications", "unreadCount", "loading", "hasMore", "page", "setPage", "isLoadingMore", "setIsLoadingMore", "ITEMS_PER_PAGE", "handleMarkAsRead", "id", "handleMarkAllAsRead", "e", "preventDefault", "stopPropagation", "handleNotificationClick", "notification", "link", "loadMore", "prevPage", "limit", "offset", "finally", "getNotificationIcon", "type", "className", "children", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "y", "width", "height", "rx", "ry", "points", "cx", "cy", "r", "x1", "y1", "x2", "y2", "div", "initial", "opacity", "animate", "exit", "transition", "duration", "onClick", "size", "length", "map", "concat", "isRead", "title", "time", "message", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/notification/NotificationPanel.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Bell, X, Check, ExternalLink, ChevronDown } from 'lucide-react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { socket } from '../../services/socket';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport {\r\n  fetchNotifications,\r\n  markAsRead,\r\n  markAllAsRead,\r\n  addNotification,\r\n  updateUnreadCount,\r\n  resetNotifications\r\n} from '../../features/notification/notificationSlice';\r\nimport { CreditCard } from 'lucide-react';\r\n\r\nconst NotificationPanel = ({ isOpen, onClose }) => {\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const user = useSelector((state) => state.auth.user);\r\n  const { notifications, unreadCount, loading, hasMore } = useSelector((state) => state.notifications);\r\n  const [page, setPage] = useState(1);\r\n  const [isLoadingMore, setIsLoadingMore] = useState(false);\r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  // Mark a notification as read\r\n  const handleMarkAsRead = (id) => {\r\n    if (!user) return;\r\n    \r\n    // Also update via Redux/API\r\n    dispatch(markAsRead([id]));\r\n  };\r\n\r\n  // Mark all notifications as read\r\n  const handleMarkAllAsRead = (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (!user || unreadCount === 0) return;\r\n    // Also update via Redux/API\r\n    dispatch(markAllAsRead());\r\n  };\r\n\r\n  // Handle notification click\r\n  const handleNotificationClick = (notification) => {\r\n    handleMarkAsRead(notification.id);\r\n    if (notification.link) {\r\n      navigate(notification.link);\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  // Load more notifications\r\n  const loadMore = (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setPage(prevPage => prevPage + 1);\r\n  };\r\n\r\n  // Fetch initial notifications when panel is opened\r\n  useEffect(() => {\r\n    if (isOpen && user) {\r\n      // Reset page and notifications when panel is opened\r\n      setPage(1);\r\n      dispatch(resetNotifications());\r\n\r\n      // Fetch first page of notifications\r\n      dispatch(fetchNotifications({\r\n        limit: ITEMS_PER_PAGE,\r\n        offset: 0\r\n      }));\r\n    }\r\n  }, [isOpen, user, dispatch]);\r\n\r\n  // Load more notifications when page changes\r\n  useEffect(() => {\r\n    if (page > 1 && isOpen && user) {\r\n      setIsLoadingMore(true);\r\n\r\n      dispatch(fetchNotifications({\r\n        limit: ITEMS_PER_PAGE,\r\n        offset: (page - 1) * ITEMS_PER_PAGE\r\n      }))\r\n        .finally(() => {\r\n          setIsLoadingMore(false);\r\n        });\r\n    }\r\n  }, [page, isOpen, user, dispatch, ITEMS_PER_PAGE]);\r\n\r\n  // Get icon based on notification type\r\n  const getNotificationIcon = (type) => {\r\n    switch (type) {\r\n      case 'exam':\r\n        return (\r\n          <div className=\"p-1.5 md:p-2 bg-blue-100 rounded-full\">\r\n            <svg className=\"w-4 h-4 md:w-5 md:h-5 text-blue-600\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"></path>\r\n              <rect x=\"8\" y=\"2\" width=\"8\" height=\"4\" rx=\"1\" ry=\"1\"></rect>\r\n              <path d=\"M9 14l2 2 4-4\"></path>\r\n            </svg>\r\n          </div>\r\n        );\r\n      case 'reminder':\r\n        return (\r\n          <div className=\"p-1.5 md:p-2 bg-yellow-100 rounded-full\">\r\n            <svg className=\"w-4 h-4 md:w-5 md:h-5 text-yellow-600\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9\"></path>\r\n              <path d=\"M13.73 21a2 2 0 0 1-3.46 0\"></path>\r\n            </svg>\r\n          </div>\r\n        );\r\n      case 'result':\r\n        return (\r\n          <div className=\"p-1.5 md:p-2 bg-green-100 rounded-full\">\r\n            <svg className=\"w-4 h-4 md:w-5 md:h-5 text-green-600\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\r\n              <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\r\n            </svg>\r\n          </div>\r\n        );\r\n      case 'tuition':\r\n        return (\r\n          <div className=\"p-1.5 md:p-2 bg-yellow-100 rounded-full\">\r\n            <CreditCard className=\"w-4 h-4 md:w-5 md:h-5 text-yellow-600\" />\r\n          </div>\r\n        )\r\n      default:\r\n        return (\r\n          <div className=\"p-1.5 md:p-2 bg-gray-100 rounded-full\">\r\n            <svg className=\"w-4 h-4 md:w-5 md:h-5 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n              <line x1=\"12\" y1=\"8\" x2=\"12\" y2=\"12\"></line>\r\n              <line x1=\"12\" y1=\"16\" x2=\"12.01\" y2=\"16\"></line>\r\n            </svg>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isOpen && (\r\n        <>\r\n          {/* Mobile Backdrop */}\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n            onClick={onClose}\r\n          />\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10, x: 0 }}\r\n            animate={{ opacity: 1, y: 0, x: 0 }}\r\n            exit={{ opacity: 0, y: -10, x: 0 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"fixed\r\n              md:right-16 md:top-16 md:w-[28rem] md:max-h-[80vh] md:rounded-lg\r\n              right-0 top-0 w-full h-full max-h-screen rounded-none\r\n              bg-white shadow-lg border border-gray-200 z-50 flex flex-col\"\r\n          >\r\n            {/* Header */}\r\n            <div className=\"flex justify-between items-center p-3 md:p-4 border-b border-gray-100 bg-white sticky top-0 z-10\">\r\n              <h3 className=\"text-base md:text-lg font-semibold text-gray-800\">Thông báo</h3>\r\n              <div className=\"flex items-center gap-1 md:gap-2\">\r\n                {unreadCount > 0 && (\r\n                  <button\r\n                    onClick={handleMarkAllAsRead}\r\n                    className=\"text-xs text-sky-600 hover:text-sky-700 flex items-center gap-1 px-2 py-1 rounded-md hover:bg-sky-50 transition-colors\"\r\n                  >\r\n                    <Check size={12} className=\"md:w-3.5 md:h-3.5\" />\r\n                    <span className=\"hidden sm:inline\">Đánh dấu tất cả đã đọc</span>\r\n                    <span className=\"sm:hidden\">Đã đọc</span>\r\n                  </button>\r\n                )}\r\n                <button\r\n                  onClick={onClose}\r\n                  className=\"text-gray-400 hover:text-gray-600 p-1 rounded-md hover:bg-gray-100 transition-colors\"\r\n                >\r\n                  <X size={16} className=\"md:w-4.5 md:h-4.5\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Notification list */}\r\n            <div className=\"overflow-y-auto flex-1 overscroll-contain\">\r\n              {loading && page === 1 ? (\r\n                <div className=\"p-4 md:p-6 flex justify-center\">\r\n                  <div className=\"animate-spin rounded-full h-5 w-5 md:h-6 md:w-6 border-b-2 border-sky-500\"></div>\r\n                </div>\r\n              ) : notifications.length === 0 ? (\r\n                <div className=\"p-4 md:p-6 text-center text-gray-500\">\r\n                  <Bell size={32} className=\"mx-auto mb-3 text-gray-300 md:w-10 md:h-10\" />\r\n                  <p className=\"text-sm md:text-base\">Không có thông báo nào.</p>\r\n                </div>\r\n              ) : (\r\n                <>\r\n                  <div className=\"divide-y divide-gray-100\">\r\n                    {notifications.map((notification) => (\r\n                      <div\r\n                        key={notification.id}\r\n                        onClick={() => handleNotificationClick(notification)}\r\n                        className={`p-3 md:p-4 hover:bg-gray-50 cursor-pointer transition-colors ${!notification.isRead ? 'bg-sky-50' : ''}`}\r\n                      >\r\n                        <div className=\"flex gap-2 md:gap-3\">\r\n                          <div className=\"flex-shrink-0\">\r\n                            {getNotificationIcon(notification.type)}\r\n                          </div>\r\n                          <div className=\"flex-1 min-w-0\">\r\n                            <div className=\"flex justify-between items-start gap-2\">\r\n                              <h4 className={`text-sm font-semibold line-clamp-2 ${!notification.isRead ? 'text-sky-700' : 'text-gray-800'}`}>\r\n                                {notification.title}\r\n                              </h4>\r\n                              <span className=\"text-xs text-gray-500 flex-shrink-0\">{notification.time}</span>\r\n                            </div>\r\n                            <p className=\"text-sm text-gray-600 mt-1 line-clamp-3\">{notification.message}</p>\r\n                            {notification.link && (\r\n                              <div className=\"flex items-center mt-2 text-xs text-sky-600 hover:text-sky-700\">\r\n                                <span>Xem chi tiết</span>\r\n                                <ExternalLink size={10} className=\"ml-1 md:w-3 md:h-3\" />\r\n                              </div>\r\n                            )}\r\n                            {!notification.isRead && (\r\n                              <div className=\"w-2 h-2 bg-sky-500 rounded-full mt-2 md:hidden\"></div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n\r\n                  {/* Load more button */}\r\n                  {hasMore && (\r\n                    <div className=\"p-3 text-center border-t border-gray-100 bg-white\">\r\n                      <button\r\n                        onClick={loadMore}\r\n                        disabled={isLoadingMore}\r\n                        className=\"px-3 py-2 bg-sky-50 hover:bg-sky-100 text-sky-600 rounded-md transition-colors flex items-center gap-1.5 mx-auto text-xs md:text-sm\"\r\n                      >\r\n                        {isLoadingMore ? (\r\n                          <>\r\n                            <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-sky-500\"></div>\r\n                            <span>Đang tải...</span>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <ChevronDown size={12} className=\"md:w-3.5 md:h-3.5\" />\r\n                            <span>Tải thêm</span>\r\n                          </>\r\n                        )}\r\n                      </button>\r\n                    </div>\r\n                  )}\r\n                </>\r\n              )}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            <div className=\"p-3 border-t border-gray-100 text-center bg-white\">\r\n              <button\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  e.stopPropagation();\r\n                  navigate('/notifications');\r\n                  onClose();\r\n                }}\r\n                className=\"text-sm text-sky-600 hover:text-sky-700 px-3 py-1 rounded-md hover:bg-sky-50 transition-colors\"\r\n              >\r\n                Xem tất cả thông báo\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default NotificationPanel;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,IAAI,EAAEC,CAAC,EAAEC,KAAK,EAAEC,YAAY,EAAEC,WAAW,QAAQ,cAAc;AACxE,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SACEC,kBAAkB,EAClBC,UAAU,EACVC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,kBAAkB,QACb,+CAA+C;AACtD,SAASC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,iBAAiB,GAAGC,IAAA,IAAyB;EAAAC,EAAA;EAAA,IAAxB;IAAEC,MAAM;IAAEC;EAAQ,CAAC,GAAAH,IAAA;EAC5C,MAAMI,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAMuB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,IAAI,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC;EACpD,MAAM;IAAEG,aAAa;IAAEC,WAAW;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAG5B,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACE,aAAa,CAAC;EACpG,MAAM,CAACI,IAAI,EAAEC,OAAO,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM2C,cAAc,GAAG,EAAE;;EAEzB;EACA,MAAMC,gBAAgB,GAAIC,EAAE,IAAK;IAC/B,IAAI,CAACb,IAAI,EAAE;;IAEX;IACAD,QAAQ,CAAChB,UAAU,CAAC,CAAC8B,EAAE,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,CAAC,IAAK;IACjCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnB,IAAI,CAACjB,IAAI,IAAII,WAAW,KAAK,CAAC,EAAE;IAChC;IACAL,QAAQ,CAACf,aAAa,CAAC,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMkC,uBAAuB,GAAIC,YAAY,IAAK;IAChDP,gBAAgB,CAACO,YAAY,CAACN,EAAE,CAAC;IACjC,IAAIM,YAAY,CAACC,IAAI,EAAE;MACrBtB,QAAQ,CAACqB,YAAY,CAACC,IAAI,CAAC;MAC3BvB,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAMwB,QAAQ,GAAIN,CAAC,IAAK;IACtBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBT,OAAO,CAACc,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC;EACnC,CAAC;;EAED;EACAvD,SAAS,CAAC,MAAM;IACd,IAAI6B,MAAM,IAAII,IAAI,EAAE;MAClB;MACAQ,OAAO,CAAC,CAAC,CAAC;MACVT,QAAQ,CAACZ,kBAAkB,CAAC,CAAC,CAAC;;MAE9B;MACAY,QAAQ,CAACjB,kBAAkB,CAAC;QAC1ByC,KAAK,EAAEZ,cAAc;QACrBa,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAAC5B,MAAM,EAAEI,IAAI,EAAED,QAAQ,CAAC,CAAC;;EAE5B;EACAhC,SAAS,CAAC,MAAM;IACd,IAAIwC,IAAI,GAAG,CAAC,IAAIX,MAAM,IAAII,IAAI,EAAE;MAC9BU,gBAAgB,CAAC,IAAI,CAAC;MAEtBX,QAAQ,CAACjB,kBAAkB,CAAC;QAC1ByC,KAAK,EAAEZ,cAAc;QACrBa,MAAM,EAAE,CAACjB,IAAI,GAAG,CAAC,IAAII;MACvB,CAAC,CAAC,CAAC,CACAc,OAAO,CAAC,MAAM;QACbf,gBAAgB,CAAC,KAAK,CAAC;MACzB,CAAC,CAAC;IACN;EACF,CAAC,EAAE,CAACH,IAAI,EAAEX,MAAM,EAAEI,IAAI,EAAED,QAAQ,EAAEY,cAAc,CAAC,CAAC;;EAElD;EACA,MAAMe,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QACT,oBACErC,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDvC,OAAA;YAAKsC,SAAS,EAAC,qCAAqC;YAACE,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAP,QAAA,gBACxMvC,OAAA;cAAM+C,CAAC,EAAC;YAA0E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1FnD,OAAA;cAAMoD,CAAC,EAAC,GAAG;cAACC,CAAC,EAAC,GAAG;cAACC,KAAK,EAAC,GAAG;cAACC,MAAM,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5DnD,OAAA;cAAM+C,CAAC,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,UAAU;QACb,oBACEnD,OAAA;UAAKsC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDvC,OAAA;YAAKsC,SAAS,EAAC,uCAAuC;YAACE,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAP,QAAA,gBAC1MvC,OAAA;cAAM+C,CAAC,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7DnD,OAAA;cAAM+C,CAAC,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,QAAQ;QACX,oBACEnD,OAAA;UAAKsC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDvC,OAAA;YAAKsC,SAAS,EAAC,sCAAsC;YAACE,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAP,QAAA,gBACzMvC,OAAA;cAAM+C,CAAC,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpDnD,OAAA;cAAU0D,MAAM,EAAC;YAAuB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEnD,OAAA;UAAKsC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDvC,OAAA,CAACF,UAAU;YAACwC,SAAS,EAAC;UAAuC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAEV;QACE,oBACEnD,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDvC,OAAA;YAAKsC,SAAS,EAAC,qCAAqC;YAACE,KAAK,EAAC,4BAA4B;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAAAP,QAAA,gBACxMvC,OAAA;cAAQ2D,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC;YAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACxCnD,OAAA;cAAM8D,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5CnD,OAAA;cAAM8D,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,OAAO;cAACC,EAAE,EAAC;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;IAEZ;EACF,CAAC;EAED,oBACEnD,OAAA,CAACpB,eAAe;IAAA2D,QAAA,EACbjC,MAAM,iBACLN,OAAA,CAAAE,SAAA;MAAAqC,QAAA,gBAEEvC,OAAA,CAACrB,MAAM,CAACuF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrBG,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BlC,SAAS,EAAC,qDAAqD;QAC/DmC,OAAO,EAAElE;MAAQ;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAEFnD,OAAA,CAACrB,MAAM,CAACuF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEf,CAAC,EAAE,CAAC,EAAE;UAAED,CAAC,EAAE;QAAE,CAAE;QACtCiB,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEf,CAAC,EAAE,CAAC;UAAED,CAAC,EAAE;QAAE,CAAE;QACpCkB,IAAI,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEf,CAAC,EAAE,CAAC,EAAE;UAAED,CAAC,EAAE;QAAE,CAAE;QACnCmB,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BlC,SAAS,EAAC,iMAGqD;QAAAC,QAAA,gBAG/DvC,OAAA;UAAKsC,SAAS,EAAC,kGAAkG;UAAAC,QAAA,gBAC/GvC,OAAA;YAAIsC,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EnD,OAAA;YAAKsC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAC9CzB,WAAW,GAAG,CAAC,iBACdd,OAAA;cACEyE,OAAO,EAAEjD,mBAAoB;cAC7Bc,SAAS,EAAC,wHAAwH;cAAAC,QAAA,gBAElIvC,OAAA,CAACjB,KAAK;gBAAC2F,IAAI,EAAE,EAAG;gBAACpC,SAAS,EAAC;cAAmB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjDnD,OAAA;gBAAMsC,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChEnD,OAAA;gBAAMsC,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACT,eACDnD,OAAA;cACEyE,OAAO,EAAElE,OAAQ;cACjB+B,SAAS,EAAC,sFAAsF;cAAAC,QAAA,eAEhGvC,OAAA,CAAClB,CAAC;gBAAC4F,IAAI,EAAE,EAAG;gBAACpC,SAAS,EAAC;cAAmB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnD,OAAA;UAAKsC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvDxB,OAAO,IAAIE,IAAI,KAAK,CAAC,gBACpBjB,OAAA;YAAKsC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7CvC,OAAA;cAAKsC,SAAS,EAAC;YAA2E;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,GACJtC,aAAa,CAAC8D,MAAM,KAAK,CAAC,gBAC5B3E,OAAA;YAAKsC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnDvC,OAAA,CAACnB,IAAI;cAAC6F,IAAI,EAAE,EAAG;cAACpC,SAAS,EAAC;YAA4C;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzEnD,OAAA;cAAGsC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAuB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAENnD,OAAA,CAAAE,SAAA;YAAAqC,QAAA,gBACEvC,OAAA;cAAKsC,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtC1B,aAAa,CAAC+D,GAAG,CAAE/C,YAAY,iBAC9B7B,OAAA;gBAEEyE,OAAO,EAAEA,CAAA,KAAM7C,uBAAuB,CAACC,YAAY,CAAE;gBACrDS,SAAS,kEAAAuC,MAAA,CAAkE,CAAChD,YAAY,CAACiD,MAAM,GAAG,WAAW,GAAG,EAAE,CAAG;gBAAAvC,QAAA,eAErHvC,OAAA;kBAAKsC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCvC,OAAA;oBAAKsC,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3BH,mBAAmB,CAACP,YAAY,CAACQ,IAAI;kBAAC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACNnD,OAAA;oBAAKsC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BvC,OAAA;sBAAKsC,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDvC,OAAA;wBAAIsC,SAAS,wCAAAuC,MAAA,CAAwC,CAAChD,YAAY,CAACiD,MAAM,GAAG,cAAc,GAAG,eAAe,CAAG;wBAAAvC,QAAA,EAC5GV,YAAY,CAACkD;sBAAK;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACLnD,OAAA;wBAAMsC,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAEV,YAAY,CAACmD;sBAAI;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7E,CAAC,eACNnD,OAAA;sBAAGsC,SAAS,EAAC,yCAAyC;sBAAAC,QAAA,EAAEV,YAAY,CAACoD;oBAAO;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAChFtB,YAAY,CAACC,IAAI,iBAChB9B,OAAA;sBAAKsC,SAAS,EAAC,gEAAgE;sBAAAC,QAAA,gBAC7EvC,OAAA;wBAAAuC,QAAA,EAAM;sBAAY;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzBnD,OAAA,CAAChB,YAAY;wBAAC0F,IAAI,EAAE,EAAG;wBAACpC,SAAS,EAAC;sBAAoB;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CACN,EACA,CAACtB,YAAY,CAACiD,MAAM,iBACnB9E,OAAA;sBAAKsC,SAAS,EAAC;oBAAgD;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACtE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA1BDtB,YAAY,CAACN,EAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2BjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLnC,OAAO,iBACNhB,OAAA;cAAKsC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAChEvC,OAAA;gBACEyE,OAAO,EAAE1C,QAAS;gBAClBmD,QAAQ,EAAE/D,aAAc;gBACxBmB,SAAS,EAAC,qIAAqI;gBAAAC,QAAA,EAE9IpB,aAAa,gBACZnB,OAAA,CAAAE,SAAA;kBAAAqC,QAAA,gBACEvC,OAAA;oBAAKsC,SAAS,EAAC;kBAA6D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnFnD,OAAA;oBAAAuC,QAAA,EAAM;kBAAW;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eACxB,CAAC,gBAEHnD,OAAA,CAAAE,SAAA;kBAAAqC,QAAA,gBACEvC,OAAA,CAACf,WAAW;oBAACyF,IAAI,EAAE,EAAG;oBAACpC,SAAS,EAAC;kBAAmB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvDnD,OAAA;oBAAAuC,QAAA,EAAM;kBAAQ;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eACrB;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA,eACD;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnD,OAAA;UAAKsC,SAAS,EAAC,mDAAmD;UAAAC,QAAA,eAChEvC,OAAA;YACEyE,OAAO,EAAGhD,CAAC,IAAK;cACdA,CAAC,CAACC,cAAc,CAAC,CAAC;cAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;cACnBnB,QAAQ,CAAC,gBAAgB,CAAC;cAC1BD,OAAO,CAAC,CAAC;YACX,CAAE;YACF+B,SAAS,EAAC,gGAAgG;YAAAC,QAAA,EAC3G;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA,eACb;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;AAAC9C,EAAA,CArQIF,iBAAiB;EAAA,QACJjB,WAAW,EACXG,WAAW,EACfD,WAAW,EACiCA,WAAW;AAAA;AAAA+F,EAAA,GAJhEhF,iBAAiB;AAuQvB,eAAeA,iBAAiB;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}