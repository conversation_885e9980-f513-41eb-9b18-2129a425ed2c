# 🧪 Error Handling Test Cases

## 📋 Test Scenarios for <PERSON><PERSON>un<PERSON> + Error Handling

### 🔴 Network Error Test
```javascript
// Simulate network error
const mockNetworkError = {
    code: 'ERR_NETWORK',
    message: 'Network Error',
    name: 'AxiosError'
};

// Expected behavior:
1. Question button turns RED
2. Error message: "Lỗi mạng khi lưu câu trả lời. Vui lòng kiểm tra kết nối và thử lại."
3. Question added to errorQuestions array
4. Question removed from savingQuestions set
5. User can retry by changing answer
```

### 🟠 Server Error Test
```javascript
// Simulate server error
const mockServerError = {
    response: {
        status: 500,
        data: { message: 'Internal Server Error' }
    }
};

// Expected behavior:
1. Question button turns RED
2. Error message: "Lỗi khi lưu câu trả lời. Vui lòng thử lại."
3. Question added to errorQuestions array
4. Question removed from savingQuestions set
```

### ✅ Success After Error Test
```javascript
// Scenario: Error first, then success
1. User selects answer A → Network error → Button RED
2. User selects answer B → Success → Button GREEN
3. errorQuestions should be empty
4. saveQuestions should contain questionId
```

### 🔄 Multiple Errors Test
```javascript
// Scenario: Multiple consecutive errors
1. User types "1" → Network error
2. User types "12" → Server error  
3. User types "123" → Success
4. Only final state should be reflected in UI
```

## 🎯 Manual Testing Steps

### Step 1: Network Error Simulation
1. Open DevTools → Network tab
2. Set throttling to "Offline"
3. Answer a question
4. Verify error handling

### Step 2: Server Error Simulation
1. Modify API endpoint to return 500 error
2. Answer a question
3. Verify error message and UI state

### Step 3: Recovery Testing
1. Cause an error (network/server)
2. Fix the issue (restore network/server)
3. Change the answer
4. Verify successful save and UI update

### Step 4: Debounce + Error Testing
1. Type rapidly in TLN question
2. Disconnect network during typing
3. Verify only one error is shown
4. Reconnect and change answer
5. Verify successful save

## 📊 Expected UI States

### 🔵 Saving State
- Button: Blue color with spinner (optional)
- Counter: "Số câu đang lưu" increases
- User feedback: Visual indication of saving

### ✅ Success State  
- Button: Green color
- Counter: "Số câu đã làm" increases
- savingQuestions: Question removed
- saveQuestions: Question added

### ❌ Error State
- Button: Red color
- Error message: Displayed to user
- savingQuestions: Question removed
- errorQuestions: Question added
- Counter: "Số câu chưa lưu" increases

### 🔄 Retry State
- User changes answer after error
- Button: Blue (saving) → Green (success)
- errorQuestions: Question removed
- saveQuestions: Question added

## 🚨 Edge Cases to Test

### Case 1: Component Unmount During Save
```javascript
// Scenario: User navigates away while saving
1. Start typing in TLN question
2. Navigate to another page before debounce fires
3. Verify: No memory leaks, debounced calls cancelled
```

### Case 2: Multiple Questions Same Time
```javascript
// Scenario: Answer multiple questions rapidly
1. Answer TN question → starts saving
2. Answer DS question → starts saving  
3. Answer TLN question → starts saving
4. One fails, others succeed
5. Verify: Each question has correct state
```

### Case 3: Rapid Answer Changes
```javascript
// Scenario: Change answer multiple times rapidly
1. Select A → Select B → Select C (within debounce time)
2. Network error occurs
3. Verify: Only final answer (C) is attempted
4. Verify: Correct error handling for answer C
```

### Case 4: Mixed Success/Error
```javascript
// Scenario: Some questions save, others fail
1. Answer 5 questions simultaneously
2. 3 succeed, 2 fail (network issues)
3. Verify: Correct state for each question
4. Verify: Counters are accurate
```

## 🔧 Debugging Tips

### Console Logs to Check
```javascript
// Success logs
"Đã lưu câu trả lời TN thành công"
"Đã lưu câu trả lời DS thành công"  
"Đã lưu câu trả lời TLN thành công"

// Error logs
"Network error khi lưu câu trả lời TN: [error]"
"Lỗi khi lưu câu trả lời DS: [error]"
```

### State to Monitor
```javascript
// Redux DevTools
- doExam.saveQuestions (array)
- doExam.errorQuestions (array)
- doExam.loadingSubmitAnswer (boolean)

// Component State  
- savingQuestions (Set)
- markedQuestions (Set)
```

### Network Tab Verification
- Check API calls are debounced correctly
- Verify retry attempts (if implemented)
- Monitor response status codes
- Check request payloads

## ✅ Success Criteria

### Functional Requirements
- ✅ Debounce works for all question types
- ✅ Error handling works for all error types
- ✅ UI state reflects actual save status
- ✅ User gets appropriate feedback
- ✅ Recovery works after errors

### Performance Requirements  
- ✅ No memory leaks on unmount
- ✅ Minimal API calls (debounced)
- ✅ Smooth UI interactions
- ✅ Fast error recovery

### UX Requirements
- ✅ Clear error messages
- ✅ Visual state indicators
- ✅ Intuitive retry mechanism
- ✅ No data loss on errors
