[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\Dashboard.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\RegisterPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\socket.js": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MenuSidebar.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\Choice.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "270"}, {"size": 837, "mtime": 1748800674146, "results": "271", "hashOfConfig": "272"}, {"size": 375, "mtime": 1744531393988, "results": "273", "hashOfConfig": "272"}, {"size": 9856, "mtime": 1750161060767, "results": "274", "hashOfConfig": "272"}, {"size": 2238, "mtime": 1749801515196, "results": "275", "hashOfConfig": "272"}, {"size": 843, "mtime": 1744531393977, "results": "276", "hashOfConfig": "272"}, {"size": 9018, "mtime": 1748220639296, "results": "277", "hashOfConfig": "272"}, {"size": 1183, "mtime": 1749697714870, "results": "278", "hashOfConfig": "272"}, {"size": 5016, "mtime": 1748330106066, "results": "279", "hashOfConfig": "272"}, {"size": 4828, "mtime": 1746378664900, "results": "280", "hashOfConfig": "272"}, {"size": 2012, "mtime": 1744531393979, "results": "281", "hashOfConfig": "272"}, {"size": 1271, "mtime": 1744531393978, "results": "282", "hashOfConfig": "272"}, {"size": 21195, "mtime": 1747223318312, "results": "283", "hashOfConfig": "272"}, {"size": 11683, "mtime": 1748250288653, "results": "284", "hashOfConfig": "272"}, {"size": 1979, "mtime": 1749722105422, "results": "285", "hashOfConfig": "272"}, {"size": 551, "mtime": 1744531393982, "results": "286", "hashOfConfig": "272"}, {"size": 275, "mtime": 1748215697376, "results": "287", "hashOfConfig": "272"}, {"size": 1739, "mtime": 1749721392840, "results": "288", "hashOfConfig": "272"}, {"size": 45583, "mtime": 1749698060672, "results": "289", "hashOfConfig": "272"}, {"size": 9142, "mtime": 1749697382803, "results": "290", "hashOfConfig": "272"}, {"size": 401, "mtime": 1744531393980, "results": "291", "hashOfConfig": "272"}, {"size": 21111, "mtime": 1748220666759, "results": "292", "hashOfConfig": "272"}, {"size": 5290, "mtime": 1749723419715, "results": "293", "hashOfConfig": "272"}, {"size": 2156, "mtime": 1749729430930, "results": "294", "hashOfConfig": "272"}, {"size": 4548, "mtime": 1749978521128, "results": "295", "hashOfConfig": "272"}, {"size": 348, "mtime": 1749202010110, "results": "296", "hashOfConfig": "272"}, {"size": 3119, "mtime": 1749720998016, "results": "297", "hashOfConfig": "272"}, {"size": 8773, "mtime": 1748278675061, "results": "298", "hashOfConfig": "272"}, {"size": 1080, "mtime": 1747460558584, "results": "299", "hashOfConfig": "272"}, {"size": 6445, "mtime": 1749721854651, "results": "300", "hashOfConfig": "272"}, {"size": 3345, "mtime": 1748250288648, "results": "301", "hashOfConfig": "272"}, {"size": 3099, "mtime": 1744531393973, "results": "302", "hashOfConfig": "272"}, {"size": 6929, "mtime": 1749723272968, "results": "303", "hashOfConfig": "272"}, {"size": 17415, "mtime": 1749731127191, "results": "304", "hashOfConfig": "272"}, {"size": 12809, "mtime": 1749801873182, "results": "305", "hashOfConfig": "272"}, {"size": 1380, "mtime": 1744531393975, "results": "306", "hashOfConfig": "272"}, {"size": 2643, "mtime": 1747353945002, "results": "307", "hashOfConfig": "272"}, {"size": 4309, "mtime": 1748709335425, "results": "308", "hashOfConfig": "272"}, {"size": 4640, "mtime": 1748220605989, "results": "309", "hashOfConfig": "272"}, {"size": 2480, "mtime": 1747721626218, "results": "310", "hashOfConfig": "272"}, {"size": 5150, "mtime": 1748398556383, "results": "311", "hashOfConfig": "272"}, {"size": 1337, "mtime": 1747720637516, "results": "312", "hashOfConfig": "272"}, {"size": 690, "mtime": 1744531393950, "results": "313", "hashOfConfig": "272"}, {"size": 1339, "mtime": 1747223318344, "results": "314", "hashOfConfig": "272"}, {"size": 673, "mtime": 1744531393976, "results": "315", "hashOfConfig": "272"}, {"size": 903, "mtime": 1744531393976, "results": "316", "hashOfConfig": "272"}, {"size": 2284, "mtime": 1744531393959, "results": "317", "hashOfConfig": "272"}, {"size": 1151, "mtime": 1749730250381, "results": "318", "hashOfConfig": "272"}, {"size": 3200, "mtime": 1744531393954, "results": "319", "hashOfConfig": "272"}, {"size": 3578, "mtime": 1747223318325, "results": "320", "hashOfConfig": "272"}, {"size": 635, "mtime": 1744531393961, "results": "321", "hashOfConfig": "272"}, {"size": 1227, "mtime": 1744531393952, "results": "322", "hashOfConfig": "272"}, {"size": 5587, "mtime": 1744531393969, "results": "323", "hashOfConfig": "272"}, {"size": 7571, "mtime": 1747902858134, "results": "324", "hashOfConfig": "272"}, {"size": 1218, "mtime": 1744531393963, "results": "325", "hashOfConfig": "272"}, {"size": 9586, "mtime": 1749722764769, "results": "326", "hashOfConfig": "272"}, {"size": 5657, "mtime": 1744531393962, "results": "327", "hashOfConfig": "272"}, {"size": 28691, "mtime": 1744958278477, "results": "328", "hashOfConfig": "272"}, {"size": 19936, "mtime": 1748984865157, "results": "329", "hashOfConfig": "272"}, {"size": 5631, "mtime": 1749721260990, "results": "330", "hashOfConfig": "272"}, {"size": 8139, "mtime": 1749721464118, "results": "331", "hashOfConfig": "272"}, {"size": 911, "mtime": 1744531393943, "results": "332", "hashOfConfig": "272"}, {"size": 3647, "mtime": 1747223318326, "results": "333", "hashOfConfig": "272"}, {"size": 30556, "mtime": 1749698031543, "results": "334", "hashOfConfig": "272"}, {"size": 9572, "mtime": 1749693815954, "results": "335", "hashOfConfig": "272"}, {"size": 448, "mtime": 1749815688858, "results": "336", "hashOfConfig": "272"}, {"size": 1574, "mtime": 1744531393960, "results": "337", "hashOfConfig": "272"}, {"size": 7401, "mtime": 1747223318342, "results": "338", "hashOfConfig": "272"}, {"size": 2914, "mtime": 1747223318342, "results": "339", "hashOfConfig": "272"}, {"size": 28398, "mtime": 1749425223637, "results": "340", "hashOfConfig": "272"}, {"size": 17496, "mtime": 1748709335423, "results": "341", "hashOfConfig": "272"}, {"size": 19585, "mtime": 1747902858113, "results": "342", "hashOfConfig": "272"}, {"size": 1734, "mtime": 1744531393948, "results": "343", "hashOfConfig": "272"}, {"size": 55645, "mtime": 1748984865157, "results": "344", "hashOfConfig": "272"}, {"size": 6448, "mtime": 1749729618233, "results": "345", "hashOfConfig": "272"}, {"size": 5637, "mtime": 1747254491214, "results": "346", "hashOfConfig": "272"}, {"size": 21475, "mtime": 1748984865155, "results": "347", "hashOfConfig": "272"}, {"size": 10256, "mtime": 1744531393939, "results": "348", "hashOfConfig": "272"}, {"size": 1071, "mtime": 1748876218633, "results": "349", "hashOfConfig": "272"}, {"size": 18529, "mtime": 1749202010110, "results": "350", "hashOfConfig": "272"}, {"size": 6162, "mtime": 1748250288622, "results": "351", "hashOfConfig": "272"}, {"size": 3022, "mtime": 1747719253353, "results": "352", "hashOfConfig": "272"}, {"size": 7915, "mtime": 1748392811401, "results": "353", "hashOfConfig": "272"}, {"size": 5023, "mtime": 1749697044412, "results": "354", "hashOfConfig": "272"}, {"size": 3490, "mtime": 1749748056906, "results": "355", "hashOfConfig": "272"}, {"size": 935, "mtime": 1745405710864, "results": "356", "hashOfConfig": "272"}, {"size": 949, "mtime": 1747223318316, "results": "357", "hashOfConfig": "272"}, {"size": 3267, "mtime": 1747354362354, "results": "358", "hashOfConfig": "272"}, {"size": 3004, "mtime": 1749747762024, "results": "359", "hashOfConfig": "272"}, {"size": 1302, "mtime": 1748326437434, "results": "360", "hashOfConfig": "272"}, {"size": 2380, "mtime": 1744531393947, "results": "361", "hashOfConfig": "272"}, {"size": 2201, "mtime": 1744531393951, "results": "362", "hashOfConfig": "272"}, {"size": 14316, "mtime": 1749895693266, "results": "363", "hashOfConfig": "272"}, {"size": 1990, "mtime": 1744531393948, "results": "364", "hashOfConfig": "272"}, {"size": 841, "mtime": 1748984865154, "results": "365", "hashOfConfig": "272"}, {"size": 5565, "mtime": 1745690690469, "results": "366", "hashOfConfig": "272"}, {"size": 2295, "mtime": 1747223318353, "results": "367", "hashOfConfig": "272"}, {"size": 3146, "mtime": 1744531393940, "results": "368", "hashOfConfig": "272"}, {"size": 4074, "mtime": 1747223318326, "results": "369", "hashOfConfig": "272"}, {"size": 2984, "mtime": 1747902858121, "results": "370", "hashOfConfig": "272"}, {"size": 10634, "mtime": 1745483150534, "results": "371", "hashOfConfig": "372"}, {"size": 3707, "mtime": 1749722678323, "results": "373", "hashOfConfig": "272"}, {"size": 6034, "mtime": 1748364026312, "results": "374", "hashOfConfig": "272"}, {"size": 4147, "mtime": 1749731674278, "results": "375", "hashOfConfig": "272"}, {"size": 829, "mtime": 1744531393990, "results": "376", "hashOfConfig": "272"}, {"size": 3423, "mtime": 1749801242912, "results": "377", "hashOfConfig": "272"}, {"size": 297, "mtime": 1744531393989, "results": "378", "hashOfConfig": "272"}, {"size": 313, "mtime": 1744531393940, "results": "379", "hashOfConfig": "272"}, {"size": 4754, "mtime": 1749730388262, "results": "380", "hashOfConfig": "272"}, {"size": 1652, "mtime": 1748250288663, "results": "381", "hashOfConfig": "272"}, {"size": 993, "mtime": 1747223318349, "results": "382", "hashOfConfig": "272"}, {"size": 475, "mtime": 1748984865156, "results": "383", "hashOfConfig": "272"}, {"size": 902, "mtime": 1747223318353, "results": "384", "hashOfConfig": "272"}, {"size": 3053, "mtime": 1744531393946, "results": "385", "hashOfConfig": "272"}, {"size": 47782, "mtime": 1748984865154, "results": "386", "hashOfConfig": "272"}, {"size": 3402, "mtime": 1748781512174, "results": "387", "hashOfConfig": "272"}, {"size": 4872, "mtime": 1747223318349, "results": "388", "hashOfConfig": "272"}, {"size": 1392, "mtime": 1744957327338, "results": "389", "hashOfConfig": "272"}, {"size": 2297, "mtime": 1744531393945, "results": "390", "hashOfConfig": "272"}, {"size": 2193, "mtime": 1747223318349, "results": "391", "hashOfConfig": "272"}, {"size": 1359, "mtime": 1747223318349, "results": "392", "hashOfConfig": "272"}, {"size": 826, "mtime": 1748398318309, "results": "393", "hashOfConfig": "272"}, {"size": 1769, "mtime": 1748709335429, "results": "394", "hashOfConfig": "272"}, {"size": 888, "mtime": 1744551763492, "results": "395", "hashOfConfig": "272"}, {"size": 4921, "mtime": 1747223318325, "results": "396", "hashOfConfig": "272"}, {"size": 24089, "mtime": 1750161401768, "results": "397", "hashOfConfig": "272"}, {"size": 411, "mtime": 1744531393940, "results": "398", "hashOfConfig": "272"}, {"size": 2290, "mtime": 1744531393963, "results": "399", "hashOfConfig": "272"}, {"size": 1219, "mtime": 1747467640276, "results": "400", "hashOfConfig": "272"}, {"size": 2003, "mtime": 1744531393970, "results": "401", "hashOfConfig": "272"}, {"size": 2166, "mtime": 1744531393969, "results": "402", "hashOfConfig": "272"}, {"size": 18019, "mtime": 1748984865167, "results": "403", "hashOfConfig": "272"}, {"size": 6813, "mtime": 1747223318342, "results": "404", "hashOfConfig": "272"}, {"size": 3094, "mtime": 1744531393970, "results": "405", "hashOfConfig": "272"}, {"size": 6087, "mtime": 1748876218633, "results": "406", "hashOfConfig": "272"}, {"size": 503, "mtime": 1744531393949, "results": "407", "hashOfConfig": "272"}, {"size": 641, "mtime": 1746285622761, "results": "408", "hashOfConfig": "272"}, {"size": 7876, "mtime": 1747223318342, "results": "409", "hashOfConfig": "272"}, {"size": 4922, "mtime": 1748329867180, "results": "410", "hashOfConfig": "272"}, {"size": 19209, "mtime": 1749815803869, "results": "411", "hashOfConfig": "272"}, {"size": 20555, "mtime": 1748250288625, "results": "412", "hashOfConfig": "272"}, {"size": 1337, "mtime": 1744531393967, "results": "413", "hashOfConfig": "372"}, {"size": 5412, "mtime": 1747223318349, "results": "414", "hashOfConfig": "272"}, {"size": 2938, "mtime": 1747223318353, "results": "415", "hashOfConfig": "272"}, {"size": 3182, "mtime": 1747223318353, "results": "416", "hashOfConfig": "272"}, {"size": 2928, "mtime": 1747223318349, "results": "417", "hashOfConfig": "272"}, {"size": 1885, "mtime": 1747354661883, "results": "418", "hashOfConfig": "272"}, {"size": 1345, "mtime": 1749697625937, "results": "419", "hashOfConfig": "272"}, {"size": 4099, "mtime": 1749731407409, "results": "420", "hashOfConfig": "272"}, {"size": 1106, "mtime": 1744531393967, "results": "421", "hashOfConfig": "272"}, {"size": 642, "mtime": 1744531393966, "results": "422", "hashOfConfig": "272"}, {"size": 3540, "mtime": 1744531393966, "results": "423", "hashOfConfig": "272"}, {"size": 6380, "mtime": 1747361017417, "results": "424", "hashOfConfig": "272"}, {"size": 2600, "mtime": 1748876218633, "results": "425", "hashOfConfig": "272"}, {"size": 11010, "mtime": 1748278675058, "results": "426", "hashOfConfig": "272"}, {"size": 3297, "mtime": 1744531393957, "results": "427", "hashOfConfig": "272"}, {"size": 20934, "mtime": 1748987770234, "results": "428", "hashOfConfig": "272"}, {"size": 13435, "mtime": 1749695159063, "results": "429", "hashOfConfig": "272"}, {"size": 1205, "mtime": 1748984865161, "results": "430", "hashOfConfig": "272"}, {"size": 12148, "mtime": 1748250288636, "results": "431", "hashOfConfig": "272"}, {"size": 7688, "mtime": 1747223318312, "results": "432", "hashOfConfig": "272"}, {"size": 8344, "mtime": 1745507778499, "results": "433", "hashOfConfig": "272"}, {"size": 8025, "mtime": 1745507836095, "results": "434", "hashOfConfig": "272"}, {"size": 6988, "mtime": 1747223318344, "results": "435", "hashOfConfig": "272"}, {"size": 7719, "mtime": 1745499803667, "results": "436", "hashOfConfig": "272"}, {"size": 8378, "mtime": 1747223318344, "results": "437", "hashOfConfig": "272"}, {"size": 7254, "mtime": 1747223318344, "results": "438", "hashOfConfig": "272"}, {"size": 1721, "mtime": 1749553291593, "results": "439", "hashOfConfig": "272"}, {"size": 11464, "mtime": 1745500164258, "results": "440", "hashOfConfig": "272"}, {"size": 4650, "mtime": 1745508333678, "results": "441", "hashOfConfig": "272"}, {"size": 13822, "mtime": 1748250288625, "results": "442", "hashOfConfig": "272"}, {"size": 8599, "mtime": 1747223318326, "results": "443", "hashOfConfig": "272"}, {"size": 9774, "mtime": 1747223318326, "results": "444", "hashOfConfig": "272"}, {"size": 7914, "mtime": 1747223318326, "results": "445", "hashOfConfig": "272"}, {"size": 10728, "mtime": 1749548057374, "results": "446", "hashOfConfig": "272"}, {"size": 18582, "mtime": 1749747601919, "results": "447", "hashOfConfig": "272"}, {"size": 8954, "mtime": 1749810779685, "results": "448", "hashOfConfig": "272"}, {"size": 3429, "mtime": 1745682027607, "results": "449", "hashOfConfig": "272"}, {"size": 2298, "mtime": 1749802834779, "results": "450", "hashOfConfig": "272"}, {"size": 823, "mtime": 1748779533260, "results": "451", "hashOfConfig": "272"}, {"size": 1380, "mtime": 1745682047729, "results": "452", "hashOfConfig": "272"}, {"size": 1145, "mtime": 1747902858133, "results": "453", "hashOfConfig": "272"}, {"size": 1118, "mtime": 1745682069045, "results": "454", "hashOfConfig": "272"}, {"size": 978, "mtime": 1747902858130, "results": "455", "hashOfConfig": "272"}, {"size": 1781, "mtime": 1745682059432, "results": "456", "hashOfConfig": "372"}, {"size": 10563, "mtime": 1748585335221, "results": "457", "hashOfConfig": "272"}, {"size": 3574, "mtime": 1746378664904, "results": "458", "hashOfConfig": "272"}, {"size": 4389, "mtime": 1749980511142, "results": "459", "hashOfConfig": "272"}, {"size": 4863, "mtime": 1749815418258, "results": "460", "hashOfConfig": "272"}, {"size": 6432, "mtime": 1749815434335, "results": "461", "hashOfConfig": "272"}, {"size": 1020, "mtime": 1745682406063, "results": "462", "hashOfConfig": "272"}, {"size": 2723, "mtime": 1749810234131, "results": "463", "hashOfConfig": "272"}, {"size": 6116, "mtime": 1746378664905, "results": "464", "hashOfConfig": "272"}, {"size": 1565, "mtime": 1747223318344, "results": "465", "hashOfConfig": "272"}, {"size": 1624, "mtime": 1745689590880, "results": "466", "hashOfConfig": "272"}, {"size": 42976, "mtime": 1749980501187, "results": "467", "hashOfConfig": "272"}, {"size": 70430, "mtime": 1748984865165, "results": "468", "hashOfConfig": "272"}, {"size": 9519, "mtime": 1747354195259, "results": "469", "hashOfConfig": "272"}, {"size": 7408, "mtime": 1749894301204, "results": "470", "hashOfConfig": "272"}, {"size": 4905, "mtime": 1747354192845, "results": "471", "hashOfConfig": "272"}, {"size": 59290, "mtime": 1749616133330, "results": "472", "hashOfConfig": "272"}, {"size": 26487, "mtime": 1748278828957, "results": "473", "hashOfConfig": "272"}, {"size": 24147, "mtime": 1747354834297, "results": "474", "hashOfConfig": "272"}, {"size": 23053, "mtime": 1749730024729, "results": "475", "hashOfConfig": "272"}, {"size": 37417, "mtime": 1748984865162, "results": "476", "hashOfConfig": "272"}, {"size": 16976, "mtime": 1749732457976, "results": "477", "hashOfConfig": "272"}, {"size": 8060, "mtime": 1747223318310, "results": "478", "hashOfConfig": "272"}, {"size": 16767, "mtime": 1748876218633, "results": "479", "hashOfConfig": "272"}, {"size": 3160, "mtime": 1745731138150, "results": "480", "hashOfConfig": "272"}, {"size": 7136, "mtime": 1747223318325, "results": "481", "hashOfConfig": "272"}, {"size": 20185, "mtime": 1747223318312, "results": "482", "hashOfConfig": "272"}, {"size": 2129, "mtime": 1746378664905, "results": "483", "hashOfConfig": "272"}, {"size": 955, "mtime": 1746378664898, "results": "484", "hashOfConfig": "272"}, {"size": 1184, "mtime": 1746378664905, "results": "485", "hashOfConfig": "272"}, {"size": 13378, "mtime": 1748984865159, "results": "486", "hashOfConfig": "272"}, {"size": 1099, "mtime": 1748326442261, "results": "487", "hashOfConfig": "272"}, {"size": 15897, "mtime": 1749894770618, "results": "488", "hashOfConfig": "272"}, {"size": 11960, "mtime": 1749815846575, "results": "489", "hashOfConfig": "272"}, {"size": 12224, "mtime": 1748220515100, "results": "490", "hashOfConfig": "272"}, {"size": 10534, "mtime": 1748220627343, "results": "491", "hashOfConfig": "272"}, {"size": 4031, "mtime": 1747278525096, "results": "492", "hashOfConfig": "272"}, {"size": 2036, "mtime": 1747283717643, "results": "493", "hashOfConfig": "272"}, {"size": 72414, "mtime": 1748330146983, "results": "494", "hashOfConfig": "272"}, {"size": 3589, "mtime": 1747355350828, "results": "495", "hashOfConfig": "272"}, {"size": 7509, "mtime": 1747353152130, "results": "496", "hashOfConfig": "272"}, {"size": 6153, "mtime": 1747354063004, "results": "497", "hashOfConfig": "272"}, {"size": 16791, "mtime": 1748473697636, "results": "498", "hashOfConfig": "272"}, {"size": 10572, "mtime": 1750164366135, "results": "499", "hashOfConfig": "272"}, {"size": 13464, "mtime": 1747902858145, "results": "500", "hashOfConfig": "272"}, {"size": 53659, "mtime": 1750164425101, "results": "501", "hashOfConfig": "272"}, {"size": 5431, "mtime": 1750161854535, "results": "502", "hashOfConfig": "272"}, {"size": 13940, "mtime": 1750161172966, "results": "503", "hashOfConfig": "272"}, {"size": 6596, "mtime": 1747524539464, "results": "504", "hashOfConfig": "272"}, {"size": 4152, "mtime": 1749202010110, "results": "505", "hashOfConfig": "272"}, {"size": 4762, "mtime": 1748513292659, "results": "506", "hashOfConfig": "272"}, {"size": 2443, "mtime": 1747719362467, "results": "507", "hashOfConfig": "272"}, {"size": 15359, "mtime": 1749549797204, "results": "508", "hashOfConfig": "272"}, {"size": 1863, "mtime": 1748356706231, "results": "509", "hashOfConfig": "272"}, {"size": 50801, "mtime": 1749552838208, "results": "510", "hashOfConfig": "272"}, {"size": 16122, "mtime": 1748221650546, "results": "511", "hashOfConfig": "272"}, {"size": 8222, "mtime": 1748250288630, "results": "512", "hashOfConfig": "272"}, {"size": 11210, "mtime": 1748223444732, "results": "513", "hashOfConfig": "272"}, {"size": 41966, "mtime": 1748250288654, "results": "514", "hashOfConfig": "272"}, {"size": 7612, "mtime": 1748250288649, "results": "515", "hashOfConfig": "272"}, {"size": 28083, "mtime": 1748250288657, "results": "516", "hashOfConfig": "272"}, {"size": 29543, "mtime": 1748984865164, "results": "517", "hashOfConfig": "272"}, {"size": 36682, "mtime": 1748250768337, "results": "518", "hashOfConfig": "272"}, {"size": 1840, "mtime": 1748250288662, "results": "519", "hashOfConfig": "272"}, {"size": 9569, "mtime": 1749694485776, "results": "520", "hashOfConfig": "272"}, {"size": 13102, "mtime": 1748250288647, "results": "521", "hashOfConfig": "272"}, {"size": 16077, "mtime": 1748365756504, "results": "522", "hashOfConfig": "272"}, {"size": 3987, "mtime": 1748709335425, "results": "523", "hashOfConfig": "272"}, {"size": 3539, "mtime": 1748800991826, "results": "524", "hashOfConfig": "272"}, {"size": 1712, "mtime": 1748800656400, "results": "525", "hashOfConfig": "272"}, {"size": 1983, "mtime": 1749871596913, "results": "526", "hashOfConfig": "272"}, {"size": 3908, "mtime": 1748801325319, "results": "527", "hashOfConfig": "272"}, {"size": 839, "mtime": 1748801505979, "results": "528", "hashOfConfig": "272"}, {"size": 365, "mtime": 1748984865153, "results": "529", "hashOfConfig": "272"}, {"size": 1199, "mtime": 1748984865156, "results": "530", "hashOfConfig": "272"}, {"size": 8860, "mtime": 1749202010110, "results": "531", "hashOfConfig": "272"}, {"size": 2130, "mtime": 1749521895529, "results": "532", "hashOfConfig": "272"}, {"size": 7780, "mtime": 1749202010110, "results": "533", "hashOfConfig": "272"}, {"size": 3832, "mtime": 1749202010110, "results": "534", "hashOfConfig": "272"}, {"size": 243, "mtime": 1749521895533, "results": "535", "hashOfConfig": "272"}, {"size": 2836, "mtime": 1749722547776, "results": "536", "hashOfConfig": "272"}, {"size": 734, "mtime": 1749721859047, "results": "537", "hashOfConfig": "272"}, {"size": 388, "mtime": 1749720429395, "results": "538", "hashOfConfig": "272"}, {"size": 579, "mtime": 1749731593347, "results": "539", "hashOfConfig": "272"}, {"size": 1530, "mtime": 1749732424739, "results": "540", "hashOfConfig": "272"}, {"size": 10261, "mtime": 1749813601878, "results": "541", "hashOfConfig": "272"}, {"size": 1664, "mtime": 1749808930419, "results": "542", "hashOfConfig": "272"}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1353", "1354"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\RegisterPage.jsx", ["1355"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1356", "1357"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1358", "1359"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1360"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1361"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1362", "1363", "1364", "1365", "1366"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1367", "1368"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1369", "1370"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1371"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1372", "1373", "1374", "1375", "1376", "1377"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1378", "1379", "1380", "1381", "1382"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1383", "1384", "1385", "1386"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1387", "1388"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1389", "1390", "1391", "1392"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1393"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1394"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1395"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1396"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1397", "1398", "1399", "1400", "1401", "1402", "1403"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1404"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1405", "1406", "1407"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1408"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1409"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1410", "1411", "1412"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1413", "1414", "1415"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1416", "1417"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1418", "1419", "1420", "1421", "1422", "1423"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\socket.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1432", "1433"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1434", "1435", "1436", "1437", "1438"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1439", "1440", "1441", "1442"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx", ["1443", "1444", "1445"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1446"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1447", "1448", "1449", "1450", "1451"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1452", "1453", "1454", "1455", "1456", "1457"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1458", "1459"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1460"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1461", "1462", "1463", "1464", "1465", "1466", "1467"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1468", "1469", "1470", "1471", "1472"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1473", "1474", "1475"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1476", "1477"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1478"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1489"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1490"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1505"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1506"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1507"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1508"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1509"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1510", "1511", "1512", "1513"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1514"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MenuSidebar.jsx", ["1515"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\Choice.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1516"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1517"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1518", "1519", "1520", "1521"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1522", "1523"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1524", "1525", "1526"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1527", "1528", "1529", "1530"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1531"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1532", "1533"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1534"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1535", "1536"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1537"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1538"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1539", "1540", "1541", "1542", "1543"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1544", "1545"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1546", "1547", "1548"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1573", "1574", "1575", "1576"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1577"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1578", "1579", "1580", "1581"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1582", "1583", "1584", "1585", "1586", "1587", "1588"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1599", "1600"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1601", "1602", "1603"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1604", "1605", "1606", "1607", "1608", "1609", "1610"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1611", "1612", "1613", "1614", "1615", "1616", "1617"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1618", "1619"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1620"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1621", "1622", "1623"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", ["1624", "1625", "1626", "1627", "1628"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1629"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1630", "1631", "1632"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1633", "1634", "1635", "1636", "1637", "1638", "1639"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1640"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1641", "1642"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["1675", "1676", "1677", "1678", "1679", "1680"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["1681"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["1682"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["1683"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["1684", "1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["1686", "1687", "1688", "1689", "1690", "1691", "1692"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["1693", "1694", "1695", "1696", "1697"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["1698", "1699", "1700"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["1701", "1702"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["1703", "1704"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["1705", "1706", "1707"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["1708"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], {"ruleId": "1709", "severity": 1, "message": "1710", "line": 4, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 17}, {"ruleId": "1709", "severity": 1, "message": "1713", "line": 50, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 50, "endColumn": 27}, {"ruleId": "1709", "severity": 1, "message": "1714", "line": 1, "column": 20, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1715", "line": 4, "column": 23, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 27}, {"ruleId": "1709", "severity": 1, "message": "1716", "line": 8, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 25}, {"ruleId": "1709", "severity": 1, "message": "1717", "line": 16, "column": 41, "nodeType": "1711", "messageId": "1712", "endLine": 16, "endColumn": 51}, {"ruleId": "1709", "severity": 1, "message": "1718", "line": 17, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 17, "endColumn": 36}, {"ruleId": "1719", "severity": 1, "message": "1720", "line": 26, "column": 8, "nodeType": "1721", "endLine": 26, "endColumn": 19, "suggestions": "1722"}, {"ruleId": "1723", "severity": 1, "message": "1724", "line": 393, "column": 45, "nodeType": "1725", "endLine": 398, "endColumn": 47}, {"ruleId": "1709", "severity": 1, "message": "1726", "line": 6, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 11}, {"ruleId": "1709", "severity": 1, "message": "1727", "line": 6, "column": 27, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 31}, {"ruleId": "1709", "severity": 1, "message": "1728", "line": 7, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 25}, {"ruleId": "1709", "severity": 1, "message": "1729", "line": 8, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 32}, {"ruleId": "1709", "severity": 1, "message": "1730", "line": 88, "column": 23, "nodeType": "1711", "messageId": "1712", "endLine": 88, "endColumn": 31}, {"ruleId": "1709", "severity": 1, "message": "1731", "line": 14, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1718", "line": 15, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 36}, {"ruleId": "1709", "severity": 1, "message": "1732", "line": 2, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1733", "line": 3, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 3, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1734", "line": 5, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 39}, {"ruleId": "1709", "severity": 1, "message": "1735", "line": 15, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1736", "line": 16, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 16, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1737", "line": 22, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 22, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1738", "line": 28, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 28, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1739", "line": 71, "column": 31, "nodeType": "1711", "endLine": 71, "endColumn": 42}, {"ruleId": "1719", "severity": 1, "message": "1740", "line": 266, "column": 8, "nodeType": "1721", "endLine": 266, "endColumn": 18, "suggestions": "1741"}, {"ruleId": "1709", "severity": 1, "message": "1735", "line": 6, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1736", "line": 9, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 9, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1737", "line": 18, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1738", "line": 20, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 20, "endColumn": 25}, {"ruleId": "1709", "severity": 1, "message": "1742", "line": 26, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 26}, {"ruleId": "1709", "severity": 1, "message": "1743", "line": 11, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 11, "endColumn": 30}, {"ruleId": "1709", "severity": 1, "message": "1744", "line": 26, "column": 21, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 26}, {"ruleId": "1709", "severity": 1, "message": "1717", "line": 26, "column": 41, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 51}, {"ruleId": "1719", "severity": 1, "message": "1745", "line": 201, "column": 8, "nodeType": "1721", "endLine": 201, "endColumn": 19, "suggestions": "1746"}, {"ruleId": "1709", "severity": 1, "message": "1734", "line": 6, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 39}, {"ruleId": "1709", "severity": 1, "message": "1718", "line": 14, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 36}, {"ruleId": "1709", "severity": 1, "message": "1732", "line": 2, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1747", "line": 6, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1748", "line": 7, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1749", "line": 8, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1750", "line": 1, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1751", "line": 37, "column": 19, "nodeType": "1711", "messageId": "1712", "endLine": 37, "endColumn": 27}, {"ruleId": "1752", "severity": 1, "message": "1753", "line": 140, "column": 80, "nodeType": "1754", "messageId": "1755", "endLine": 140, "endColumn": 82}, {"ruleId": "1719", "severity": 1, "message": "1756", "line": 18, "column": 8, "nodeType": "1721", "endLine": 18, "endColumn": 10, "suggestions": "1757"}, {"ruleId": "1709", "severity": 1, "message": "1714", "line": 1, "column": 20, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1758", "line": 4, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 23}, {"ruleId": "1709", "severity": 1, "message": "1759", "line": 11, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 11, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1760", "line": 11, "column": 53, "nodeType": "1711", "messageId": "1712", "endLine": 11, "endColumn": 62}, {"ruleId": "1709", "severity": 1, "message": "1761", "line": 12, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 12, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1762", "line": 12, "column": 16, "nodeType": "1711", "messageId": "1712", "endLine": 12, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1763", "line": 27, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 27, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1764", "line": 5, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1765", "line": 11, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 11, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 18, "column": 36, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 46}, {"ruleId": "1719", "severity": 1, "message": "1767", "line": 43, "column": 8, "nodeType": "1721", "endLine": 43, "endColumn": 26, "suggestions": "1768"}, {"ruleId": "1709", "severity": 1, "message": "1714", "line": 2, "column": 20, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1769", "line": 7, "column": 47, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 68}, {"ruleId": "1709", "severity": 1, "message": "1770", "line": 17, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 17, "endColumn": 16}, {"ruleId": "1709", "severity": 1, "message": "1771", "line": 29, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 29, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1772", "line": 36, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 36, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1765", "line": 7, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 15, "column": 36, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 46}, {"ruleId": "1709", "severity": 1, "message": "1773", "line": 19, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 19, "endColumn": 37}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 15, "column": 36, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 46}, {"ruleId": "1719", "severity": 1, "message": "1774", "line": 34, "column": 8, "nodeType": "1721", "endLine": 34, "endColumn": 53, "suggestions": "1775"}, {"ruleId": "1709", "severity": 1, "message": "1776", "line": 12, "column": 55, "nodeType": "1711", "messageId": "1712", "endLine": 12, "endColumn": 61}, {"ruleId": "1709", "severity": 1, "message": "1777", "line": 12, "column": 77, "nodeType": "1711", "messageId": "1712", "endLine": 12, "endColumn": 82}, {"ruleId": "1709", "severity": 1, "message": "1778", "line": 12, "column": 84, "nodeType": "1711", "messageId": "1712", "endLine": 12, "endColumn": 94}, {"ruleId": "1709", "severity": 1, "message": "1737", "line": 17, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 17, "endColumn": 19}, {"ruleId": "1719", "severity": 1, "message": "1739", "line": 38, "column": 31, "nodeType": "1711", "endLine": 38, "endColumn": 42}, {"ruleId": "1709", "severity": 1, "message": "1779", "line": 93, "column": 17, "nodeType": "1711", "messageId": "1712", "endLine": 93, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1733", "line": 2, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1734", "line": 7, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 39}, {"ruleId": "1709", "severity": 1, "message": "1780", "line": 24, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 24, "endColumn": 26}, {"ruleId": "1709", "severity": 1, "message": "1781", "line": 25, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 25, "endColumn": 30}, {"ruleId": "1709", "severity": 1, "message": "1782", "line": 47, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 47, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1783", "line": 68, "column": 8, "nodeType": "1721", "endLine": 68, "endColumn": 30, "suggestions": "1784"}, {"ruleId": "1709", "severity": 1, "message": "1785", "line": 78, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 78, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1786", "line": 86, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 86, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1787", "line": 8, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 28}, {"ruleId": "1719", "severity": 1, "message": "1788", "line": 45, "column": 8, "nodeType": "1721", "endLine": 45, "endColumn": 32, "suggestions": "1789"}, {"ruleId": "1709", "severity": 1, "message": "1790", "line": 6, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1791", "line": 7, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1735", "line": 13, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1736", "line": 14, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1738", "line": 23, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 23, "endColumn": 25}, {"ruleId": "1709", "severity": 1, "message": "1792", "line": 5, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1791", "line": 6, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1787", "line": 7, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 28}, {"ruleId": "1719", "severity": 1, "message": "1793", "line": 51, "column": 8, "nodeType": "1721", "endLine": 51, "endColumn": 28, "suggestions": "1794"}, {"ruleId": "1709", "severity": 1, "message": "1795", "line": 1, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1796", "line": 10, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 10, "endColumn": 17}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 286, "column": 59, "nodeType": "1799", "messageId": "1800", "endLine": 286, "endColumn": 60, "suggestions": "1801"}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 11, "column": 41, "nodeType": "1711", "messageId": "1712", "endLine": 11, "endColumn": 51}, {"ruleId": "1709", "severity": 1, "message": "1802", "line": 1, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 28}, {"ruleId": "1709", "severity": 1, "message": "1803", "line": 2, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1714", "line": 3, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 3, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1804", "line": 7, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1805", "line": 12, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 12, "endColumn": 31}, {"ruleId": "1709", "severity": 1, "message": "1806", "line": 2, "column": 37, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 45}, {"ruleId": "1709", "severity": 1, "message": "1807", "line": 2, "column": 47, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 60}, {"ruleId": "1709", "severity": 1, "message": "1808", "line": 2, "column": 62, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 67}, {"ruleId": "1709", "severity": 1, "message": "1809", "line": 2, "column": 69, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 77}, {"ruleId": "1709", "severity": 1, "message": "1810", "line": 3, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 3, "endColumn": 26}, {"ruleId": "1719", "severity": 1, "message": "1811", "line": 36, "column": 8, "nodeType": "1721", "endLine": 36, "endColumn": 43, "suggestions": "1812"}, {"ruleId": "1709", "severity": 1, "message": "1795", "line": 1, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1813", "line": 7, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1814", "line": 25, "column": 8, "nodeType": "1721", "endLine": 25, "endColumn": 20, "suggestions": "1815"}, {"ruleId": "1709", "severity": 1, "message": "1816", "line": 1, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1817", "line": 32, "column": 83, "nodeType": "1711", "messageId": "1712", "endLine": 32, "endColumn": 91}, {"ruleId": "1709", "severity": 1, "message": "1818", "line": 121, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 121, "endColumn": 23}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 159, "column": 22, "nodeType": "1821", "messageId": "1822", "endLine": 159, "endColumn": 24}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 159, "column": 91, "nodeType": "1821", "messageId": "1822", "endLine": 159, "endColumn": 93}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 304, "column": 40, "nodeType": "1821", "messageId": "1822", "endLine": 304, "endColumn": 42}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 304, "column": 109, "nodeType": "1821", "messageId": "1822", "endLine": 304, "endColumn": 111}, {"ruleId": "1709", "severity": 1, "message": "1823", "line": 14, "column": 38, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 47}, {"ruleId": "1709", "severity": 1, "message": "1824", "line": 14, "column": 49, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 56}, {"ruleId": "1709", "severity": 1, "message": "1825", "line": 14, "column": 58, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 70}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 34, "column": 40, "nodeType": "1821", "messageId": "1822", "endLine": 34, "endColumn": 42}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 34, "column": 109, "nodeType": "1821", "messageId": "1822", "endLine": 34, "endColumn": 111}, {"ruleId": "1709", "severity": 1, "message": "1744", "line": 15, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1826", "line": 15, "column": 44, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 52}, {"ruleId": "1719", "severity": 1, "message": "1827", "line": 42, "column": 8, "nodeType": "1721", "endLine": 42, "endColumn": 40, "suggestions": "1828"}, {"ruleId": "1709", "severity": 1, "message": "1813", "line": 1, "column": 25, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 33}, {"ruleId": "1709", "severity": 1, "message": "1829", "line": 4, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1830", "line": 4, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1831", "line": 1, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1715", "line": 2, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1832", "line": 2, "column": 16, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 27}, {"ruleId": "1709", "severity": 1, "message": "1833", "line": 4, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 28}, {"ruleId": "1709", "severity": 1, "message": "1834", "line": 4, "column": 46, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 67}, {"ruleId": "1709", "severity": 1, "message": "1835", "line": 13, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 26}, {"ruleId": "1709", "severity": 1, "message": "1836", "line": 47, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 47, "endColumn": 16}, {"ruleId": "1709", "severity": 1, "message": "1837", "line": 54, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 54, "endColumn": 16}, {"ruleId": "1709", "severity": 1, "message": "1838", "line": 61, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 61, "endColumn": 25}, {"ruleId": "1709", "severity": 1, "message": "1839", "line": 77, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 77, "endColumn": 23}, {"ruleId": "1719", "severity": 1, "message": "1840", "line": 29, "column": 8, "nodeType": "1721", "endLine": 29, "endColumn": 10, "suggestions": "1841"}, {"ruleId": "1723", "severity": 1, "message": "1724", "line": 44, "column": 29, "nodeType": "1725", "endLine": 44, "endColumn": 98}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 229, "column": 30, "nodeType": "1799", "messageId": "1800", "endLine": 229, "endColumn": 31, "suggestions": "1842"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 242, "column": 61, "nodeType": "1799", "messageId": "1800", "endLine": 242, "endColumn": 62, "suggestions": "1843"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 248, "column": 29, "nodeType": "1799", "messageId": "1800", "endLine": 248, "endColumn": 30, "suggestions": "1844"}, {"ruleId": "1797", "severity": 1, "message": "1845", "line": 248, "column": 31, "nodeType": "1799", "messageId": "1800", "endLine": 248, "endColumn": 32, "suggestions": "1846"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 249, "column": 51, "nodeType": "1799", "messageId": "1800", "endLine": 249, "endColumn": 52, "suggestions": "1847"}, {"ruleId": "1797", "severity": 1, "message": "1845", "line": 249, "column": 53, "nodeType": "1799", "messageId": "1800", "endLine": 249, "endColumn": 54, "suggestions": "1848"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 330, "column": 30, "nodeType": "1799", "messageId": "1800", "endLine": 330, "endColumn": 31, "suggestions": "1849"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 352, "column": 61, "nodeType": "1799", "messageId": "1800", "endLine": 352, "endColumn": 62, "suggestions": "1850"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 358, "column": 29, "nodeType": "1799", "messageId": "1800", "endLine": 358, "endColumn": 30, "suggestions": "1851"}, {"ruleId": "1797", "severity": 1, "message": "1845", "line": 358, "column": 31, "nodeType": "1799", "messageId": "1800", "endLine": 358, "endColumn": 32, "suggestions": "1852"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 359, "column": 51, "nodeType": "1799", "messageId": "1800", "endLine": 359, "endColumn": 52, "suggestions": "1853"}, {"ruleId": "1797", "severity": 1, "message": "1845", "line": 359, "column": 53, "nodeType": "1799", "messageId": "1800", "endLine": 359, "endColumn": 54, "suggestions": "1854"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 433, "column": 30, "nodeType": "1799", "messageId": "1800", "endLine": 433, "endColumn": 31, "suggestions": "1855"}, {"ruleId": "1797", "severity": 1, "message": "1798", "line": 452, "column": 61, "nodeType": "1799", "messageId": "1800", "endLine": 452, "endColumn": 62, "suggestions": "1856"}, {"ruleId": "1719", "severity": 1, "message": "1857", "line": 80, "column": 8, "nodeType": "1721", "endLine": 80, "endColumn": 10, "suggestions": "1858"}, {"ruleId": "1723", "severity": 1, "message": "1724", "line": 56, "column": 37, "nodeType": "1725", "endLine": 56, "endColumn": 107}, {"ruleId": "1709", "severity": 1, "message": "1859", "line": 3, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 3, "endColumn": 27}, {"ruleId": "1719", "severity": 1, "message": "1860", "line": 73, "column": 8, "nodeType": "1721", "endLine": 73, "endColumn": 25, "suggestions": "1861"}, {"ruleId": "1709", "severity": 1, "message": "1714", "line": 3, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 3, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1862", "line": 5, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1863", "line": 13, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 16}, {"ruleId": "1709", "severity": 1, "message": "1864", "line": 14, "column": 28, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 45}, {"ruleId": "1709", "severity": 1, "message": "1865", "line": 34, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 34, "endColumn": 20}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 116, "column": 56, "nodeType": "1754", "messageId": "1755", "endLine": 116, "endColumn": 58}, {"ruleId": "1709", "severity": 1, "message": "1715", "line": 1, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1867", "line": 7, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 21}, {"ruleId": "1868", "severity": 1, "message": "1869", "line": 12, "column": 25, "nodeType": "1725", "endLine": 12, "endColumn": 614}, {"ruleId": "1709", "severity": 1, "message": "1795", "line": 1, "column": 46, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 49}, {"ruleId": "1709", "severity": 1, "message": "1870", "line": 3, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 3, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1871", "line": 14, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1872", "line": 14, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 37}, {"ruleId": "1719", "severity": 1, "message": "1873", "line": 21, "column": 8, "nodeType": "1721", "endLine": 21, "endColumn": 26, "suggestions": "1874"}, {"ruleId": "1719", "severity": 1, "message": "1875", "line": 40, "column": 8, "nodeType": "1721", "endLine": 40, "endColumn": 10, "suggestions": "1876"}, {"ruleId": "1709", "severity": 1, "message": "1726", "line": 2, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 11}, {"ruleId": "1709", "severity": 1, "message": "1733", "line": 3, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 3, "endColumn": 21}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 140, "column": 56, "nodeType": "1754", "messageId": "1755", "endLine": 140, "endColumn": 58}, {"ruleId": "1709", "severity": 1, "message": "1862", "line": 4, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1877", "line": 5, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 16}, {"ruleId": "1709", "severity": 1, "message": "1878", "line": 100, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 100, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1879", "line": 111, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 111, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1759", "line": 15, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1749", "line": 7, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 36}, {"ruleId": "1709", "severity": 1, "message": "1759", "line": 13, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1759", "line": 14, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1749", "line": 7, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 36}, {"ruleId": "1709", "severity": 1, "message": "1759", "line": 13, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1749", "line": 7, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 36}, {"ruleId": "1880", "severity": 1, "message": "1881", "line": 28, "column": 74, "nodeType": "1754", "messageId": "1882", "endLine": 28, "endColumn": 75}, {"ruleId": "1709", "severity": 1, "message": "1883", "line": 6, "column": 25, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1884", "line": 6, "column": 31, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 37}, {"ruleId": "1709", "severity": 1, "message": "1778", "line": 6, "column": 39, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 49}, {"ruleId": "1709", "severity": 1, "message": "1808", "line": 6, "column": 51, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 56}, {"ruleId": "1709", "severity": 1, "message": "1885", "line": 6, "column": 65, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 70}, {"ruleId": "1719", "severity": 1, "message": "1886", "line": 50, "column": 8, "nodeType": "1721", "endLine": 50, "endColumn": 28, "suggestions": "1887"}, {"ruleId": "1719", "severity": 1, "message": "1886", "line": 54, "column": 8, "nodeType": "1721", "endLine": 54, "endColumn": 21, "suggestions": "1888"}, {"ruleId": "1709", "severity": 1, "message": "1889", "line": 37, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 37, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1890", "line": 44, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 44, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1891", "line": 47, "column": 9, "nodeType": "1892", "endLine": 51, "endColumn": 4}, {"ruleId": "1709", "severity": 1, "message": "1893", "line": 16, "column": 45, "nodeType": "1711", "messageId": "1712", "endLine": 16, "endColumn": 57}, {"ruleId": "1719", "severity": 1, "message": "1894", "line": 54, "column": 8, "nodeType": "1721", "endLine": 54, "endColumn": 14, "suggestions": "1895"}, {"ruleId": "1709", "severity": 1, "message": "1896", "line": 106, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 106, "endColumn": 22}, {"ruleId": "1719", "severity": 1, "message": "1897", "line": 221, "column": 8, "nodeType": "1721", "endLine": 221, "endColumn": 23, "suggestions": "1898"}, {"ruleId": "1709", "severity": 1, "message": "1899", "line": 272, "column": 19, "nodeType": "1711", "messageId": "1712", "endLine": 272, "endColumn": 25}, {"ruleId": "1719", "severity": 1, "message": "1739", "line": 360, "column": 37, "nodeType": "1711", "endLine": 360, "endColumn": 48}, {"ruleId": "1719", "severity": 1, "message": "1739", "line": 387, "column": 37, "nodeType": "1711", "endLine": 387, "endColumn": 48}, {"ruleId": "1719", "severity": 1, "message": "1900", "line": 730, "column": 8, "nodeType": "1721", "endLine": 730, "endColumn": 99, "suggestions": "1901"}, {"ruleId": "1719", "severity": 1, "message": "1902", "line": 838, "column": 8, "nodeType": "1721", "endLine": 838, "endColumn": 36, "suggestions": "1903"}, {"ruleId": "1709", "severity": 1, "message": "1904", "line": 2, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1905", "line": 4, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1906", "line": 18, "column": 25, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1907", "line": 18, "column": 137, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 146}, {"ruleId": "1709", "severity": 1, "message": "1908", "line": 18, "column": 148, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 161}, {"ruleId": "1709", "severity": 1, "message": "1909", "line": 18, "column": 163, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 173}, {"ruleId": "1709", "severity": 1, "message": "1910", "line": 18, "column": 175, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 182}, {"ruleId": "1709", "severity": 1, "message": "1911", "line": 18, "column": 184, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 189}, {"ruleId": "1709", "severity": 1, "message": "1912", "line": 18, "column": 191, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 205}, {"ruleId": "1709", "severity": 1, "message": "1913", "line": 34, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 34, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1914", "line": 39, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 39, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1915", "line": 40, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 40, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1916", "line": 64, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 64, "endColumn": 17}, {"ruleId": "1709", "severity": 1, "message": "1917", "line": 65, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 65, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1918", "line": 67, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 67, "endColumn": 17}, {"ruleId": "1709", "severity": 1, "message": "1795", "line": 4, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1919", "line": 6, "column": 35, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 46}, {"ruleId": "1709", "severity": 1, "message": "1920", "line": 10, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 10, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 26, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 21}, {"ruleId": "1719", "severity": 1, "message": "1894", "line": 37, "column": 8, "nodeType": "1721", "endLine": 37, "endColumn": 14, "suggestions": "1921"}, {"ruleId": "1709", "severity": 1, "message": "1829", "line": 15, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1922", "line": 18, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 18, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1726", "line": 21, "column": 98, "nodeType": "1711", "messageId": "1712", "endLine": 21, "endColumn": 99}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 262, "column": 38, "nodeType": "1754", "messageId": "1755", "endLine": 262, "endColumn": 40}, {"ruleId": "1709", "severity": 1, "message": "1736", "line": 7, "column": 111, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 115}, {"ruleId": "1709", "severity": 1, "message": "1923", "line": 7, "column": 117, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 127}, {"ruleId": "1709", "severity": 1, "message": "1816", "line": 10, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 10, "endColumn": 24}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 178, "column": 123, "nodeType": "1821", "messageId": "1822", "endLine": 178, "endColumn": 125}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 178, "column": 197, "nodeType": "1821", "messageId": "1822", "endLine": 178, "endColumn": 199}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 187, "column": 120, "nodeType": "1821", "messageId": "1822", "endLine": 187, "endColumn": 122}, {"ruleId": "1819", "severity": 1, "message": "1820", "line": 187, "column": 189, "nodeType": "1821", "messageId": "1822", "endLine": 187, "endColumn": 191}, {"ruleId": "1709", "severity": 1, "message": "1806", "line": 15, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1776", "line": 17, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 17, "endColumn": 11}, {"ruleId": "1709", "severity": 1, "message": "1736", "line": 20, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 20, "endColumn": 9}, {"ruleId": "1709", "severity": 1, "message": "1924", "line": 21, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 21, "endColumn": 17}, {"ruleId": "1709", "severity": 1, "message": "1777", "line": 24, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 24, "endColumn": 10}, {"ruleId": "1709", "severity": 1, "message": "1829", "line": 28, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 28, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1925", "line": 30, "column": 7, "nodeType": "1711", "messageId": "1712", "endLine": 30, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1926", "line": 159, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 159, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1927", "line": 160, "column": 21, "nodeType": "1711", "messageId": "1712", "endLine": 160, "endColumn": 31}, {"ruleId": "1719", "severity": 1, "message": "1928", "line": 166, "column": 8, "nodeType": "1721", "endLine": 166, "endColumn": 18, "suggestions": "1929"}, {"ruleId": "1709", "severity": 1, "message": "1930", "line": 26, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 18}, {"ruleId": "1719", "severity": 1, "message": "1931", "line": 199, "column": 8, "nodeType": "1721", "endLine": 199, "endColumn": 21, "suggestions": "1932"}, {"ruleId": "1709", "severity": 1, "message": "1933", "line": 34, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 34, "endColumn": 20}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 49, "column": 37, "nodeType": "1754", "messageId": "1755", "endLine": 49, "endColumn": 39}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 51, "column": 44, "nodeType": "1754", "messageId": "1755", "endLine": 51, "endColumn": 46}, {"ruleId": "1709", "severity": 1, "message": "1934", "line": 8, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 16}, {"ruleId": "1709", "severity": 1, "message": "1726", "line": 8, "column": 18, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1809", "line": 8, "column": 21, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1935", "line": 15, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 15, "endColumn": 17}, {"ruleId": "1709", "severity": 1, "message": "1936", "line": 16, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 16, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1937", "line": 26, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 37}, {"ruleId": "1709", "severity": 1, "message": "1938", "line": 111, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 111, "endColumn": 23}, {"ruleId": "1709", "severity": 1, "message": "1829", "line": 7, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1736", "line": 13, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 9}, {"ruleId": "1709", "severity": 1, "message": "1806", "line": 17, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 17, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1939", "line": 19, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 19, "endColumn": 13}, {"ruleId": "1719", "severity": 1, "message": "1940", "line": 129, "column": 8, "nodeType": "1721", "endLine": 129, "endColumn": 18, "suggestions": "1941"}, {"ruleId": "1709", "severity": 1, "message": "1942", "line": 155, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 155, "endColumn": 32}, {"ruleId": "1709", "severity": 1, "message": "1943", "line": 232, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 232, "endColumn": 29}, {"ruleId": "1719", "severity": 1, "message": "1944", "line": 39, "column": 8, "nodeType": "1721", "endLine": 39, "endColumn": 30, "suggestions": "1945"}, {"ruleId": "1723", "severity": 1, "message": "1724", "line": 226, "column": 69, "nodeType": "1725", "endLine": 230, "endColumn": 71}, {"ruleId": "1752", "severity": 1, "message": "1753", "line": 49, "column": 108, "nodeType": "1754", "messageId": "1755", "endLine": 49, "endColumn": 110}, {"ruleId": "1709", "severity": 1, "message": "1744", "line": 55, "column": 42, "nodeType": "1711", "messageId": "1712", "endLine": 55, "endColumn": 47}, {"ruleId": "1719", "severity": 1, "message": "1886", "line": 88, "column": 8, "nodeType": "1721", "endLine": 88, "endColumn": 28, "suggestions": "1946"}, {"ruleId": "1719", "severity": 1, "message": "1886", "line": 92, "column": 8, "nodeType": "1721", "endLine": 92, "endColumn": 21, "suggestions": "1947"}, {"ruleId": "1709", "severity": 1, "message": "1863", "line": 5, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 16}, {"ruleId": "1709", "severity": 1, "message": "1948", "line": 7, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 7, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1949", "line": 8, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 12}, {"ruleId": "1709", "severity": 1, "message": "1950", "line": 13, "column": 3, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1864", "line": 14, "column": 3, "nodeType": "1711", "messageId": "1712", "endLine": 14, "endColumn": 20}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 285, "column": 65, "nodeType": "1754", "messageId": "1755", "endLine": 285, "endColumn": 67}, {"ruleId": "1709", "severity": 1, "message": "1951", "line": 28, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 28, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1778", "line": 29, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 29, "endColumn": 15}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 406, "column": 101, "nodeType": "1754", "messageId": "1755", "endLine": 406, "endColumn": 103}, {"ruleId": "1709", "severity": 1, "message": "1952", "line": 55, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 55, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1953", "line": 55, "column": 32, "nodeType": "1711", "messageId": "1712", "endLine": 55, "endColumn": 42}, {"ruleId": "1709", "severity": 1, "message": "1954", "line": 59, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 59, "endColumn": 27}, {"ruleId": "1709", "severity": 1, "message": "1955", "line": 70, "column": 12, "nodeType": "1711", "messageId": "1712", "endLine": 70, "endColumn": 23}, {"ruleId": "1709", "severity": 1, "message": "1956", "line": 82, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 82, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1957", "line": 88, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 88, "endColumn": 30}, {"ruleId": "1709", "severity": 1, "message": "1942", "line": 94, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 94, "endColumn": 32}, {"ruleId": "1752", "severity": 1, "message": "1753", "line": 296, "column": 35, "nodeType": "1754", "messageId": "1755", "endLine": 296, "endColumn": 37}, {"ruleId": "1709", "severity": 1, "message": "1776", "line": 19, "column": 3, "nodeType": "1711", "messageId": "1712", "endLine": 19, "endColumn": 9}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 31, "column": 24, "nodeType": "1711", "messageId": "1712", "endLine": 31, "endColumn": 34}, {"ruleId": "1709", "severity": 1, "message": "1958", "line": 4, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1959", "line": 5, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 14}, {"ruleId": "1709", "severity": 1, "message": "1960", "line": 13, "column": 3, "nodeType": "1711", "messageId": "1712", "endLine": 13, "endColumn": 25}, {"ruleId": "1709", "severity": 1, "message": "1951", "line": 24, "column": 77, "nodeType": "1711", "messageId": "1712", "endLine": 24, "endColumn": 86}, {"ruleId": "1709", "severity": 1, "message": "1961", "line": 24, "column": 88, "nodeType": "1711", "messageId": "1712", "endLine": 24, "endColumn": 92}, {"ruleId": "1709", "severity": 1, "message": "1776", "line": 24, "column": 94, "nodeType": "1711", "messageId": "1712", "endLine": 24, "endColumn": 100}, {"ruleId": "1709", "severity": 1, "message": "1732", "line": 26, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1962", "line": 27, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 27, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1737", "line": 32, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 32, "endColumn": 17}, {"ruleId": "1709", "severity": 1, "message": "1963", "line": 33, "column": 28, "nodeType": "1711", "messageId": "1712", "endLine": 33, "endColumn": 45}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 35, "column": 17, "nodeType": "1711", "messageId": "1712", "endLine": 35, "endColumn": 27}, {"ruleId": "1709", "severity": 1, "message": "1964", "line": 79, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 79, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1965", "line": 80, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 80, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1966", "line": 86, "column": 20, "nodeType": "1711", "messageId": "1712", "endLine": 86, "endColumn": 31}, {"ruleId": "1709", "severity": 1, "message": "1967", "line": 87, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 87, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1968", "line": 87, "column": 22, "nodeType": "1711", "messageId": "1712", "endLine": 87, "endColumn": 35}, {"ruleId": "1709", "severity": 1, "message": "1969", "line": 88, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 88, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1970", "line": 88, "column": 20, "nodeType": "1711", "messageId": "1712", "endLine": 88, "endColumn": 31}, {"ruleId": "1709", "severity": 1, "message": "1971", "line": 91, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 91, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1972", "line": 92, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 92, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1973", "line": 93, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 93, "endColumn": 29}, {"ruleId": "1709", "severity": 1, "message": "1974", "line": 94, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 94, "endColumn": 27}, {"ruleId": "1709", "severity": 1, "message": "1975", "line": 123, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 123, "endColumn": 21}, {"ruleId": "1709", "severity": 1, "message": "1976", "line": 124, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 124, "endColumn": 27}, {"ruleId": "1709", "severity": 1, "message": "1977", "line": 126, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 126, "endColumn": 23}, {"ruleId": "1709", "severity": 1, "message": "1978", "line": 126, "column": 25, "nodeType": "1711", "messageId": "1712", "endLine": 126, "endColumn": 41}, {"ruleId": "1709", "severity": 1, "message": "1979", "line": 154, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 154, "endColumn": 25}, {"ruleId": "1709", "severity": 1, "message": "1980", "line": 160, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 160, "endColumn": 33}, {"ruleId": "1719", "severity": 1, "message": "1981", "line": 271, "column": 6, "nodeType": "1721", "endLine": 271, "endColumn": 32, "suggestions": "1982"}, {"ruleId": "1709", "severity": 1, "message": "1983", "line": 349, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 349, "endColumn": 23}, {"ruleId": "1709", "severity": 1, "message": "1984", "line": 567, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 567, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1985", "line": 579, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 579, "endColumn": 18}, {"ruleId": "1709", "severity": 1, "message": "1986", "line": 5, "column": 3, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 30}, {"ruleId": "1709", "severity": 1, "message": "1987", "line": 6, "column": 3, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1777", "line": 19, "column": 3, "nodeType": "1711", "messageId": "1712", "endLine": 19, "endColumn": 8}, {"ruleId": "1709", "severity": 1, "message": "1761", "line": 26, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 26, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1878", "line": 27, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 27, "endColumn": 17}, {"ruleId": "1709", "severity": 1, "message": "1988", "line": 32, "column": 32, "nodeType": "1711", "messageId": "1712", "endLine": 32, "endColumn": 55}, {"ruleId": "1709", "severity": 1, "message": "1989", "line": 2, "column": 26, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 32}, {"ruleId": "1719", "severity": 1, "message": "1739", "line": 35, "column": 29, "nodeType": "1711", "endLine": 35, "endColumn": 40}, {"ruleId": "1719", "severity": 1, "message": "1739", "line": 35, "column": 30, "nodeType": "1711", "endLine": 35, "endColumn": 41}, {"ruleId": "1752", "severity": 1, "message": "1866", "line": 330, "column": 68, "nodeType": "1754", "messageId": "1755", "endLine": 330, "endColumn": 70}, {"ruleId": "1752", "severity": 1, "message": "1753", "line": 343, "column": 64, "nodeType": "1754", "messageId": "1755", "endLine": 343, "endColumn": 66}, {"ruleId": "1709", "severity": 1, "message": "1795", "line": 1, "column": 10, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1990", "line": 12, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 12, "endColumn": 13}, {"ruleId": "1709", "severity": 1, "message": "1735", "line": 23, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 23, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1736", "line": 25, "column": 119, "nodeType": "1711", "messageId": "1712", "endLine": 25, "endColumn": 123}, {"ruleId": "1709", "severity": 1, "message": "1991", "line": 25, "column": 135, "nodeType": "1711", "messageId": "1712", "endLine": 25, "endColumn": 140}, {"ruleId": "1709", "severity": 1, "message": "1737", "line": 32, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 32, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1766", "line": 40, "column": 9, "nodeType": "1711", "messageId": "1712", "endLine": 40, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1750", "line": 4, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 4, "endColumn": 19}, {"ruleId": "1709", "severity": 1, "message": "1776", "line": 17, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 17, "endColumn": 11}, {"ruleId": "1709", "severity": 1, "message": "1727", "line": 20, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 20, "endColumn": 9}, {"ruleId": "1709", "severity": 1, "message": "1992", "line": 27, "column": 5, "nodeType": "1711", "messageId": "1712", "endLine": 27, "endColumn": 32}, {"ruleId": "1719", "severity": 1, "message": "1993", "line": 122, "column": 8, "nodeType": "1721", "endLine": 122, "endColumn": 10, "suggestions": "1994"}, {"ruleId": "1709", "severity": 1, "message": "1809", "line": 6, "column": 56, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 64}, {"ruleId": "1709", "severity": 1, "message": "1884", "line": 6, "column": 73, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 79}, {"ruleId": "1709", "severity": 1, "message": "1778", "line": 6, "column": 81, "nodeType": "1711", "messageId": "1712", "endLine": 6, "endColumn": 91}, {"ruleId": "1709", "severity": 1, "message": "1995", "line": 5, "column": 60, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 66}, {"ruleId": "1709", "severity": 1, "message": "1934", "line": 5, "column": 68, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 74}, {"ruleId": "1709", "severity": 1, "message": "1996", "line": 2, "column": 42, "nodeType": "1711", "messageId": "1712", "endLine": 2, "endColumn": 51}, {"ruleId": "1709", "severity": 1, "message": "1997", "line": 5, "column": 11, "nodeType": "1711", "messageId": "1712", "endLine": 5, "endColumn": 24}, {"ruleId": "1709", "severity": 1, "message": "1829", "line": 8, "column": 8, "nodeType": "1711", "messageId": "1712", "endLine": 8, "endColumn": 22}, {"ruleId": "1709", "severity": 1, "message": "1933", "line": 16, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 16, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1998", "line": 17, "column": 13, "nodeType": "1711", "messageId": "1712", "endLine": 17, "endColumn": 20}, {"ruleId": "1709", "severity": 1, "message": "1813", "line": 1, "column": 21, "nodeType": "1711", "messageId": "1712", "endLine": 1, "endColumn": 29}, "no-unused-vars", "'Dashboard' is defined but never used.", "Identifier", "unusedVar", "'AdminUserSearchPage' is defined but never used.", "'useEffect' is defined but never used.", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'totalItems' is assigned a value but never used.", "'isFilterVIew' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", "ArrayExpression", ["1999"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["2000"], "'showAddStudent' is assigned a value but never used.", "'ScoreDistributionChart' is defined but never used.", "'limit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'prevRanks'. Either include them or remove the dependency array.", ["2001"], "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'AdminLayout' is defined but never used.", "'errorMsg' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["2002"], "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2003"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["2004"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2005"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2006"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2007"], "'use' is defined but never used.", "'compose' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["2008", "2009"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2010"], "'useState' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2011"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2012"], "'LoadingSpinner' is defined but never used.", "'ChevronRight' is defined but never used.", "'BeeMathLogo' is defined but never used.", "'useLocation' is defined but never used.", "'toggleCloseSidebar' is defined but never used.", "'toggleTuitionDropdown' is defined but never used.", "'tuitionDropdown' is assigned a value but never used.", "'icon3' is assigned a value but never used.", "'icon4' is assigned a value but never used.", "'iconAttendance' is assigned a value but never used.", "'notification' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2013"], ["2014", "2015"], ["2016", "2017"], ["2018", "2019"], "Unnecessary escape character: \\).", ["2020", "2021"], ["2022", "2023"], ["2024", "2025"], ["2026", "2027"], ["2028", "2029"], ["2030", "2031"], ["2032", "2033"], ["2034", "2035"], ["2036", "2037"], ["2038", "2039"], ["2040", "2041"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2042"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2043"], "'InputSearch' is defined but never used.", "'socket' is defined but never used.", "'updateUnreadCount' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2044"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2045"], "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2046"], ["2047"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2048"], "'addQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2049"], "'result' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2050"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2051"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'setAttempts' is defined but never used.", "'ScoreBarChart' is defined but never used.", ["2052"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2053"], "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2054"], "'loading' is assigned a value but never used.", "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2055"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2056"], ["2057"], ["2058"], "'formatDistanceToNow' is defined but never used.", "'vi' is defined but never used.", "'addNotification' is defined but never used.", "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'html2canvas' is defined but never used.", "'find' is defined but never used.", "'fetchTuitionStatistics' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'tuitionStatistics' is assigned a value but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'startMonth' is assigned a value but never used.", "'setStartMonth' is assigned a value but never used.", "'endMonth' is assigned a value but never used.", "'setEndMonth' is assigned a value but never used.", "'monthlyChartRef' is assigned a value but never used.", "'classChartRef' is assigned a value but never used.", "'monthlyChartInstance' is assigned a value but never used.", "'classChartInstance' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'exportLoading' is assigned a value but never used.", "'setExportLoading' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterIsPaid', 'filterMonth', 'filterOverdue', and 'inputValue'. Either include them or remove the dependency array.", ["2059"], "'handleBatchAdd' is assigned a value but never used.", "'iconBatch' is assigned a value but never used.", "'iconUsers' is assigned a value but never used.", "'fetchUserTuitionPaymentById' is defined but never used.", "'clearTuitionPayment' is defined but never used.", "'setClassTuitionsLoading' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2060"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", {"desc": "2061", "fix": "2062"}, {"desc": "2063", "fix": "2064"}, {"desc": "2065", "fix": "2066"}, {"desc": "2067", "fix": "2068"}, {"desc": "2069", "fix": "2070"}, {"desc": "2071", "fix": "2072"}, {"desc": "2073", "fix": "2074"}, {"desc": "2075", "fix": "2076"}, {"desc": "2077", "fix": "2078"}, {"messageId": "2079", "fix": "2080", "desc": "2081"}, {"messageId": "2082", "fix": "2083", "desc": "2084"}, {"desc": "2085", "fix": "2086"}, {"desc": "2087", "fix": "2088"}, {"desc": "2089", "fix": "2090"}, {"desc": "2091", "fix": "2092"}, {"messageId": "2079", "fix": "2093", "desc": "2081"}, {"messageId": "2082", "fix": "2094", "desc": "2084"}, {"messageId": "2079", "fix": "2095", "desc": "2081"}, {"messageId": "2082", "fix": "2096", "desc": "2084"}, {"messageId": "2079", "fix": "2097", "desc": "2081"}, {"messageId": "2082", "fix": "2098", "desc": "2084"}, {"messageId": "2079", "fix": "2099", "desc": "2081"}, {"messageId": "2082", "fix": "2100", "desc": "2084"}, {"messageId": "2079", "fix": "2101", "desc": "2081"}, {"messageId": "2082", "fix": "2102", "desc": "2084"}, {"messageId": "2079", "fix": "2103", "desc": "2081"}, {"messageId": "2082", "fix": "2104", "desc": "2084"}, {"messageId": "2079", "fix": "2105", "desc": "2081"}, {"messageId": "2082", "fix": "2106", "desc": "2084"}, {"messageId": "2079", "fix": "2107", "desc": "2081"}, {"messageId": "2082", "fix": "2108", "desc": "2084"}, {"messageId": "2079", "fix": "2109", "desc": "2081"}, {"messageId": "2082", "fix": "2110", "desc": "2084"}, {"messageId": "2079", "fix": "2111", "desc": "2081"}, {"messageId": "2082", "fix": "2112", "desc": "2084"}, {"messageId": "2079", "fix": "2113", "desc": "2081"}, {"messageId": "2082", "fix": "2114", "desc": "2084"}, {"messageId": "2079", "fix": "2115", "desc": "2081"}, {"messageId": "2082", "fix": "2116", "desc": "2084"}, {"messageId": "2079", "fix": "2117", "desc": "2081"}, {"messageId": "2082", "fix": "2118", "desc": "2084"}, {"messageId": "2079", "fix": "2119", "desc": "2081"}, {"messageId": "2082", "fix": "2120", "desc": "2084"}, {"desc": "2121", "fix": "2122"}, {"desc": "2123", "fix": "2124"}, {"desc": "2125", "fix": "2126"}, {"desc": "2127", "fix": "2128"}, {"desc": "2129", "fix": "2130"}, {"desc": "2131", "fix": "2132"}, {"desc": "2133", "fix": "2134"}, {"desc": "2135", "fix": "2136"}, {"desc": "2137", "fix": "2138"}, {"desc": "2139", "fix": "2140"}, {"desc": "2133", "fix": "2141"}, {"desc": "2142", "fix": "2143"}, {"desc": "2144", "fix": "2145"}, {"desc": "2146", "fix": "2147"}, {"desc": "2148", "fix": "2149"}, {"desc": "2129", "fix": "2150"}, {"desc": "2131", "fix": "2151"}, {"desc": "2152", "fix": "2153"}, {"desc": "2154", "fix": "2155"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2156", "text": "2157"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2158", "text": "2159"}, "Update the dependencies array to be: [attempts1, dispatch, prevRanks]", {"range": "2160", "text": "2161"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2162", "text": "2163"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2164", "text": "2165"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2166", "text": "2167"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2168", "text": "2169"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2170", "text": "2171"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2172", "text": "2173"}, "removeEscape", {"range": "2174", "text": "2175"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2176", "text": "2177"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2178", "text": "2179"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2180", "text": "2181"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2182", "text": "2183"}, "Update the dependencies array to be: [maxLength]", {"range": "2184", "text": "2185"}, {"range": "2186", "text": "2175"}, {"range": "2187", "text": "2177"}, {"range": "2188", "text": "2175"}, {"range": "2189", "text": "2177"}, {"range": "2190", "text": "2175"}, {"range": "2191", "text": "2177"}, {"range": "2192", "text": "2175"}, {"range": "2193", "text": "2177"}, {"range": "2194", "text": "2175"}, {"range": "2195", "text": "2177"}, {"range": "2196", "text": "2175"}, {"range": "2197", "text": "2177"}, {"range": "2198", "text": "2175"}, {"range": "2199", "text": "2177"}, {"range": "2200", "text": "2175"}, {"range": "2201", "text": "2177"}, {"range": "2202", "text": "2175"}, {"range": "2203", "text": "2177"}, {"range": "2204", "text": "2175"}, {"range": "2205", "text": "2177"}, {"range": "2206", "text": "2175"}, {"range": "2207", "text": "2177"}, {"range": "2208", "text": "2175"}, {"range": "2209", "text": "2177"}, {"range": "2210", "text": "2175"}, {"range": "2211", "text": "2177"}, {"range": "2212", "text": "2175"}, {"range": "2213", "text": "2177"}, "Update the dependencies array to be: [handlePaste]", {"range": "2214", "text": "2215"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2216", "text": "2217"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2218", "text": "2219"}, "Update the dependencies array to be: [handleFile]", {"range": "2220", "text": "2221"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2222", "text": "2223"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2224", "text": "2225"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2226", "text": "2227"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2228", "text": "2229"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2230", "text": "2231"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2232", "text": "2233"}, {"range": "2234", "text": "2227"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2235", "text": "2236"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2237", "text": "2238"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2239", "text": "2240"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2241", "text": "2242"}, {"range": "2243", "text": "2223"}, {"range": "2244", "text": "2225"}, "Update the dependencies array to be: [dispatch, filterClass, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", {"range": "2245", "text": "2246"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2247", "text": "2248"}, [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9489, 9499], "[dispatch, fetchPdfFiles]", [7946, 7957], "[attempts1, dispatch, prevRanks]", [669, 671], "[options, selected, type]", [1909, 1927], "[dispatch, fetchQuestions, params]", [1518, 1563], "[dispatch, search, page, pageSize, sortOrder, classId]", [2625, 2647], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2128, 2148], "[codes, exam, exam?.class]", [10595, 10596], "", [10595, 10595], "\\", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [8598, 8599], [8598, 8598], [9063, 9064], [9063, 9063], [9259, 9260], [9259, 9259], [9261, 9262], [9261, 9261], [9331, 9332], [9331, 9331], [9333, 9334], [9333, 9333], [11606, 11607], [11606, 11606], [12526, 12527], [12526, 12526], [12693, 12694], [12693, 12693], [12695, 12696], [12695, 12695], [12765, 12766], [12765, 12765], [12767, 12768], [12767, 12767], [15061, 15062], [15061, 15061], [15755, 15756], [15755, 15755], [2448, 2450], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2444, 2450], "[exam, examId, navigate]", [8671, 8686], "[flag, remainingTime]", [29299, 29390], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [33497, 33525], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1591, 1613], "[currentPage, didInit, loadReports]", [3871, 3891], [3965, 3978], [9460, 9486], "[dispatch, filterClass, filterIsPaid, filterMonth, filterOverdue, inputValue, page, pageSize]", [3986, 3988], "[dispatch, userId]"]