[{"C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\Dashboard.jsx": "5", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\RegisterPage.jsx": "6", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx": "7", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx": "8", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx": "9", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx": "10", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx": "11", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx": "12", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx": "13", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx": "14", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx": "15", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx": "16", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx": "17", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx": "18", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx": "19", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx": "20", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx": "21", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx": "22", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx": "23", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx": "24", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx": "25", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx": "26", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js": "27", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js": "28", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js": "29", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js": "30", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js": "31", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js": "32", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js": "33", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js": "34", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js": "35", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js": "36", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js": "37", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js": "38", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js": "39", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js": "40", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js": "41", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx": "42", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx": "43", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx": "44", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx": "45", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx": "46", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx": "47", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx": "48", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx": "49", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx": "50", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx": "51", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx": "52", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx": "53", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx": "55", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx": "56", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx": "57", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx": "58", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx": "59", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx": "60", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx": "61", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx": "62", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx": "63", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx": "64", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\socket.js": "65", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx": "66", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx": "67", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx": "68", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx": "69", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx": "70", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx": "71", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx": "72", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx": "73", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx": "74", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx": "75", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx": "76", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx": "77", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx": "78", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx": "79", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx": "80", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js": "81", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx": "82", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx": "83", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx": "84", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js": "85", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx": "86", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx": "87", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js": "88", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx": "89", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx": "90", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx": "91", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx": "92", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx": "93", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx": "94", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx": "95", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx": "96", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx": "97", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx": "98", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx": "99", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx": "100", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js": "101", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx": "102", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx": "103", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js": "104", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js": "105", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js": "106", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx": "107", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js": "108", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js": "109", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx": "110", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx": "111", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx": "112", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx": "113", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx": "114", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx": "115", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx": "116", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx": "117", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx": "118", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx": "119", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx": "120", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js": "121", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js": "122", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js": "123", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx": "124", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx": "125", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx": "126", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx": "127", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx": "128", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx": "129", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx": "130", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js": "131", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx": "132", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx": "133", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx": "134", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx": "135", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js": "136", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx": "137", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx": "138", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx": "139", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx": "140", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx": "141", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx": "142", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx": "143", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx": "144", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx": "145", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx": "146", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js": "147", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js": "148", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MenuSidebar.jsx": "149", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\Choice.jsx": "150", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx": "151", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx": "152", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx": "153", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx": "154", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx": "155", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx": "156", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx": "157", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx": "158", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx": "159", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx": "160", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx": "161", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx": "162", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx": "163", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx": "164", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx": "165", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx": "166", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx": "167", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js": "168", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js": "169", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx": "170", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx": "171", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx": "172", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx": "173", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js": "174", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx": "175", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx": "176", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx": "177", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx": "178", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx": "179", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx": "180", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx": "181", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx": "182", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx": "183", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx": "184", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx": "185", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx": "186", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx": "187", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx": "188", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx": "189", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx": "190", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx": "191", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx": "192", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx": "193", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx": "194", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx": "195", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx": "196", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx": "197", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx": "198", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx": "199", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx": "200", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx": "201", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx": "202", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx": "203", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx": "204", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx": "205", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx": "206", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx": "207", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js": "208", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx": "209", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx": "210", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js": "211", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx": "212", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js": "213", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx": "214", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx": "215", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx": "216", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx": "217", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx": "218", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js": "219", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js": "220", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js": "221", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx": "222", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx": "223", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx": "224", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx": "225", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx": "226", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js": "227", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx": "228", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\ClassTuitionList.jsx": "229", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx": "230", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js": "231", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx": "232", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx": "233", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx": "234", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx": "235", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx": "236", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js": "237", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js": "238", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx": "239", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx": "240", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx": "241", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx": "242", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx": "243", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js": "244", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx": "245", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx": "246", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx": "247", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js": "248", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx": "249", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx": "250", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx": "251", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx": "252", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx": "253", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx": "254", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js": "255", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js": "256", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx": "257", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx": "258", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx": "259", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx": "260", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js": "261", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx": "262", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx": "263", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js": "264", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx": "265", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js": "266", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js": "267", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx": "268", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx": "269", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js": "270", "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js": "271"}, {"size": 837, "mtime": 1748800674146, "results": "272", "hashOfConfig": "273"}, {"size": 375, "mtime": 1744531393988, "results": "274", "hashOfConfig": "273"}, {"size": 10026, "mtime": 1749202010110, "results": "275", "hashOfConfig": "273"}, {"size": 2238, "mtime": 1749801515196, "results": "276", "hashOfConfig": "273"}, {"size": 843, "mtime": 1744531393977, "results": "277", "hashOfConfig": "273"}, {"size": 9018, "mtime": 1748220639296, "results": "278", "hashOfConfig": "273"}, {"size": 1183, "mtime": 1749697714870, "results": "279", "hashOfConfig": "273"}, {"size": 5016, "mtime": 1748330106066, "results": "280", "hashOfConfig": "273"}, {"size": 4828, "mtime": 1746378664900, "results": "281", "hashOfConfig": "273"}, {"size": 2012, "mtime": 1744531393979, "results": "282", "hashOfConfig": "273"}, {"size": 1271, "mtime": 1744531393978, "results": "283", "hashOfConfig": "273"}, {"size": 21195, "mtime": 1747223318312, "results": "284", "hashOfConfig": "273"}, {"size": 11683, "mtime": 1748250288653, "results": "285", "hashOfConfig": "273"}, {"size": 1979, "mtime": 1749722105422, "results": "286", "hashOfConfig": "273"}, {"size": 551, "mtime": 1744531393982, "results": "287", "hashOfConfig": "273"}, {"size": 275, "mtime": 1748215697376, "results": "288", "hashOfConfig": "273"}, {"size": 1739, "mtime": 1749721392840, "results": "289", "hashOfConfig": "273"}, {"size": 45583, "mtime": 1749698060672, "results": "290", "hashOfConfig": "273"}, {"size": 9142, "mtime": 1749697382803, "results": "291", "hashOfConfig": "273"}, {"size": 401, "mtime": 1744531393980, "results": "292", "hashOfConfig": "273"}, {"size": 21111, "mtime": 1748220666759, "results": "293", "hashOfConfig": "273"}, {"size": 5290, "mtime": 1749723419715, "results": "294", "hashOfConfig": "273"}, {"size": 2156, "mtime": 1749729430930, "results": "295", "hashOfConfig": "273"}, {"size": 4545, "mtime": 1746378664905, "results": "296", "hashOfConfig": "273"}, {"size": 348, "mtime": 1749202010110, "results": "297", "hashOfConfig": "273"}, {"size": 3119, "mtime": 1749720998016, "results": "298", "hashOfConfig": "273"}, {"size": 8773, "mtime": 1748278675061, "results": "299", "hashOfConfig": "273"}, {"size": 1080, "mtime": 1747460558584, "results": "300", "hashOfConfig": "273"}, {"size": 6445, "mtime": 1749721854651, "results": "301", "hashOfConfig": "273"}, {"size": 3345, "mtime": 1748250288648, "results": "302", "hashOfConfig": "273"}, {"size": 3099, "mtime": 1744531393973, "results": "303", "hashOfConfig": "273"}, {"size": 6929, "mtime": 1749723272968, "results": "304", "hashOfConfig": "273"}, {"size": 17415, "mtime": 1749731127191, "results": "305", "hashOfConfig": "273"}, {"size": 12809, "mtime": 1749801873182, "results": "306", "hashOfConfig": "273"}, {"size": 1380, "mtime": 1744531393975, "results": "307", "hashOfConfig": "273"}, {"size": 2643, "mtime": 1747353945002, "results": "308", "hashOfConfig": "273"}, {"size": 4309, "mtime": 1748709335425, "results": "309", "hashOfConfig": "273"}, {"size": 4640, "mtime": 1748220605989, "results": "310", "hashOfConfig": "273"}, {"size": 2480, "mtime": 1747721626218, "results": "311", "hashOfConfig": "273"}, {"size": 5150, "mtime": 1748398556383, "results": "312", "hashOfConfig": "273"}, {"size": 1337, "mtime": 1747720637516, "results": "313", "hashOfConfig": "273"}, {"size": 690, "mtime": 1744531393950, "results": "314", "hashOfConfig": "273"}, {"size": 1339, "mtime": 1747223318344, "results": "315", "hashOfConfig": "273"}, {"size": 673, "mtime": 1744531393976, "results": "316", "hashOfConfig": "273"}, {"size": 903, "mtime": 1744531393976, "results": "317", "hashOfConfig": "273"}, {"size": 2284, "mtime": 1744531393959, "results": "318", "hashOfConfig": "273"}, {"size": 1151, "mtime": 1749730250381, "results": "319", "hashOfConfig": "273"}, {"size": 3200, "mtime": 1744531393954, "results": "320", "hashOfConfig": "273"}, {"size": 3578, "mtime": 1747223318325, "results": "321", "hashOfConfig": "273"}, {"size": 635, "mtime": 1744531393961, "results": "322", "hashOfConfig": "273"}, {"size": 1227, "mtime": 1744531393952, "results": "323", "hashOfConfig": "273"}, {"size": 5587, "mtime": 1744531393969, "results": "324", "hashOfConfig": "273"}, {"size": 7571, "mtime": 1747902858134, "results": "325", "hashOfConfig": "273"}, {"size": 1218, "mtime": 1744531393963, "results": "326", "hashOfConfig": "273"}, {"size": 9586, "mtime": 1749722764769, "results": "327", "hashOfConfig": "273"}, {"size": 5657, "mtime": 1744531393962, "results": "328", "hashOfConfig": "273"}, {"size": 28691, "mtime": 1744958278477, "results": "329", "hashOfConfig": "273"}, {"size": 19936, "mtime": 1748984865157, "results": "330", "hashOfConfig": "273"}, {"size": 5631, "mtime": 1749721260990, "results": "331", "hashOfConfig": "273"}, {"size": 8139, "mtime": 1749721464118, "results": "332", "hashOfConfig": "273"}, {"size": 911, "mtime": 1744531393943, "results": "333", "hashOfConfig": "273"}, {"size": 3647, "mtime": 1747223318326, "results": "334", "hashOfConfig": "273"}, {"size": 30556, "mtime": 1749698031543, "results": "335", "hashOfConfig": "273"}, {"size": 9572, "mtime": 1749693815954, "results": "336", "hashOfConfig": "273"}, {"size": 448, "mtime": 1749798978200, "results": "337", "hashOfConfig": "273"}, {"size": 1574, "mtime": 1744531393960, "results": "338", "hashOfConfig": "273"}, {"size": 7401, "mtime": 1747223318342, "results": "339", "hashOfConfig": "273"}, {"size": 2914, "mtime": 1747223318342, "results": "340", "hashOfConfig": "273"}, {"size": 28398, "mtime": 1749425223637, "results": "341", "hashOfConfig": "273"}, {"size": 17496, "mtime": 1748709335423, "results": "342", "hashOfConfig": "273"}, {"size": 19585, "mtime": 1747902858113, "results": "343", "hashOfConfig": "273"}, {"size": 1734, "mtime": 1744531393948, "results": "344", "hashOfConfig": "273"}, {"size": 55645, "mtime": 1748984865157, "results": "345", "hashOfConfig": "273"}, {"size": 6448, "mtime": 1749729618233, "results": "346", "hashOfConfig": "273"}, {"size": 5637, "mtime": 1747254491214, "results": "347", "hashOfConfig": "273"}, {"size": 21475, "mtime": 1748984865155, "results": "348", "hashOfConfig": "273"}, {"size": 10256, "mtime": 1744531393939, "results": "349", "hashOfConfig": "273"}, {"size": 1071, "mtime": 1748876218633, "results": "350", "hashOfConfig": "273"}, {"size": 18529, "mtime": 1749202010110, "results": "351", "hashOfConfig": "273"}, {"size": 6162, "mtime": 1748250288622, "results": "352", "hashOfConfig": "273"}, {"size": 3022, "mtime": 1747719253353, "results": "353", "hashOfConfig": "273"}, {"size": 7915, "mtime": 1748392811401, "results": "354", "hashOfConfig": "273"}, {"size": 5023, "mtime": 1749697044412, "results": "355", "hashOfConfig": "273"}, {"size": 3490, "mtime": 1749748056906, "results": "356", "hashOfConfig": "273"}, {"size": 935, "mtime": 1745405710864, "results": "357", "hashOfConfig": "273"}, {"size": 949, "mtime": 1747223318316, "results": "358", "hashOfConfig": "273"}, {"size": 3267, "mtime": 1747354362354, "results": "359", "hashOfConfig": "273"}, {"size": 3004, "mtime": 1749747762024, "results": "360", "hashOfConfig": "273"}, {"size": 1302, "mtime": 1748326437434, "results": "361", "hashOfConfig": "273"}, {"size": 2380, "mtime": 1744531393947, "results": "362", "hashOfConfig": "273"}, {"size": 2201, "mtime": 1744531393951, "results": "363", "hashOfConfig": "273"}, {"size": 13275, "mtime": 1747226142248, "results": "364", "hashOfConfig": "273"}, {"size": 1990, "mtime": 1744531393948, "results": "365", "hashOfConfig": "273"}, {"size": 841, "mtime": 1748984865154, "results": "366", "hashOfConfig": "273"}, {"size": 5565, "mtime": 1745690690469, "results": "367", "hashOfConfig": "273"}, {"size": 2295, "mtime": 1747223318353, "results": "368", "hashOfConfig": "273"}, {"size": 3146, "mtime": 1744531393940, "results": "369", "hashOfConfig": "273"}, {"size": 4074, "mtime": 1747223318326, "results": "370", "hashOfConfig": "273"}, {"size": 2984, "mtime": 1747902858121, "results": "371", "hashOfConfig": "273"}, {"size": 10634, "mtime": 1745483150534, "results": "372", "hashOfConfig": "373"}, {"size": 3707, "mtime": 1749722678323, "results": "374", "hashOfConfig": "273"}, {"size": 6034, "mtime": 1748364026312, "results": "375", "hashOfConfig": "273"}, {"size": 4147, "mtime": 1749731674278, "results": "376", "hashOfConfig": "273"}, {"size": 829, "mtime": 1744531393990, "results": "377", "hashOfConfig": "273"}, {"size": 3423, "mtime": 1749801242912, "results": "378", "hashOfConfig": "273"}, {"size": 297, "mtime": 1744531393989, "results": "379", "hashOfConfig": "273"}, {"size": 313, "mtime": 1744531393940, "results": "380", "hashOfConfig": "273"}, {"size": 4754, "mtime": 1749730388262, "results": "381", "hashOfConfig": "273"}, {"size": 1652, "mtime": 1748250288663, "results": "382", "hashOfConfig": "273"}, {"size": 993, "mtime": 1747223318349, "results": "383", "hashOfConfig": "273"}, {"size": 475, "mtime": 1748984865156, "results": "384", "hashOfConfig": "273"}, {"size": 902, "mtime": 1747223318353, "results": "385", "hashOfConfig": "273"}, {"size": 3053, "mtime": 1744531393946, "results": "386", "hashOfConfig": "273"}, {"size": 47782, "mtime": 1748984865154, "results": "387", "hashOfConfig": "273"}, {"size": 3402, "mtime": 1748781512174, "results": "388", "hashOfConfig": "273"}, {"size": 4872, "mtime": 1747223318349, "results": "389", "hashOfConfig": "273"}, {"size": 1392, "mtime": 1744957327338, "results": "390", "hashOfConfig": "273"}, {"size": 2297, "mtime": 1744531393945, "results": "391", "hashOfConfig": "273"}, {"size": 2193, "mtime": 1747223318349, "results": "392", "hashOfConfig": "273"}, {"size": 1359, "mtime": 1747223318349, "results": "393", "hashOfConfig": "273"}, {"size": 826, "mtime": 1748398318309, "results": "394", "hashOfConfig": "273"}, {"size": 1769, "mtime": 1748709335429, "results": "395", "hashOfConfig": "273"}, {"size": 888, "mtime": 1744551763492, "results": "396", "hashOfConfig": "273"}, {"size": 4921, "mtime": 1747223318325, "results": "397", "hashOfConfig": "273"}, {"size": 26654, "mtime": 1748709335424, "results": "398", "hashOfConfig": "273"}, {"size": 411, "mtime": 1744531393940, "results": "399", "hashOfConfig": "273"}, {"size": 2290, "mtime": 1744531393963, "results": "400", "hashOfConfig": "273"}, {"size": 1219, "mtime": 1747467640276, "results": "401", "hashOfConfig": "273"}, {"size": 2003, "mtime": 1744531393970, "results": "402", "hashOfConfig": "273"}, {"size": 2166, "mtime": 1744531393969, "results": "403", "hashOfConfig": "273"}, {"size": 18019, "mtime": 1748984865167, "results": "404", "hashOfConfig": "273"}, {"size": 6813, "mtime": 1747223318342, "results": "405", "hashOfConfig": "273"}, {"size": 3094, "mtime": 1744531393970, "results": "406", "hashOfConfig": "273"}, {"size": 6087, "mtime": 1748876218633, "results": "407", "hashOfConfig": "273"}, {"size": 503, "mtime": 1744531393949, "results": "408", "hashOfConfig": "273"}, {"size": 641, "mtime": 1746285622761, "results": "409", "hashOfConfig": "273"}, {"size": 7876, "mtime": 1747223318342, "results": "410", "hashOfConfig": "273"}, {"size": 4922, "mtime": 1748329867180, "results": "411", "hashOfConfig": "273"}, {"size": 20787, "mtime": 1749202010110, "results": "412", "hashOfConfig": "273"}, {"size": 20555, "mtime": 1748250288625, "results": "413", "hashOfConfig": "273"}, {"size": 1337, "mtime": 1744531393967, "results": "414", "hashOfConfig": "373"}, {"size": 5412, "mtime": 1747223318349, "results": "415", "hashOfConfig": "273"}, {"size": 2938, "mtime": 1747223318353, "results": "416", "hashOfConfig": "273"}, {"size": 3182, "mtime": 1747223318353, "results": "417", "hashOfConfig": "273"}, {"size": 2928, "mtime": 1747223318349, "results": "418", "hashOfConfig": "273"}, {"size": 1885, "mtime": 1747354661883, "results": "419", "hashOfConfig": "273"}, {"size": 1345, "mtime": 1749697625937, "results": "420", "hashOfConfig": "273"}, {"size": 4099, "mtime": 1749731407409, "results": "421", "hashOfConfig": "273"}, {"size": 1106, "mtime": 1744531393967, "results": "422", "hashOfConfig": "273"}, {"size": 642, "mtime": 1744531393966, "results": "423", "hashOfConfig": "273"}, {"size": 3540, "mtime": 1744531393966, "results": "424", "hashOfConfig": "273"}, {"size": 6380, "mtime": 1747361017417, "results": "425", "hashOfConfig": "273"}, {"size": 2600, "mtime": 1748876218633, "results": "426", "hashOfConfig": "273"}, {"size": 11010, "mtime": 1748278675058, "results": "427", "hashOfConfig": "273"}, {"size": 3297, "mtime": 1744531393957, "results": "428", "hashOfConfig": "273"}, {"size": 20934, "mtime": 1748987770234, "results": "429", "hashOfConfig": "273"}, {"size": 13435, "mtime": 1749695159063, "results": "430", "hashOfConfig": "273"}, {"size": 1205, "mtime": 1748984865161, "results": "431", "hashOfConfig": "273"}, {"size": 12148, "mtime": 1748250288636, "results": "432", "hashOfConfig": "273"}, {"size": 7688, "mtime": 1747223318312, "results": "433", "hashOfConfig": "273"}, {"size": 8344, "mtime": 1745507778499, "results": "434", "hashOfConfig": "273"}, {"size": 8025, "mtime": 1745507836095, "results": "435", "hashOfConfig": "273"}, {"size": 6988, "mtime": 1747223318344, "results": "436", "hashOfConfig": "273"}, {"size": 7719, "mtime": 1745499803667, "results": "437", "hashOfConfig": "273"}, {"size": 8378, "mtime": 1747223318344, "results": "438", "hashOfConfig": "273"}, {"size": 7254, "mtime": 1747223318344, "results": "439", "hashOfConfig": "273"}, {"size": 1721, "mtime": 1749553291593, "results": "440", "hashOfConfig": "273"}, {"size": 11464, "mtime": 1745500164258, "results": "441", "hashOfConfig": "273"}, {"size": 4650, "mtime": 1745508333678, "results": "442", "hashOfConfig": "273"}, {"size": 13822, "mtime": 1748250288625, "results": "443", "hashOfConfig": "273"}, {"size": 8599, "mtime": 1747223318326, "results": "444", "hashOfConfig": "273"}, {"size": 9774, "mtime": 1747223318326, "results": "445", "hashOfConfig": "273"}, {"size": 7914, "mtime": 1747223318326, "results": "446", "hashOfConfig": "273"}, {"size": 10728, "mtime": 1749548057374, "results": "447", "hashOfConfig": "273"}, {"size": 18582, "mtime": 1749747601919, "results": "448", "hashOfConfig": "273"}, {"size": 9180, "mtime": 1749810717922, "results": "449", "hashOfConfig": "273"}, {"size": 3429, "mtime": 1745682027607, "results": "450", "hashOfConfig": "273"}, {"size": 2298, "mtime": 1749802834779, "results": "451", "hashOfConfig": "273"}, {"size": 823, "mtime": 1748779533260, "results": "452", "hashOfConfig": "273"}, {"size": 1380, "mtime": 1745682047729, "results": "453", "hashOfConfig": "273"}, {"size": 1145, "mtime": 1747902858133, "results": "454", "hashOfConfig": "273"}, {"size": 1118, "mtime": 1745682069045, "results": "455", "hashOfConfig": "273"}, {"size": 978, "mtime": 1747902858130, "results": "456", "hashOfConfig": "273"}, {"size": 1781, "mtime": 1745682059432, "results": "457", "hashOfConfig": "373"}, {"size": 10563, "mtime": 1748585335221, "results": "458", "hashOfConfig": "273"}, {"size": 3574, "mtime": 1746378664904, "results": "459", "hashOfConfig": "273"}, {"size": 3885, "mtime": 1746379640253, "results": "460", "hashOfConfig": "273"}, {"size": 4884, "mtime": 1746379602967, "results": "461", "hashOfConfig": "273"}, {"size": 6370, "mtime": 1748984865159, "results": "462", "hashOfConfig": "273"}, {"size": 1020, "mtime": 1745682406063, "results": "463", "hashOfConfig": "273"}, {"size": 2723, "mtime": 1749810234131, "results": "464", "hashOfConfig": "273"}, {"size": 6116, "mtime": 1746378664905, "results": "465", "hashOfConfig": "273"}, {"size": 1565, "mtime": 1747223318344, "results": "466", "hashOfConfig": "273"}, {"size": 1624, "mtime": 1745689590880, "results": "467", "hashOfConfig": "273"}, {"size": 40185, "mtime": 1749810642432, "results": "468", "hashOfConfig": "273"}, {"size": 70430, "mtime": 1748984865165, "results": "469", "hashOfConfig": "273"}, {"size": 9519, "mtime": 1747354195259, "results": "470", "hashOfConfig": "273"}, {"size": 5402, "mtime": 1749747958085, "results": "471", "hashOfConfig": "273"}, {"size": 4905, "mtime": 1747354192845, "results": "472", "hashOfConfig": "273"}, {"size": 59290, "mtime": 1749616133330, "results": "473", "hashOfConfig": "273"}, {"size": 26487, "mtime": 1748278828957, "results": "474", "hashOfConfig": "273"}, {"size": 24147, "mtime": 1747354834297, "results": "475", "hashOfConfig": "273"}, {"size": 23053, "mtime": 1749730024729, "results": "476", "hashOfConfig": "273"}, {"size": 37417, "mtime": 1748984865162, "results": "477", "hashOfConfig": "273"}, {"size": 16976, "mtime": 1749732457976, "results": "478", "hashOfConfig": "273"}, {"size": 8060, "mtime": 1747223318310, "results": "479", "hashOfConfig": "273"}, {"size": 16767, "mtime": 1748876218633, "results": "480", "hashOfConfig": "273"}, {"size": 3160, "mtime": 1745731138150, "results": "481", "hashOfConfig": "273"}, {"size": 7136, "mtime": 1747223318325, "results": "482", "hashOfConfig": "273"}, {"size": 20185, "mtime": 1747223318312, "results": "483", "hashOfConfig": "273"}, {"size": 2129, "mtime": 1746378664905, "results": "484", "hashOfConfig": "273"}, {"size": 955, "mtime": 1746378664898, "results": "485", "hashOfConfig": "273"}, {"size": 1184, "mtime": 1746378664905, "results": "486", "hashOfConfig": "273"}, {"size": 13378, "mtime": 1748984865159, "results": "487", "hashOfConfig": "273"}, {"size": 1099, "mtime": 1748326442261, "results": "488", "hashOfConfig": "273"}, {"size": 13560, "mtime": 1749747848907, "results": "489", "hashOfConfig": "273"}, {"size": 15114, "mtime": 1749202010110, "results": "490", "hashOfConfig": "273"}, {"size": 12224, "mtime": 1748220515100, "results": "491", "hashOfConfig": "273"}, {"size": 10534, "mtime": 1748220627343, "results": "492", "hashOfConfig": "273"}, {"size": 4031, "mtime": 1747278525096, "results": "493", "hashOfConfig": "273"}, {"size": 2036, "mtime": 1747283717643, "results": "494", "hashOfConfig": "273"}, {"size": 72414, "mtime": 1748330146983, "results": "495", "hashOfConfig": "273"}, {"size": 3589, "mtime": 1747355350828, "results": "496", "hashOfConfig": "273"}, {"size": 7509, "mtime": 1747353152130, "results": "497", "hashOfConfig": "273"}, {"size": 6153, "mtime": 1747354063004, "results": "498", "hashOfConfig": "273"}, {"size": 16791, "mtime": 1748473697636, "results": "499", "hashOfConfig": "273"}, {"size": 18350, "mtime": 1747902858137, "results": "500", "hashOfConfig": "273"}, {"size": 13464, "mtime": 1747902858145, "results": "501", "hashOfConfig": "273"}, {"size": 16606, "mtime": 1748220670679, "results": "502", "hashOfConfig": "273"}, {"size": 83855, "mtime": 1747721491423, "results": "503", "hashOfConfig": "273"}, {"size": 7887, "mtime": 1747520064907, "results": "504", "hashOfConfig": "273"}, {"size": 13915, "mtime": 1747524271204, "results": "505", "hashOfConfig": "273"}, {"size": 6596, "mtime": 1747524539464, "results": "506", "hashOfConfig": "273"}, {"size": 4152, "mtime": 1749202010110, "results": "507", "hashOfConfig": "273"}, {"size": 4762, "mtime": 1748513292659, "results": "508", "hashOfConfig": "273"}, {"size": 2443, "mtime": 1747719362467, "results": "509", "hashOfConfig": "273"}, {"size": 15359, "mtime": 1749549797204, "results": "510", "hashOfConfig": "273"}, {"size": 1863, "mtime": 1748356706231, "results": "511", "hashOfConfig": "273"}, {"size": 50801, "mtime": 1749552838208, "results": "512", "hashOfConfig": "273"}, {"size": 16122, "mtime": 1748221650546, "results": "513", "hashOfConfig": "273"}, {"size": 8222, "mtime": 1748250288630, "results": "514", "hashOfConfig": "273"}, {"size": 11210, "mtime": 1748223444732, "results": "515", "hashOfConfig": "273"}, {"size": 41966, "mtime": 1748250288654, "results": "516", "hashOfConfig": "273"}, {"size": 7612, "mtime": 1748250288649, "results": "517", "hashOfConfig": "273"}, {"size": 28083, "mtime": 1748250288657, "results": "518", "hashOfConfig": "273"}, {"size": 29543, "mtime": 1748984865164, "results": "519", "hashOfConfig": "273"}, {"size": 36682, "mtime": 1748250768337, "results": "520", "hashOfConfig": "273"}, {"size": 1840, "mtime": 1748250288662, "results": "521", "hashOfConfig": "273"}, {"size": 9569, "mtime": 1749694485776, "results": "522", "hashOfConfig": "273"}, {"size": 13102, "mtime": 1748250288647, "results": "523", "hashOfConfig": "273"}, {"size": 16077, "mtime": 1748365756504, "results": "524", "hashOfConfig": "273"}, {"size": 3987, "mtime": 1748709335425, "results": "525", "hashOfConfig": "273"}, {"size": 3539, "mtime": 1748800991826, "results": "526", "hashOfConfig": "273"}, {"size": 1712, "mtime": 1748800656400, "results": "527", "hashOfConfig": "273"}, {"size": 1983, "mtime": 1749648702993, "results": "528", "hashOfConfig": "273"}, {"size": 3908, "mtime": 1748801325319, "results": "529", "hashOfConfig": "273"}, {"size": 839, "mtime": 1748801505979, "results": "530", "hashOfConfig": "273"}, {"size": 365, "mtime": 1748984865153, "results": "531", "hashOfConfig": "273"}, {"size": 1199, "mtime": 1748984865156, "results": "532", "hashOfConfig": "273"}, {"size": 8860, "mtime": 1749202010110, "results": "533", "hashOfConfig": "273"}, {"size": 2130, "mtime": 1749521895529, "results": "534", "hashOfConfig": "273"}, {"size": 7780, "mtime": 1749202010110, "results": "535", "hashOfConfig": "273"}, {"size": 3832, "mtime": 1749202010110, "results": "536", "hashOfConfig": "273"}, {"size": 243, "mtime": 1749521895533, "results": "537", "hashOfConfig": "273"}, {"size": 2836, "mtime": 1749722547776, "results": "538", "hashOfConfig": "273"}, {"size": 734, "mtime": 1749721859047, "results": "539", "hashOfConfig": "273"}, {"size": 388, "mtime": 1749720429395, "results": "540", "hashOfConfig": "273"}, {"size": 579, "mtime": 1749731593347, "results": "541", "hashOfConfig": "273"}, {"size": 1530, "mtime": 1749732424739, "results": "542", "hashOfConfig": "273"}, {"size": 8754, "mtime": 1749809973025, "results": "543", "hashOfConfig": "273"}, {"size": 1664, "mtime": 1749808930419, "results": "544", "hashOfConfig": "273"}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e07wfn", {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1op6h7j", {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1262", "messages": "1263", "suppressedMessages": "1264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1265", "messages": "1266", "suppressedMessages": "1267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1268", "messages": "1269", "suppressedMessages": "1270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1271", "messages": "1272", "suppressedMessages": "1273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1274", "messages": "1275", "suppressedMessages": "1276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1277", "messages": "1278", "suppressedMessages": "1279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1280", "messages": "1281", "suppressedMessages": "1282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1283", "messages": "1284", "suppressedMessages": "1285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1286", "messages": "1287", "suppressedMessages": "1288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1289", "messages": "1290", "suppressedMessages": "1291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1292", "messages": "1293", "suppressedMessages": "1294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1295", "messages": "1296", "suppressedMessages": "1297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1298", "messages": "1299", "suppressedMessages": "1300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1301", "messages": "1302", "suppressedMessages": "1303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1304", "messages": "1305", "suppressedMessages": "1306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1307", "messages": "1308", "suppressedMessages": "1309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1310", "messages": "1311", "suppressedMessages": "1312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1313", "messages": "1314", "suppressedMessages": "1315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1316", "messages": "1317", "suppressedMessages": "1318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1319", "messages": "1320", "suppressedMessages": "1321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1322", "messages": "1323", "suppressedMessages": "1324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1325", "messages": "1326", "suppressedMessages": "1327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1328", "messages": "1329", "suppressedMessages": "1330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1331", "messages": "1332", "suppressedMessages": "1333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1334", "messages": "1335", "suppressedMessages": "1336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1337", "messages": "1338", "suppressedMessages": "1339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1340", "messages": "1341", "suppressedMessages": "1342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1343", "messages": "1344", "suppressedMessages": "1345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1346", "messages": "1347", "suppressedMessages": "1348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1349", "messages": "1350", "suppressedMessages": "1351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1352", "messages": "1353", "suppressedMessages": "1354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1355", "messages": "1356", "suppressedMessages": "1357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\App.js", ["1358", "1359", "1360", "1361"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\RegisterPage.jsx", ["1362"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\LoginPage.jsx", ["1363", "1364"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\error\\NotificationDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\CodeManagement.jsx", ["1365", "1366"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticleManagement.jsx", ["1367"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\ArticlePostPage.jsx", ["1368"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\HomePageManagement.jsx", ["1369", "1370", "1371", "1372", "1373"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\questionManagement.jsx", ["1374", "1375"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\question\\QuestionDetailAdmin.jsx", ["1376", "1377"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassManagement.jsx", ["1378"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\LessonManagement.jsx", ["1379", "1380", "1381", "1382", "1383", "1384"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\ClassUserManagement.jsx", ["1385", "1386", "1387", "1388", "1389"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamDetailAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\TrackingExamAdmin.jsx", ["1390", "1391", "1392", "1393"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\QuestionOfExamAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\ExamManagement.jsx", ["1394", "1395"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\exam\\PreviewExamAdmin.jsx", ["1396", "1397", "1398", "1399"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentDetailAdmin.jsx", ["1400"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\StudentManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\auth\\authSlice.js", ["1401"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\sidebar\\sidebarSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\user\\userSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\code\\codeSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\question\\questionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\class\\classSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\exam\\examSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\state\\stateApiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\answer\\answerSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\image\\imageSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attempt\\attemptSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\article\\articleSlice.js", ["1402"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\sanitizeInput.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\GoogleLoginButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\AuthLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputForAuthPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\AuthDropMenu.jsx", ["1403"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\dropMenu\\OptionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\logo\\BeeMathLogo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\checkBox\\AuthCheckbox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\CodeTable.jsx", ["1404", "1405", "1406", "1407", "1408", "1409", "1410"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ArticleTable.jsx", ["1411"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AdminModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTable.jsx", ["1412", "1413", "1414"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddCodeModal.jsx", ["1415"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddQuestionModal.jsx", ["1416"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddClassModal.jsx", ["1417", "1418", "1419"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassTable.jsx", ["1420", "1421", "1422"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\UserClassTable.jsx", ["1423", "1424"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\YouTubePlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\suggestInputBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewDetail.jsx", ["1425", "1426", "1427", "1428", "1429", "1430"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FunctionBarAdmin.jsx", ["1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\socket.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownEditer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutMultipleImages.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\LearningItemIcon.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\QuestionDetail.jsx", ["1439", "1440"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ClassDetail.jsx", ["1441", "1442", "1443", "1444", "1445"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\ExamDetail.jsx", ["1446", "1447", "1448", "1449"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreDistributionChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddExamModal.jsx", ["1450", "1451", "1452"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ExamTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBarAttemp.jsx", ["1453"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\PreviewExam.jsx", ["1454", "1455", "1456", "1457", "1458"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\UserDetail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\CustomSchedule.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\formatters.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\SlideShow.jsx", ["1459", "1460", "1461", "1462", "1463", "1464"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\userTable.jsx", ["1465", "1466"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ShowTotalResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\authApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\StudentThoughts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\apiHandler.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\RenderLatex.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\AnswerSummaryPieChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\countDownCard.jsx", ["1467"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\ExamCard.jsx", ["1468", "1469", "1470", "1471", "1472", "1473", "1474"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewPdf.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\card\\RelatedExamCard.jsx", ["1475", "1476", "1477", "1478", "1479"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ScoreBarChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\NetworkSpeedTest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ExamRegulationModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\input\\InputSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\FilterExamSidebar.jsx", ["1480", "1481", "1482"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\ClassImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\JoinClassModal.jsx", ["1483", "1484"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\codeApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\examApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\answerApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\QrCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\classApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\userApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderDoExamPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ViewLearning.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ScreenButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleRelatedSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleContent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleList.jsx", ["1485"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleBreadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\articleApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\imageApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attemptApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\pagination\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\AdminSidebar.jsx", ["1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ParticlesBackground.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ChangeDescriptionCode.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmDeleteModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TooltipTd.jsx", ["1497"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\QuestionTableRow.jsx", ["1498"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\question\\questionUtils.js", ["1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\UploadImage.jsx", ["1513"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\StatementTableRow.jsx", ["1514"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UploadPdf.jsx", ["1515"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonFunctionBarAdmin.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\PutImgae.jsx", ["1516"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\detail\\DetailTr.jsx", ["1517"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\Header.jsx", ["1518", "1519"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScheduleModal.jsx", ["1520"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TickSideBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ActiveFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ClassFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ChapterFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\CategoryFilters.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ArticleCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\requestInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\responseInterceptor.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MenuSidebar.jsx", ["1521"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\Choice.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\HeaderSidebar.jsx", ["1522"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\UserSidebar.jsx", ["1523"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\ChoiceHeader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\StudentCardModal.jsx", ["1524", "1525", "1526", "1527"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\image\\AvatarUploader.jsx", ["1528", "1529"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\Schedule.jsx", ["1530", "1531", "1532"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddStudentModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserLayoutHome.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\header\\HeaderHome.jsx", ["1533", "1534", "1535", "1536"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\achievement\\AchievementManagement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementImageModal.jsx", ["1537"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementStatTable.jsx", ["1538", "1539"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\AddAchievementStatModal.jsx", ["1540"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementImageTable.jsx", ["1541", "1542"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\AchievementCategoryTable.jsx", ["1543"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ConfirmModal.jsx", ["1544"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\achievement\\achievementSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\achievementApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\achievement\\AchievementSection.jsx", ["1545", "1546", "1547", "1548", "1549"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementCategoryModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementImageModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\EditAchievementStatModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\excelExport.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamTopbar.jsx", ["1550", "1551"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ExamSidebar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SettingsButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ThemeToggleButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SizeSlider.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\TimeDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionCounter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\SubmitButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\QuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ExamContent.jsx", ["1552", "1553", "1554"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\ShortAnswerQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\MultipleChoiceQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\TrueFalseQuestion.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\QuestionImage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\MarkableQuestionButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\questions\\SingleQuestionView.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ViewModeToggle.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\sidebar\\ProgressBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\DoExamPage.jsx", ["1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\Home.jsx", ["1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\HistoryDoExamPage.jsx", ["1583", "1584", "1585", "1586"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PracticePage.jsx", ["1587", "1588", "1589", "1590"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\PreviewExam.jsx", ["1591"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\home\\OverViewPage.jsx", ["1592", "1593", "1594", "1595"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ExamDetail.jsx", ["1596", "1597", "1598", "1599", "1600", "1601", "1602"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\RankingPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassUserPage.jsx", ["1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\LearningPage.jsx", ["1613", "1614"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\class\\ClassDetailPage.jsx", ["1615", "1616", "1617"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticlePage.jsx", ["1618", "1619", "1620", "1621", "1622", "1623", "1624"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\article\\ArticleListPage.jsx", ["1625", "1626", "1627", "1628", "1629", "1630", "1631"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\fullscreenUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\loading\\SpinnerDemo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\QuestionReportManagement.jsx", ["1632", "1633"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\questionReport\\questionReportSlice.js", ["1634"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ReportButton.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\questionReportApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\modal\\ReportQuestionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\utils\\NoTranslate.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\filter\\FilterExamSidebar.jsx", ["1635", "1636", "1637", "1638"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\notification\\NotificationPanel.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\notifications\\NotificationsPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\notification\\notificationSlice.js", ["1639"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\notificationApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\cacheManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\practice\\ScorePage.jsx", ["1640", "1641", "1642"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\breadcrumb\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernAnswerSummaryChart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\ModernScoreSummaryTable.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\article\\ModernArticleSidebar.jsx", ["1643", "1644", "1645", "1646", "1647", "1648", "1649"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\tuition\\tuitionSlice.js", ["1650", "1651", "1652"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPayments.jsx", ["1653", "1654"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\ClassTuitionList.jsx", ["1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\tuition\\TuitionPaymentList.jsx", ["1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\tuitionApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\tuition\\UserTuitionPaymentDetail.jsx", ["1683"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\PaymentModal.jsx", ["1684"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ClassSearchInput.jsx", ["1685"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\UserSearchInput.jsx", ["1686"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MultiClassSelector.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\attendance\\attendanceSlice.js", ["1687", "1688"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\attendanceApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\class\\AttendancePage.jsx", ["1689", "1690", "1691", "1692", "1693", "1694", "1695"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\attendance\\UserAttendancePage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\attendance\\AttendanceCard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminUserSearchPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\attendance\\AdminMobileAttendancePage.jsx", ["1696", "1697", "1698", "1699", "1700"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\lesson\\lessonSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\achievements\\AllAchievementsPage.jsx", ["1701", "1702", "1703"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\features\\AllFeaturesPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\user\\schedule\\AllSchedulePage.jsx", ["1704", "1705"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\lessonApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\bar\\FilterBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\team\\TeamSection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\banner\\ClassBanner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\ClassAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\MaintenancePage.jsx", ["1706", "1707"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceWrapper.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\config\\maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\utils\\maintenanceUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\MaintenanceCleaner.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\latex\\MarkDownPreview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\pages\\admin\\user\\UserClassManagement.jsx", ["1708", "1709", "1710"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\learningItem\\learningItemSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\ClassOfUserTable.jsx", ["1711"], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\UserAdminLayout.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\learningItemApi.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\table\\TotalComponent.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\pagination\\paginationReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\filter\\filterReducer.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\components\\button\\ButtonForUserPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\layouts\\404NotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\features\\doExam\\doExamSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\ToanThayBee\\ToanThayBeeFrontendReactjs\\src\\services\\doExamApi.js", [], [], {"ruleId": "1712", "severity": 1, "message": "1713", "line": 4, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 17}, {"ruleId": "1712", "severity": 1, "message": "1716", "line": 45, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 45, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1717", "line": 46, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 46, "endColumn": 26}, {"ruleId": "1712", "severity": 1, "message": "1718", "line": 51, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 51, "endColumn": 27}, {"ruleId": "1712", "severity": 1, "message": "1719", "line": 1, "column": 20, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1720", "line": 4, "column": 23, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 27}, {"ruleId": "1712", "severity": 1, "message": "1721", "line": 8, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 25}, {"ruleId": "1712", "severity": 1, "message": "1722", "line": 16, "column": 41, "nodeType": "1714", "messageId": "1715", "endLine": 16, "endColumn": 51}, {"ruleId": "1712", "severity": 1, "message": "1723", "line": 17, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 36}, {"ruleId": "1724", "severity": 1, "message": "1725", "line": 26, "column": 8, "nodeType": "1726", "endLine": 26, "endColumn": 19, "suggestions": "1727"}, {"ruleId": "1728", "severity": 1, "message": "1729", "line": 393, "column": 45, "nodeType": "1730", "endLine": 398, "endColumn": 47}, {"ruleId": "1712", "severity": 1, "message": "1731", "line": 6, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 11}, {"ruleId": "1712", "severity": 1, "message": "1732", "line": 6, "column": 27, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 31}, {"ruleId": "1712", "severity": 1, "message": "1733", "line": 7, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 25}, {"ruleId": "1712", "severity": 1, "message": "1734", "line": 8, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 32}, {"ruleId": "1712", "severity": 1, "message": "1735", "line": 88, "column": 23, "nodeType": "1714", "messageId": "1715", "endLine": 88, "endColumn": 31}, {"ruleId": "1712", "severity": 1, "message": "1736", "line": 14, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1723", "line": 15, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 36}, {"ruleId": "1712", "severity": 1, "message": "1737", "line": 2, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1738", "line": 3, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 3, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1739", "line": 5, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 39}, {"ruleId": "1712", "severity": 1, "message": "1740", "line": 15, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1741", "line": 16, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 16, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1742", "line": 22, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 22, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1743", "line": 28, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 28, "endColumn": 25}, {"ruleId": "1724", "severity": 1, "message": "1744", "line": 71, "column": 31, "nodeType": "1714", "endLine": 71, "endColumn": 42}, {"ruleId": "1724", "severity": 1, "message": "1745", "line": 266, "column": 8, "nodeType": "1726", "endLine": 266, "endColumn": 18, "suggestions": "1746"}, {"ruleId": "1712", "severity": 1, "message": "1740", "line": 6, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1741", "line": 9, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 9, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1742", "line": 18, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1743", "line": 20, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 20, "endColumn": 25}, {"ruleId": "1712", "severity": 1, "message": "1747", "line": 26, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 26, "endColumn": 26}, {"ruleId": "1712", "severity": 1, "message": "1748", "line": 11, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 11, "endColumn": 30}, {"ruleId": "1712", "severity": 1, "message": "1749", "line": 26, "column": 21, "nodeType": "1714", "messageId": "1715", "endLine": 26, "endColumn": 26}, {"ruleId": "1712", "severity": 1, "message": "1722", "line": 26, "column": 41, "nodeType": "1714", "messageId": "1715", "endLine": 26, "endColumn": 51}, {"ruleId": "1724", "severity": 1, "message": "1750", "line": 201, "column": 8, "nodeType": "1726", "endLine": 201, "endColumn": 19, "suggestions": "1751"}, {"ruleId": "1712", "severity": 1, "message": "1739", "line": 6, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 39}, {"ruleId": "1712", "severity": 1, "message": "1723", "line": 14, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 36}, {"ruleId": "1712", "severity": 1, "message": "1737", "line": 2, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1752", "line": 6, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 18}, {"ruleId": "1712", "severity": 1, "message": "1753", "line": 7, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1754", "line": 8, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1755", "line": 1, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1756", "line": 37, "column": 19, "nodeType": "1714", "messageId": "1715", "endLine": 37, "endColumn": 27}, {"ruleId": "1757", "severity": 1, "message": "1758", "line": 140, "column": 80, "nodeType": "1759", "messageId": "1760", "endLine": 140, "endColumn": 82}, {"ruleId": "1724", "severity": 1, "message": "1761", "line": 18, "column": 8, "nodeType": "1726", "endLine": 18, "endColumn": 10, "suggestions": "1762"}, {"ruleId": "1712", "severity": 1, "message": "1719", "line": 1, "column": 20, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1763", "line": 4, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 23}, {"ruleId": "1712", "severity": 1, "message": "1764", "line": 11, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 11, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1765", "line": 11, "column": 53, "nodeType": "1714", "messageId": "1715", "endLine": 11, "endColumn": 62}, {"ruleId": "1712", "severity": 1, "message": "1766", "line": 12, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 12, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1767", "line": 12, "column": 16, "nodeType": "1714", "messageId": "1715", "endLine": 12, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1768", "line": 27, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 27, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1769", "line": 5, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1770", "line": 11, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 11, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 18, "column": 36, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 46}, {"ruleId": "1724", "severity": 1, "message": "1772", "line": 43, "column": 8, "nodeType": "1726", "endLine": 43, "endColumn": 26, "suggestions": "1773"}, {"ruleId": "1712", "severity": 1, "message": "1719", "line": 2, "column": 20, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1774", "line": 7, "column": 47, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 68}, {"ruleId": "1712", "severity": 1, "message": "1775", "line": 17, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 16}, {"ruleId": "1712", "severity": 1, "message": "1776", "line": 29, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 29, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1777", "line": 36, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 36, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1770", "line": 7, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 15, "column": 36, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 46}, {"ruleId": "1712", "severity": 1, "message": "1778", "line": 19, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 19, "endColumn": 37}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 15, "column": 36, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 46}, {"ruleId": "1724", "severity": 1, "message": "1779", "line": 34, "column": 8, "nodeType": "1726", "endLine": 34, "endColumn": 53, "suggestions": "1780"}, {"ruleId": "1712", "severity": 1, "message": "1781", "line": 12, "column": 55, "nodeType": "1714", "messageId": "1715", "endLine": 12, "endColumn": 61}, {"ruleId": "1712", "severity": 1, "message": "1782", "line": 12, "column": 77, "nodeType": "1714", "messageId": "1715", "endLine": 12, "endColumn": 82}, {"ruleId": "1712", "severity": 1, "message": "1783", "line": 12, "column": 84, "nodeType": "1714", "messageId": "1715", "endLine": 12, "endColumn": 94}, {"ruleId": "1712", "severity": 1, "message": "1742", "line": 17, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 19}, {"ruleId": "1724", "severity": 1, "message": "1744", "line": 38, "column": 31, "nodeType": "1714", "endLine": 38, "endColumn": 42}, {"ruleId": "1712", "severity": 1, "message": "1784", "line": 93, "column": 17, "nodeType": "1714", "messageId": "1715", "endLine": 93, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1738", "line": 2, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1739", "line": 7, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 39}, {"ruleId": "1712", "severity": 1, "message": "1785", "line": 24, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 24, "endColumn": 26}, {"ruleId": "1712", "severity": 1, "message": "1786", "line": 25, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 25, "endColumn": 30}, {"ruleId": "1712", "severity": 1, "message": "1787", "line": 47, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 47, "endColumn": 22}, {"ruleId": "1724", "severity": 1, "message": "1788", "line": 68, "column": 8, "nodeType": "1726", "endLine": 68, "endColumn": 30, "suggestions": "1789"}, {"ruleId": "1712", "severity": 1, "message": "1790", "line": 78, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 78, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1791", "line": 86, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 86, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1792", "line": 8, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 28}, {"ruleId": "1724", "severity": 1, "message": "1793", "line": 45, "column": 8, "nodeType": "1726", "endLine": 45, "endColumn": 32, "suggestions": "1794"}, {"ruleId": "1712", "severity": 1, "message": "1795", "line": 6, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 18}, {"ruleId": "1712", "severity": 1, "message": "1796", "line": 7, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1740", "line": 13, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 13, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1741", "line": 14, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1743", "line": 23, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 23, "endColumn": 25}, {"ruleId": "1712", "severity": 1, "message": "1797", "line": 5, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1796", "line": 6, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1792", "line": 7, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 28}, {"ruleId": "1724", "severity": 1, "message": "1798", "line": 51, "column": 8, "nodeType": "1726", "endLine": 51, "endColumn": 28, "suggestions": "1799"}, {"ruleId": "1712", "severity": 1, "message": "1800", "line": 1, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1801", "line": 10, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 10, "endColumn": 17}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 286, "column": 59, "nodeType": "1804", "messageId": "1805", "endLine": 286, "endColumn": 60, "suggestions": "1806"}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 11, "column": 41, "nodeType": "1714", "messageId": "1715", "endLine": 11, "endColumn": 51}, {"ruleId": "1712", "severity": 1, "message": "1807", "line": 1, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 28}, {"ruleId": "1712", "severity": 1, "message": "1808", "line": 2, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1719", "line": 3, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 3, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1809", "line": 7, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1810", "line": 12, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 12, "endColumn": 31}, {"ruleId": "1712", "severity": 1, "message": "1811", "line": 2, "column": 37, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 45}, {"ruleId": "1712", "severity": 1, "message": "1812", "line": 2, "column": 47, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 60}, {"ruleId": "1712", "severity": 1, "message": "1813", "line": 2, "column": 62, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 67}, {"ruleId": "1712", "severity": 1, "message": "1814", "line": 2, "column": 69, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 77}, {"ruleId": "1712", "severity": 1, "message": "1815", "line": 3, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 3, "endColumn": 26}, {"ruleId": "1724", "severity": 1, "message": "1816", "line": 36, "column": 8, "nodeType": "1726", "endLine": 36, "endColumn": 43, "suggestions": "1817"}, {"ruleId": "1712", "severity": 1, "message": "1800", "line": 1, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1818", "line": 7, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 18}, {"ruleId": "1724", "severity": 1, "message": "1819", "line": 25, "column": 8, "nodeType": "1726", "endLine": 25, "endColumn": 20, "suggestions": "1820"}, {"ruleId": "1712", "severity": 1, "message": "1821", "line": 1, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1822", "line": 18, "column": 83, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 91}, {"ruleId": "1712", "severity": 1, "message": "1823", "line": 149, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 149, "endColumn": 23}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 202, "column": 44, "nodeType": "1826", "messageId": "1827", "endLine": 202, "endColumn": 46}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 202, "column": 113, "nodeType": "1826", "messageId": "1827", "endLine": 202, "endColumn": 115}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 253, "column": 40, "nodeType": "1826", "messageId": "1827", "endLine": 253, "endColumn": 42}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 253, "column": 109, "nodeType": "1826", "messageId": "1827", "endLine": 253, "endColumn": 111}, {"ruleId": "1712", "severity": 1, "message": "1828", "line": 14, "column": 38, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 47}, {"ruleId": "1712", "severity": 1, "message": "1829", "line": 14, "column": 49, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 56}, {"ruleId": "1712", "severity": 1, "message": "1830", "line": 14, "column": 58, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 70}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 34, "column": 40, "nodeType": "1826", "messageId": "1827", "endLine": 34, "endColumn": 42}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 34, "column": 109, "nodeType": "1826", "messageId": "1827", "endLine": 34, "endColumn": 111}, {"ruleId": "1712", "severity": 1, "message": "1749", "line": 15, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 18}, {"ruleId": "1712", "severity": 1, "message": "1831", "line": 15, "column": 44, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 52}, {"ruleId": "1724", "severity": 1, "message": "1832", "line": 42, "column": 8, "nodeType": "1726", "endLine": 42, "endColumn": 40, "suggestions": "1833"}, {"ruleId": "1712", "severity": 1, "message": "1818", "line": 1, "column": 25, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 33}, {"ruleId": "1712", "severity": 1, "message": "1834", "line": 4, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1835", "line": 4, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1836", "line": 1, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1720", "line": 2, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1837", "line": 2, "column": 16, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 27}, {"ruleId": "1712", "severity": 1, "message": "1838", "line": 4, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 28}, {"ruleId": "1712", "severity": 1, "message": "1839", "line": 4, "column": 46, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 67}, {"ruleId": "1712", "severity": 1, "message": "1840", "line": 13, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 13, "endColumn": 26}, {"ruleId": "1712", "severity": 1, "message": "1841", "line": 39, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 39, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1842", "line": 47, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 47, "endColumn": 16}, {"ruleId": "1712", "severity": 1, "message": "1843", "line": 54, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 54, "endColumn": 16}, {"ruleId": "1712", "severity": 1, "message": "1844", "line": 61, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 61, "endColumn": 25}, {"ruleId": "1712", "severity": 1, "message": "1845", "line": 77, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 77, "endColumn": 23}, {"ruleId": "1724", "severity": 1, "message": "1846", "line": 29, "column": 8, "nodeType": "1726", "endLine": 29, "endColumn": 10, "suggestions": "1847"}, {"ruleId": "1728", "severity": 1, "message": "1729", "line": 44, "column": 29, "nodeType": "1730", "endLine": 44, "endColumn": 98}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 229, "column": 30, "nodeType": "1804", "messageId": "1805", "endLine": 229, "endColumn": 31, "suggestions": "1848"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 242, "column": 61, "nodeType": "1804", "messageId": "1805", "endLine": 242, "endColumn": 62, "suggestions": "1849"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 248, "column": 29, "nodeType": "1804", "messageId": "1805", "endLine": 248, "endColumn": 30, "suggestions": "1850"}, {"ruleId": "1802", "severity": 1, "message": "1851", "line": 248, "column": 31, "nodeType": "1804", "messageId": "1805", "endLine": 248, "endColumn": 32, "suggestions": "1852"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 249, "column": 51, "nodeType": "1804", "messageId": "1805", "endLine": 249, "endColumn": 52, "suggestions": "1853"}, {"ruleId": "1802", "severity": 1, "message": "1851", "line": 249, "column": 53, "nodeType": "1804", "messageId": "1805", "endLine": 249, "endColumn": 54, "suggestions": "1854"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 330, "column": 30, "nodeType": "1804", "messageId": "1805", "endLine": 330, "endColumn": 31, "suggestions": "1855"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 352, "column": 61, "nodeType": "1804", "messageId": "1805", "endLine": 352, "endColumn": 62, "suggestions": "1856"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 358, "column": 29, "nodeType": "1804", "messageId": "1805", "endLine": 358, "endColumn": 30, "suggestions": "1857"}, {"ruleId": "1802", "severity": 1, "message": "1851", "line": 358, "column": 31, "nodeType": "1804", "messageId": "1805", "endLine": 358, "endColumn": 32, "suggestions": "1858"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 359, "column": 51, "nodeType": "1804", "messageId": "1805", "endLine": 359, "endColumn": 52, "suggestions": "1859"}, {"ruleId": "1802", "severity": 1, "message": "1851", "line": 359, "column": 53, "nodeType": "1804", "messageId": "1805", "endLine": 359, "endColumn": 54, "suggestions": "1860"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 433, "column": 30, "nodeType": "1804", "messageId": "1805", "endLine": 433, "endColumn": 31, "suggestions": "1861"}, {"ruleId": "1802", "severity": 1, "message": "1803", "line": 452, "column": 61, "nodeType": "1804", "messageId": "1805", "endLine": 452, "endColumn": 62, "suggestions": "1862"}, {"ruleId": "1724", "severity": 1, "message": "1863", "line": 80, "column": 8, "nodeType": "1726", "endLine": 80, "endColumn": 10, "suggestions": "1864"}, {"ruleId": "1728", "severity": 1, "message": "1729", "line": 56, "column": 37, "nodeType": "1730", "endLine": 56, "endColumn": 107}, {"ruleId": "1712", "severity": 1, "message": "1865", "line": 3, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 3, "endColumn": 27}, {"ruleId": "1724", "severity": 1, "message": "1866", "line": 73, "column": 8, "nodeType": "1726", "endLine": 73, "endColumn": 25, "suggestions": "1867"}, {"ruleId": "1712", "severity": 1, "message": "1719", "line": 3, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 3, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1868", "line": 5, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1869", "line": 34, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 34, "endColumn": 20}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 116, "column": 56, "nodeType": "1759", "messageId": "1760", "endLine": 116, "endColumn": 58}, {"ruleId": "1712", "severity": 1, "message": "1720", "line": 1, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1871", "line": 7, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 21}, {"ruleId": "1872", "severity": 1, "message": "1873", "line": 12, "column": 25, "nodeType": "1730", "endLine": 12, "endColumn": 614}, {"ruleId": "1712", "severity": 1, "message": "1800", "line": 1, "column": 46, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 49}, {"ruleId": "1712", "severity": 1, "message": "1874", "line": 3, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 3, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1875", "line": 14, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1876", "line": 14, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 37}, {"ruleId": "1724", "severity": 1, "message": "1877", "line": 21, "column": 8, "nodeType": "1726", "endLine": 21, "endColumn": 26, "suggestions": "1878"}, {"ruleId": "1724", "severity": 1, "message": "1879", "line": 40, "column": 8, "nodeType": "1726", "endLine": 40, "endColumn": 10, "suggestions": "1880"}, {"ruleId": "1712", "severity": 1, "message": "1731", "line": 2, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 11}, {"ruleId": "1712", "severity": 1, "message": "1738", "line": 3, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 3, "endColumn": 21}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 140, "column": 56, "nodeType": "1759", "messageId": "1760", "endLine": 140, "endColumn": 58}, {"ruleId": "1712", "severity": 1, "message": "1868", "line": 4, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1881", "line": 5, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 16}, {"ruleId": "1712", "severity": 1, "message": "1882", "line": 100, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 100, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1883", "line": 111, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 111, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1764", "line": 15, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1754", "line": 7, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 36}, {"ruleId": "1712", "severity": 1, "message": "1764", "line": 13, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 13, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1764", "line": 14, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 14, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1754", "line": 7, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 36}, {"ruleId": "1712", "severity": 1, "message": "1764", "line": 13, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 13, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1754", "line": 7, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 36}, {"ruleId": "1884", "severity": 1, "message": "1885", "line": 28, "column": 74, "nodeType": "1759", "messageId": "1886", "endLine": 28, "endColumn": 75}, {"ruleId": "1712", "severity": 1, "message": "1887", "line": 6, "column": 25, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1888", "line": 6, "column": 31, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 37}, {"ruleId": "1712", "severity": 1, "message": "1783", "line": 6, "column": 39, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 49}, {"ruleId": "1712", "severity": 1, "message": "1813", "line": 6, "column": 51, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 56}, {"ruleId": "1712", "severity": 1, "message": "1889", "line": 6, "column": 65, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 70}, {"ruleId": "1724", "severity": 1, "message": "1890", "line": 50, "column": 8, "nodeType": "1726", "endLine": 50, "endColumn": 28, "suggestions": "1891"}, {"ruleId": "1724", "severity": 1, "message": "1890", "line": 54, "column": 8, "nodeType": "1726", "endLine": 54, "endColumn": 21, "suggestions": "1892"}, {"ruleId": "1712", "severity": 1, "message": "1893", "line": 37, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 37, "endColumn": 18}, {"ruleId": "1712", "severity": 1, "message": "1894", "line": 44, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 44, "endColumn": 29}, {"ruleId": "1724", "severity": 1, "message": "1895", "line": 47, "column": 9, "nodeType": "1896", "endLine": 51, "endColumn": 4}, {"ruleId": "1712", "severity": 1, "message": "1897", "line": 16, "column": 45, "nodeType": "1714", "messageId": "1715", "endLine": 16, "endColumn": 57}, {"ruleId": "1724", "severity": 1, "message": "1898", "line": 54, "column": 8, "nodeType": "1726", "endLine": 54, "endColumn": 14, "suggestions": "1899"}, {"ruleId": "1724", "severity": 1, "message": "1900", "line": 99, "column": 11, "nodeType": "1896", "endLine": 104, "endColumn": 6, "suggestions": "1901"}, {"ruleId": "1724", "severity": 1, "message": "1902", "line": 99, "column": 11, "nodeType": "1896", "endLine": 104, "endColumn": 6, "suggestions": "1903"}, {"ruleId": "1724", "severity": 1, "message": "1904", "line": 106, "column": 11, "nodeType": "1896", "endLine": 111, "endColumn": 6}, {"ruleId": "1724", "severity": 1, "message": "1905", "line": 113, "column": 11, "nodeType": "1896", "endLine": 115, "endColumn": 6, "suggestions": "1906"}, {"ruleId": "1724", "severity": 1, "message": "1907", "line": 117, "column": 11, "nodeType": "1896", "endLine": 119, "endColumn": 6, "suggestions": "1908"}, {"ruleId": "1724", "severity": 1, "message": "1909", "line": 226, "column": 8, "nodeType": "1726", "endLine": 226, "endColumn": 23, "suggestions": "1910"}, {"ruleId": "1724", "severity": 1, "message": "1744", "line": 334, "column": 37, "nodeType": "1714", "endLine": 334, "endColumn": 48}, {"ruleId": "1724", "severity": 1, "message": "1744", "line": 357, "column": 37, "nodeType": "1714", "endLine": 357, "endColumn": 48}, {"ruleId": "1724", "severity": 1, "message": "1744", "line": 380, "column": 38, "nodeType": "1714", "endLine": 380, "endColumn": 49}, {"ruleId": "1724", "severity": 1, "message": "1911", "line": 692, "column": 8, "nodeType": "1726", "endLine": 692, "endColumn": 99, "suggestions": "1912"}, {"ruleId": "1724", "severity": 1, "message": "1913", "line": 800, "column": 8, "nodeType": "1726", "endLine": 800, "endColumn": 36, "suggestions": "1914"}, {"ruleId": "1712", "severity": 1, "message": "1915", "line": 2, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1916", "line": 4, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1917", "line": 18, "column": 25, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1918", "line": 18, "column": 137, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 146}, {"ruleId": "1712", "severity": 1, "message": "1919", "line": 18, "column": 148, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 161}, {"ruleId": "1712", "severity": 1, "message": "1920", "line": 18, "column": 163, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 173}, {"ruleId": "1712", "severity": 1, "message": "1921", "line": 18, "column": 175, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 182}, {"ruleId": "1712", "severity": 1, "message": "1922", "line": 18, "column": 184, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 189}, {"ruleId": "1712", "severity": 1, "message": "1923", "line": 18, "column": 191, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 205}, {"ruleId": "1712", "severity": 1, "message": "1924", "line": 34, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 34, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1925", "line": 39, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 39, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1926", "line": 40, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 40, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1927", "line": 64, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 64, "endColumn": 17}, {"ruleId": "1712", "severity": 1, "message": "1928", "line": 65, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 65, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1929", "line": 67, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 67, "endColumn": 17}, {"ruleId": "1712", "severity": 1, "message": "1800", "line": 4, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1930", "line": 6, "column": 35, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 46}, {"ruleId": "1712", "severity": 1, "message": "1931", "line": 10, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 10, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 26, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 26, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1719", "line": 5, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1818", "line": 5, "column": 21, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1749", "line": 17, "column": 23, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 28}, {"ruleId": "1712", "severity": 1, "message": "1765", "line": 17, "column": 49, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 58}, {"ruleId": "1724", "severity": 1, "message": "1898", "line": 37, "column": 8, "nodeType": "1726", "endLine": 37, "endColumn": 14, "suggestions": "1932"}, {"ruleId": "1712", "severity": 1, "message": "1834", "line": 15, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1933", "line": 18, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1731", "line": 21, "column": 98, "nodeType": "1714", "messageId": "1715", "endLine": 21, "endColumn": 99}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 262, "column": 38, "nodeType": "1759", "messageId": "1760", "endLine": 262, "endColumn": 40}, {"ruleId": "1712", "severity": 1, "message": "1741", "line": 7, "column": 111, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 115}, {"ruleId": "1712", "severity": 1, "message": "1934", "line": 7, "column": 117, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 127}, {"ruleId": "1712", "severity": 1, "message": "1821", "line": 10, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 10, "endColumn": 24}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 178, "column": 123, "nodeType": "1826", "messageId": "1827", "endLine": 178, "endColumn": 125}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 178, "column": 197, "nodeType": "1826", "messageId": "1827", "endLine": 178, "endColumn": 199}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 187, "column": 120, "nodeType": "1826", "messageId": "1827", "endLine": 187, "endColumn": 122}, {"ruleId": "1824", "severity": 1, "message": "1825", "line": 187, "column": 189, "nodeType": "1826", "messageId": "1827", "endLine": 187, "endColumn": 191}, {"ruleId": "1712", "severity": 1, "message": "1811", "line": 15, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1781", "line": 17, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 11}, {"ruleId": "1712", "severity": 1, "message": "1741", "line": 20, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 20, "endColumn": 9}, {"ruleId": "1712", "severity": 1, "message": "1935", "line": 21, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 21, "endColumn": 17}, {"ruleId": "1712", "severity": 1, "message": "1782", "line": 24, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 24, "endColumn": 10}, {"ruleId": "1712", "severity": 1, "message": "1834", "line": 28, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 28, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1936", "line": 30, "column": 7, "nodeType": "1714", "messageId": "1715", "endLine": 30, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1937", "line": 159, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 159, "endColumn": 18}, {"ruleId": "1712", "severity": 1, "message": "1938", "line": 160, "column": 21, "nodeType": "1714", "messageId": "1715", "endLine": 160, "endColumn": 31}, {"ruleId": "1724", "severity": 1, "message": "1939", "line": 166, "column": 8, "nodeType": "1726", "endLine": 166, "endColumn": 18, "suggestions": "1940"}, {"ruleId": "1712", "severity": 1, "message": "1941", "line": 26, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 26, "endColumn": 18}, {"ruleId": "1724", "severity": 1, "message": "1942", "line": 199, "column": 8, "nodeType": "1726", "endLine": 199, "endColumn": 21, "suggestions": "1943"}, {"ruleId": "1712", "severity": 1, "message": "1944", "line": 34, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 34, "endColumn": 20}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 49, "column": 37, "nodeType": "1759", "messageId": "1760", "endLine": 49, "endColumn": 39}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 51, "column": 44, "nodeType": "1759", "messageId": "1760", "endLine": 51, "endColumn": 46}, {"ruleId": "1712", "severity": 1, "message": "1945", "line": 8, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 16}, {"ruleId": "1712", "severity": 1, "message": "1731", "line": 8, "column": 18, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1814", "line": 8, "column": 21, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1946", "line": 15, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 17}, {"ruleId": "1712", "severity": 1, "message": "1947", "line": 16, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 16, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1948", "line": 26, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 26, "endColumn": 37}, {"ruleId": "1712", "severity": 1, "message": "1949", "line": 111, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 111, "endColumn": 23}, {"ruleId": "1712", "severity": 1, "message": "1834", "line": 7, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 7, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1741", "line": 13, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 13, "endColumn": 9}, {"ruleId": "1712", "severity": 1, "message": "1811", "line": 17, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1950", "line": 19, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 19, "endColumn": 13}, {"ruleId": "1724", "severity": 1, "message": "1951", "line": 129, "column": 8, "nodeType": "1726", "endLine": 129, "endColumn": 18, "suggestions": "1952"}, {"ruleId": "1712", "severity": 1, "message": "1953", "line": 155, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 155, "endColumn": 32}, {"ruleId": "1712", "severity": 1, "message": "1954", "line": 232, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 232, "endColumn": 29}, {"ruleId": "1724", "severity": 1, "message": "1955", "line": 39, "column": 8, "nodeType": "1726", "endLine": 39, "endColumn": 30, "suggestions": "1956"}, {"ruleId": "1728", "severity": 1, "message": "1729", "line": 226, "column": 69, "nodeType": "1730", "endLine": 230, "endColumn": 71}, {"ruleId": "1757", "severity": 1, "message": "1758", "line": 49, "column": 108, "nodeType": "1759", "messageId": "1760", "endLine": 49, "endColumn": 110}, {"ruleId": "1712", "severity": 1, "message": "1731", "line": 8, "column": 18, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1749", "line": 16, "column": 42, "nodeType": "1714", "messageId": "1715", "endLine": 16, "endColumn": 47}, {"ruleId": "1724", "severity": 1, "message": "1890", "line": 49, "column": 8, "nodeType": "1726", "endLine": 49, "endColumn": 28, "suggestions": "1957"}, {"ruleId": "1724", "severity": 1, "message": "1890", "line": 53, "column": 8, "nodeType": "1726", "endLine": 53, "endColumn": 21, "suggestions": "1958"}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 285, "column": 65, "nodeType": "1759", "messageId": "1760", "endLine": 285, "endColumn": 67}, {"ruleId": "1712", "severity": 1, "message": "1959", "line": 28, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 28, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1783", "line": 29, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 29, "endColumn": 15}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 406, "column": 101, "nodeType": "1759", "messageId": "1760", "endLine": 406, "endColumn": 103}, {"ruleId": "1712", "severity": 1, "message": "1960", "line": 55, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 55, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1961", "line": 55, "column": 32, "nodeType": "1714", "messageId": "1715", "endLine": 55, "endColumn": 42}, {"ruleId": "1712", "severity": 1, "message": "1962", "line": 59, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 59, "endColumn": 27}, {"ruleId": "1712", "severity": 1, "message": "1963", "line": 70, "column": 12, "nodeType": "1714", "messageId": "1715", "endLine": 70, "endColumn": 23}, {"ruleId": "1712", "severity": 1, "message": "1964", "line": 82, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 82, "endColumn": 29}, {"ruleId": "1712", "severity": 1, "message": "1965", "line": 88, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 88, "endColumn": 30}, {"ruleId": "1712", "severity": 1, "message": "1953", "line": 94, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 94, "endColumn": 32}, {"ruleId": "1712", "severity": 1, "message": "1966", "line": 5, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 25}, {"ruleId": "1757", "severity": 1, "message": "1758", "line": 513, "column": 35, "nodeType": "1759", "messageId": "1760", "endLine": 513, "endColumn": 37}, {"ruleId": "1757", "severity": 1, "message": "1758", "line": 578, "column": 35, "nodeType": "1759", "messageId": "1760", "endLine": 578, "endColumn": 37}, {"ruleId": "1712", "severity": 1, "message": "1781", "line": 19, "column": 3, "nodeType": "1714", "messageId": "1715", "endLine": 19, "endColumn": 9}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 31, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 31, "endColumn": 34}, {"ruleId": "1712", "severity": 1, "message": "1967", "line": 1, "column": 38, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 49}, {"ruleId": "1712", "severity": 1, "message": "1968", "line": 1, "column": 51, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 57}, {"ruleId": "1712", "severity": 1, "message": "1969", "line": 8, "column": 3, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 25}, {"ruleId": "1712", "severity": 1, "message": "1970", "line": 10, "column": 26, "nodeType": "1714", "messageId": "1715", "endLine": 10, "endColumn": 35}, {"ruleId": "1712", "severity": 1, "message": "1814", "line": 15, "column": 44, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 52}, {"ruleId": "1712", "severity": 1, "message": "1813", "line": 15, "column": 54, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 59}, {"ruleId": "1712", "severity": 1, "message": "1959", "line": 15, "column": 77, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 86}, {"ruleId": "1712", "severity": 1, "message": "1971", "line": 15, "column": 88, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 92}, {"ruleId": "1712", "severity": 1, "message": "1781", "line": 15, "column": 94, "nodeType": "1714", "messageId": "1715", "endLine": 15, "endColumn": 100}, {"ruleId": "1712", "severity": 1, "message": "1737", "line": 17, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1972", "line": 18, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 18, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1764", "line": 24, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 24, "endColumn": 17}, {"ruleId": "1712", "severity": 1, "message": "1765", "line": 24, "column": 51, "nodeType": "1714", "messageId": "1715", "endLine": 24, "endColumn": 60}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 24, "column": 62, "nodeType": "1714", "messageId": "1715", "endLine": 24, "endColumn": 72}, {"ruleId": "1712", "severity": 1, "message": "1973", "line": 5, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 14}, {"ruleId": "1712", "severity": 1, "message": "1970", "line": 21, "column": 26, "nodeType": "1714", "messageId": "1715", "endLine": 21, "endColumn": 35}, {"ruleId": "1712", "severity": 1, "message": "1737", "line": 27, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 27, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1742", "line": 32, "column": 9, "nodeType": "1714", "messageId": "1715", "endLine": 32, "endColumn": 17}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 35, "column": 24, "nodeType": "1714", "messageId": "1715", "endLine": 35, "endColumn": 34}, {"ruleId": "1712", "severity": 1, "message": "1974", "line": 53, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 53, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1975", "line": 54, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 54, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1976", "line": 106, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 106, "endColumn": 21}, {"ruleId": "1712", "severity": 1, "message": "1977", "line": 107, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 107, "endColumn": 27}, {"ruleId": "1712", "severity": 1, "message": "1978", "line": 250, "column": 9, "nodeType": "1714", "messageId": "1715", "endLine": 250, "endColumn": 25}, {"ruleId": "1712", "severity": 1, "message": "1979", "line": 256, "column": 9, "nodeType": "1714", "messageId": "1715", "endLine": 256, "endColumn": 33}, {"ruleId": "1724", "severity": 1, "message": "1980", "line": 434, "column": 6, "nodeType": "1726", "endLine": 434, "endColumn": 45, "suggestions": "1981"}, {"ruleId": "1712", "severity": 1, "message": "1982", "line": 575, "column": 9, "nodeType": "1714", "messageId": "1715", "endLine": 575, "endColumn": 23}, {"ruleId": "1712", "severity": 1, "message": "1983", "line": 865, "column": 9, "nodeType": "1714", "messageId": "1715", "endLine": 865, "endColumn": 18}, {"ruleId": "1712", "severity": 1, "message": "1782", "line": 20, "column": 3, "nodeType": "1714", "messageId": "1715", "endLine": 20, "endColumn": 8}, {"ruleId": "1712", "severity": 1, "message": "1984", "line": 2, "column": 26, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 32}, {"ruleId": "1724", "severity": 1, "message": "1744", "line": 35, "column": 29, "nodeType": "1714", "endLine": 35, "endColumn": 40}, {"ruleId": "1724", "severity": 1, "message": "1744", "line": 35, "column": 30, "nodeType": "1714", "endLine": 35, "endColumn": 41}, {"ruleId": "1757", "severity": 1, "message": "1870", "line": 330, "column": 68, "nodeType": "1759", "messageId": "1760", "endLine": 330, "endColumn": 70}, {"ruleId": "1757", "severity": 1, "message": "1758", "line": 343, "column": 64, "nodeType": "1759", "messageId": "1760", "endLine": 343, "endColumn": 66}, {"ruleId": "1712", "severity": 1, "message": "1800", "line": 1, "column": 10, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1985", "line": 12, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 12, "endColumn": 13}, {"ruleId": "1712", "severity": 1, "message": "1740", "line": 23, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 23, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1741", "line": 25, "column": 119, "nodeType": "1714", "messageId": "1715", "endLine": 25, "endColumn": 123}, {"ruleId": "1712", "severity": 1, "message": "1986", "line": 25, "column": 135, "nodeType": "1714", "messageId": "1715", "endLine": 25, "endColumn": 140}, {"ruleId": "1712", "severity": 1, "message": "1742", "line": 32, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 32, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1771", "line": 40, "column": 9, "nodeType": "1714", "messageId": "1715", "endLine": 40, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1755", "line": 4, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 4, "endColumn": 19}, {"ruleId": "1712", "severity": 1, "message": "1781", "line": 17, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 11}, {"ruleId": "1712", "severity": 1, "message": "1732", "line": 20, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 20, "endColumn": 9}, {"ruleId": "1712", "severity": 1, "message": "1987", "line": 27, "column": 5, "nodeType": "1714", "messageId": "1715", "endLine": 27, "endColumn": 32}, {"ruleId": "1724", "severity": 1, "message": "1988", "line": 122, "column": 8, "nodeType": "1726", "endLine": 122, "endColumn": 10, "suggestions": "1989"}, {"ruleId": "1712", "severity": 1, "message": "1814", "line": 6, "column": 56, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 64}, {"ruleId": "1712", "severity": 1, "message": "1888", "line": 6, "column": 73, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 79}, {"ruleId": "1712", "severity": 1, "message": "1783", "line": 6, "column": 81, "nodeType": "1714", "messageId": "1715", "endLine": 6, "endColumn": 91}, {"ruleId": "1712", "severity": 1, "message": "1990", "line": 5, "column": 60, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 66}, {"ruleId": "1712", "severity": 1, "message": "1945", "line": 5, "column": 68, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 74}, {"ruleId": "1712", "severity": 1, "message": "1991", "line": 2, "column": 42, "nodeType": "1714", "messageId": "1715", "endLine": 2, "endColumn": 51}, {"ruleId": "1712", "severity": 1, "message": "1992", "line": 5, "column": 11, "nodeType": "1714", "messageId": "1715", "endLine": 5, "endColumn": 24}, {"ruleId": "1712", "severity": 1, "message": "1834", "line": 8, "column": 8, "nodeType": "1714", "messageId": "1715", "endLine": 8, "endColumn": 22}, {"ruleId": "1712", "severity": 1, "message": "1944", "line": 16, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 16, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1993", "line": 17, "column": 13, "nodeType": "1714", "messageId": "1715", "endLine": 17, "endColumn": 20}, {"ruleId": "1712", "severity": 1, "message": "1818", "line": 1, "column": 21, "nodeType": "1714", "messageId": "1715", "endLine": 1, "endColumn": 29}, "no-unused-vars", "'Dashboard' is defined but never used.", "Identifier", "unusedVar", "'ClassTuitionList' is defined but never used.", "'TuitionPaymentList' is defined but never used.", "'AdminUserSearchPage' is defined but never used.", "'useEffect' is defined but never used.", "'Link' is defined but never used.", "'GoogleLoginButton' is defined but never used.", "'totalItems' is assigned a value but never used.", "'isFilterVIew' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'didInit' and 'navigate'. Either include them or remove the dependency array.", "ArrayExpression", ["1994"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'X' is defined but never used.", "'Save' is defined but never used.", "'PutMultipleImages' is defined but never used.", "'putSlideImagesForClass' is defined but never used.", "'category' is assigned a value but never used.", "'questions' is assigned a value but never used.", "'FunctionBarAdmin' is defined but never used.", "'useSelector' is defined but never used.", "'setIsFilterView' is defined but never used.", "'AdminSidebar' is defined but never used.", "'Home' is defined but never used.", "'navigate' is assigned a value but never used.", "'closeSidebar' is assigned a value but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has a missing dependency: 'fetchPdfFiles'. Either include it or remove the dependency array.", ["1995"], "'showAddStudent' is assigned a value but never used.", "'ScoreDistributionChart' is defined but never used.", "'limit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'prevRanks'. Either include them or remove the dependency array.", ["1996"], "'AdminModal' is defined but never used.", "'AddQuestionModal' is defined but never used.", "'setIsAddView' is defined but never used.", "'AdminLayout' is defined but never used.", "'errorMsg' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'options', 'selected', and 'type'. Either include them or remove the dependency array. If 'setPlaceholder' needs the current value of 'selected', you can also switch to useReducer instead of useState and read 'selected' in the reducer.", ["1997"], "'fetchAllCodes' is defined but never used.", "'search' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'id' is assigned a value but never used.", "'setId' is assigned a value but never used.", "'typeExists' is assigned a value but never used.", "'setSortOrder' is defined but never used.", "'resetFilters' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array. If 'fetchQuestions' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1998"], "'processInputForUpdate' is defined but never used.", "'ClockIcon' is assigned a value but never used.", "'AcademicCapIcon' is assigned a value but never used.", "'EyeIcon' is assigned a value but never used.", "'setDeleteMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classId'. Either include it or remove the dependency array.", ["1999"], "'Search' is defined but never used.", "'Clock' is defined but never used.", "'TrendingUp' is defined but never used.", "'data' is assigned a value but never used.", "'isDropdownOpen' is assigned a value but never used.", "'isDropdownOpenPage' is assigned a value but never used.", "'optionsPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSearch'. Either include it or remove the dependency array. If 'setSearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2000"], "'iconFilter' is assigned a value but never used.", "'iconExport' is assigned a value but never used.", "'SuggestInputBarAdmin' is defined but never used.", "React Hook useEffect has a missing dependency: 'question'. Either include it or remove the dependency array.", ["2001"], "'setClass' is defined but never used.", "'DropMenuBarAdmin' is defined but never used.", "'LatexRenderer' is defined but never used.", "React Hook useEffect has a missing dependency: 'exam'. Either include it or remove the dependency array.", ["2002"], "'use' is defined but never used.", "'compose' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["2003", "2004"], "'fetchExamQuestions' is defined but never used.", "'useDispatch' is defined but never used.", "'header' is defined but never used.", "'MarkdownPreviewWithMath' is defined but never used.", "'BookOpen' is defined but never used.", "'GraduationCap' is defined but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'defaultImageBanner' is defined but never used.", "React Hook useEffect has a missing dependency: 'startAutoPlay'. Either include it or remove the dependency array.", ["2005"], "'useState' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateTimeLeft'. Either include it or remove the dependency array.", ["2006"], "'ExamDefaultImage' is defined but never used.", "'imageUrl' is assigned a value but never used.", "'ActionButton' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'examClass' is assigned a value but never used.", "'chapter' is assigned a value but never used.", "'testDuration' is assigned a value but never used.", "'isSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["2007"], "'LoadingSpinner' is defined but never used.", "'ChevronRight' is defined but never used.", "'BeeMathLogo' is defined but never used.", "'useLocation' is defined but never used.", "'toggleCloseSidebar' is defined but never used.", "'toggleTuitionDropdown' is defined but never used.", "'tuitionDropdown' is assigned a value but never used.", "'iconTuition' is assigned a value but never used.", "'icon3' is assigned a value but never used.", "'icon4' is assigned a value but never used.", "'iconAttendance' is assigned a value but never used.", "'notification' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'maxLength'. Either include it or remove the dependency array.", ["2008"], ["2009", "2010"], ["2011", "2012"], ["2013", "2014"], "Unnecessary escape character: \\).", ["2015", "2016"], ["2017", "2018"], ["2019", "2020"], ["2021", "2022"], ["2023", "2024"], ["2025", "2026"], ["2027", "2028"], ["2029", "2030"], ["2031", "2032"], ["2033", "2034"], ["2035", "2036"], "React Hook useEffect has a missing dependency: 'handlePaste'. Either include it or remove the dependency array.", ["2037"], "'setSuccessMessage' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'putImageFunction'. Either include them or remove the dependency array. If 'putImageFunction' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2038"], "'InputSearch' is defined but never used.", "'menuOpen' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'isHovered' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'DefaultAvatar' is defined but never used.", "'avatarFile' is assigned a value but never used.", "'setAvatarFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'onImageChange'. Either include them or remove the dependency array. If 'onImageChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2039"], "React Hook useEffect has a missing dependency: 'handleFile'. Either include it or remove the dependency array.", ["2040"], "'logout' is defined but never used.", "'dispatch' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "'Star' is defined but never used.", "'Target' is defined but never used.", "'Crown' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchExams' and 'isClassroomExam'. Either include them or remove the dependency array.", ["2041"], ["2042"], "'isMobile' is assigned a value but never used.", "'currentQuestionType' is assigned a value but never used.", "The 'allQuestions' array makes the dependencies of useEffect Hook (at line 160) change on every render. To fix this, wrap the initialization of 'allQuestions' in its own useMemo() Hook.", "VariableDeclarator", "'isFullscreen' is defined but never used.", "React Hook useEffect has missing dependencies: 'examId' and 'navigate'. Either include them or remove the dependency array.", ["2043"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 510) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2044"], "The 'addQuestion' function makes the dependencies of useEffect Hook (at line 808) change on every render. To fix this, wrap the definition of 'addQuestion' in its own useCallback() Hook.", ["2045"], "The 'addErrorQuestion' function makes the dependencies of useEffect Hook (at line 808) change on every render. Move it inside the useEffect callback. Alternatively, wrap the definition of 'addErrorQuestion' in its own useCallback() Hook.", "The 'removeQuestion' function makes the dependencies of useEffect Hook (at line 808) change on every render. To fix this, wrap the definition of 'removeQuestion' in its own useCallback() Hook.", ["2046"], "The 'removeErrorQuestion' function makes the dependencies of useEffect Hook (at line 808) change on every render. To fix this, wrap the definition of 'removeErrorQuestion' in its own useCallback() Hook.", ["2047"], "React Hook useEffect has a missing dependency: 'flag'. Either include it or remove the dependency array.", ["2048"], "React Hook useEffect has a missing dependency: 'handleAutoSubmit'. Either include it or remove the dependency array.", ["2049"], "React Hook useEffect has missing dependencies: 'dispatch', 'exam?.isCheatingCheckEnabled', 'user.firstName', and 'user.lastName'. Either include them or remove the dependency array.", ["2050"], "'TeacherImage' is defined but never used.", "'Footer' is defined but never used.", "'Flag' is defined but never used.", "'Lightbulb' is defined but never used.", "'MessageCircle' is defined but never used.", "'HelpCircle' is defined but never used.", "'Contact' is defined but never used.", "'Phone' is defined but never used.", "'HeadphonesIcon' is defined but never used.", "'banner304151' is defined but never used.", "'calenderSlides' is assigned a value but never used.", "'bannerSlides' is assigned a value but never used.", "'vnRedColor' is assigned a value but never used.", "'vnYellowColor' is assigned a value but never used.", "'MomentCard' is assigned a value but never used.", "'setAttempts' is defined but never used.", "'ScoreBarChart' is defined but never used.", ["2051"], "'NoDataFound' is defined but never used.", "'BookMarked' is defined but never used.", "'ExternalLink' is defined but never used.", "'ButtonSidebar' is assigned a value but never used.", "'choice' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'limit'. Either include it or remove the dependency array.", ["2052"], "'UserLayout' is defined but never used.", "React Hook useEffect has a missing dependency: 'activeItem?.index'. Either include it or remove the dependency array.", ["2053"], "'loading' is assigned a value but never used.", "'Filter' is defined but never used.", "'SearchBar' is defined but never used.", "'formatDate' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'BookText' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.search'. Either include it or remove the dependency array.", ["2054"], "'getChapterDescription' is assigned a value but never used.", "'handleResetFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadReports'. Either include it or remove the dependency array.", ["2055"], ["2056"], ["2057"], "'BarChart2' is defined but never used.", "'articles' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "'handlePageChange' is assigned a value but never used.", "'currentPage' is assigned a value but never used.", "'getTypeDescription' is assigned a value but never used.", "'getClassDescription' is assigned a value but never used.", "'setErrorMessage' is defined but never used.", "'useCallback' is defined but never used.", "'useRef' is defined but never used.", "'fetchTuitionStatistics' is defined but never used.", "'setSearch' is defined but never used.", "'List' is defined but never used.", "'Chart' is defined but never used.", "'find' is defined but never used.", "'selectedUserId' is assigned a value but never used.", "'userSearchTerm' is assigned a value but never used.", "'viewPayment' is assigned a value but never used.", "'viewClassTuitions' is assigned a value but never used.", "'handleSelectUser' is assigned a value but never used.", "'handleClearUserSelection' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filterClass', 'filterMonth', 'filterOverdue', 'filterStatus', and 'inputValue'. Either include them or remove the dependency array.", ["2058"], "'handleBatchAdd' is assigned a value but never used.", "'iconBatch' is assigned a value but never used.", "'QrCode' is defined but never used.", "'setLimit' is defined but never used.", "'Award' is defined but never used.", "'clearUserAttendancesFilters' is defined but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'userId'. Either include them or remove the dependency array.", ["2059"], "'MapPin' is defined but never used.", "'RefreshCw' is defined but never used.", "'handleRefresh' is assigned a value but never used.", "'student' is assigned a value but never used.", {"desc": "2060", "fix": "2061"}, {"desc": "2062", "fix": "2063"}, {"desc": "2064", "fix": "2065"}, {"desc": "2066", "fix": "2067"}, {"desc": "2068", "fix": "2069"}, {"desc": "2070", "fix": "2071"}, {"desc": "2072", "fix": "2073"}, {"desc": "2074", "fix": "2075"}, {"desc": "2076", "fix": "2077"}, {"messageId": "2078", "fix": "2079", "desc": "2080"}, {"messageId": "2081", "fix": "2082", "desc": "2083"}, {"desc": "2084", "fix": "2085"}, {"desc": "2086", "fix": "2087"}, {"desc": "2088", "fix": "2089"}, {"desc": "2090", "fix": "2091"}, {"messageId": "2078", "fix": "2092", "desc": "2080"}, {"messageId": "2081", "fix": "2093", "desc": "2083"}, {"messageId": "2078", "fix": "2094", "desc": "2080"}, {"messageId": "2081", "fix": "2095", "desc": "2083"}, {"messageId": "2078", "fix": "2096", "desc": "2080"}, {"messageId": "2081", "fix": "2097", "desc": "2083"}, {"messageId": "2078", "fix": "2098", "desc": "2080"}, {"messageId": "2081", "fix": "2099", "desc": "2083"}, {"messageId": "2078", "fix": "2100", "desc": "2080"}, {"messageId": "2081", "fix": "2101", "desc": "2083"}, {"messageId": "2078", "fix": "2102", "desc": "2080"}, {"messageId": "2081", "fix": "2103", "desc": "2083"}, {"messageId": "2078", "fix": "2104", "desc": "2080"}, {"messageId": "2081", "fix": "2105", "desc": "2083"}, {"messageId": "2078", "fix": "2106", "desc": "2080"}, {"messageId": "2081", "fix": "2107", "desc": "2083"}, {"messageId": "2078", "fix": "2108", "desc": "2080"}, {"messageId": "2081", "fix": "2109", "desc": "2083"}, {"messageId": "2078", "fix": "2110", "desc": "2080"}, {"messageId": "2081", "fix": "2111", "desc": "2083"}, {"messageId": "2078", "fix": "2112", "desc": "2080"}, {"messageId": "2081", "fix": "2113", "desc": "2083"}, {"messageId": "2078", "fix": "2114", "desc": "2080"}, {"messageId": "2081", "fix": "2115", "desc": "2083"}, {"messageId": "2078", "fix": "2116", "desc": "2080"}, {"messageId": "2081", "fix": "2117", "desc": "2083"}, {"messageId": "2078", "fix": "2118", "desc": "2080"}, {"messageId": "2081", "fix": "2119", "desc": "2083"}, {"desc": "2120", "fix": "2121"}, {"desc": "2122", "fix": "2123"}, {"desc": "2124", "fix": "2125"}, {"desc": "2126", "fix": "2127"}, {"desc": "2128", "fix": "2129"}, {"desc": "2130", "fix": "2131"}, {"desc": "2132", "fix": "2133"}, {"desc": "2134", "fix": "2135"}, {"desc": "2134", "fix": "2136"}, {"desc": "2137", "fix": "2138"}, {"desc": "2139", "fix": "2140"}, {"desc": "2141", "fix": "2142"}, {"desc": "2143", "fix": "2144"}, {"desc": "2145", "fix": "2146"}, {"desc": "2132", "fix": "2147"}, {"desc": "2148", "fix": "2149"}, {"desc": "2150", "fix": "2151"}, {"desc": "2152", "fix": "2153"}, {"desc": "2154", "fix": "2155"}, {"desc": "2128", "fix": "2156"}, {"desc": "2130", "fix": "2157"}, {"desc": "2158", "fix": "2159"}, {"desc": "2160", "fix": "2161"}, "Update the dependencies array to be: [did<PERSON><PERSON><PERSON>, isAddView, navigate]", {"range": "2162", "text": "2163"}, "Update the dependencies array to be: [dispatch, fetchPdfFiles]", {"range": "2164", "text": "2165"}, "Update the dependencies array to be: [attempts1, dispatch, prevRanks]", {"range": "2166", "text": "2167"}, "Update the dependencies array to be: [options, selected, type]", {"range": "2168", "text": "2169"}, "Update the dependencies array to be: [dispatch, fetchQuestions, params]", {"range": "2170", "text": "2171"}, "Update the dependencies array to be: [dispatch, search, page, pageSize, sortOrder, classId]", {"range": "2172", "text": "2173"}, "Update the dependencies array to be: [inputValue, dispatch, setSearch]", {"range": "2174", "text": "2175"}, "Update the dependencies array to be: [codes, question, question.class]", {"range": "2176", "text": "2177"}, "Update the dependencies array to be: [codes, exam, exam?.class]", {"range": "2178", "text": "2179"}, "removeEscape", {"range": "2180", "text": "2181"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2182", "text": "2183"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [images.length, autoPlay, interval, startAutoPlay]", {"range": "2184", "text": "2185"}, "Update the dependencies array to be: [calculateTimeLeft, targetTime]", {"range": "2186", "text": "2187"}, "Update the dependencies array to be: [dispatch, currentPage, didInit, fetchExams]", {"range": "2188", "text": "2189"}, "Update the dependencies array to be: [maxLength]", {"range": "2190", "text": "2191"}, {"range": "2192", "text": "2181"}, {"range": "2193", "text": "2183"}, {"range": "2194", "text": "2181"}, {"range": "2195", "text": "2183"}, {"range": "2196", "text": "2181"}, {"range": "2197", "text": "2183"}, {"range": "2198", "text": "2181"}, {"range": "2199", "text": "2183"}, {"range": "2200", "text": "2181"}, {"range": "2201", "text": "2183"}, {"range": "2202", "text": "2181"}, {"range": "2203", "text": "2183"}, {"range": "2204", "text": "2181"}, {"range": "2205", "text": "2183"}, {"range": "2206", "text": "2181"}, {"range": "2207", "text": "2183"}, {"range": "2208", "text": "2181"}, {"range": "2209", "text": "2183"}, {"range": "2210", "text": "2181"}, {"range": "2211", "text": "2183"}, {"range": "2212", "text": "2181"}, {"range": "2213", "text": "2183"}, {"range": "2214", "text": "2181"}, {"range": "2215", "text": "2183"}, {"range": "2216", "text": "2181"}, {"range": "2217", "text": "2183"}, {"range": "2218", "text": "2181"}, {"range": "2219", "text": "2183"}, "Update the dependencies array to be: [handlePaste]", {"range": "2220", "text": "2221"}, "Update the dependencies array to be: [id, image, imageUrl, putImageFunction]", {"range": "2222", "text": "2223"}, "Update the dependencies array to be: [image, avatarUrl, onImageChange, id]", {"range": "2224", "text": "2225"}, "Update the dependencies array to be: [handleFile]", {"range": "2226", "text": "2227"}, "Update the dependencies array to be: [dispatch, fetchExams, isClassroomExam, isSearch]", {"range": "2228", "text": "2229"}, "Update the dependencies array to be: [currentPage, fetchExams, isClassroomExam]", {"range": "2230", "text": "2231"}, "Update the dependencies array to be: [exam, examId, navigate]", {"range": "2232", "text": "2233"}, "Wrap the definition of 'addQuestion' in its own useCallback() Hook.", {"range": "2234", "text": "2235"}, {"range": "2236", "text": "2235"}, "Wrap the definition of 'removeQuestion' in its own useCallback() Hook.", {"range": "2237", "text": "2238"}, "Wrap the definition of 'removeErrorQuestion' in its own useCallback() Hook.", {"range": "2239", "text": "2240"}, "Update the dependencies array to be: [flag, remainingTime]", {"range": "2241", "text": "2242"}, "Update the dependencies array to be: [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", {"range": "2243", "text": "2244"}, "Update the dependencies array to be: [user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", {"range": "2245", "text": "2246"}, {"range": "2247", "text": "2233"}, "Update the dependencies array to be: [dispatch, limit]", {"range": "2248", "text": "2249"}, "Update the dependencies array to be: [activeItem?.index, classDetail]", {"range": "2250", "text": "2251"}, "Update the dependencies array to be: [dispatch, location.search]", {"range": "2252", "text": "2253"}, "Update the dependencies array to be: [currentPage, didInit, loadReports]", {"range": "2254", "text": "2255"}, {"range": "2256", "text": "2229"}, {"range": "2257", "text": "2231"}, "Update the dependencies array to be: [dispatch, currentPage, limit, didInit, inputValue, filterStatus, filterMonth, filterOverdue, filterClass]", {"range": "2258", "text": "2259"}, "Update the dependencies array to be: [dispatch, userId]", {"range": "2260", "text": "2261"}, [922, 933], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, navigate]", [9489, 9499], "[dispatch, fetchPdfFiles]", [7946, 7957], "[attempts1, dispatch, prevRanks]", [669, 671], "[options, selected, type]", [1909, 1927], "[dispatch, fetchQuestions, params]", [1518, 1563], "[dispatch, search, page, pageSize, sortOrder, classId]", [2625, 2647], "[inputValue, dispatch, setSearch]", [2124, 2148], "[codes, question, question.class]", [2128, 2148], "[codes, exam, exam?.class]", [10595, 10596], "", [10595, 10595], "\\", [1142, 1177], "[images.length, autoPlay, interval, startAutoPlay]", [865, 877], "[calculateTimeLeft, targetTime]", [1868, 1900], "[dispatch, currentPage, didInit, fetchExams]", [972, 974], "[maxLength]", [8598, 8599], [8598, 8598], [9063, 9064], [9063, 9063], [9259, 9260], [9259, 9259], [9261, 9262], [9261, 9261], [9331, 9332], [9331, 9331], [9333, 9334], [9333, 9333], [11606, 11607], [11606, 11606], [12526, 12527], [12526, 12526], [12693, 12694], [12693, 12693], [12695, 12696], [12695, 12695], [12765, 12766], [12765, 12765], [12767, 12768], [12767, 12767], [15061, 15062], [15061, 15061], [15755, 15756], [15755, 15755], [2448, 2450], "[handlePaste]", [2840, 2857], "[id, image, imageUrl, putImageFunction]", [757, 775], "[image, avatarUrl, onImageChange, id]", [1462, 1464], "[handleFile]", [2341, 2361], "[dispatch, fetchExams, isClassroomExam, isSearch]", [2439, 2452], "[currentPage, fetchExams, isClassroomExam]", [2442, 2448], "[exam, examId, navigate]", [4138, 4340], "useCallback((questionId) => {\r\n        if (!saveQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        removeErrorQuestion(questionId);\r\n    })", [4138, 4340], [4606, 4714], "useCallback((questionId) => {\r\n        dispatch(setSaveQuestions(saveQuestions.filter(id => id !== questionId)));\r\n    })", [4751, 4861], "useCallback((questionId) => {\r\n        dispatch(setErrorQuestions(errorQuestions.filter(id => id !== questionId)));\r\n    })", [8577, 8592], "[flag, remainingTime]", [26457, 26548], "[exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId, handleAutoSubmit]", [30652, 30680], "[user.id, examId, attemptId, user.lastName, user.firstName, exam?.isCheatingCheckEnabled, dispatch]", [1496, 1502], [7568, 7578], "[dispatch, limit]", [6802, 6815], "[activeItem?.index, classDetail]", [6098, 6108], "[dispatch, location.search]", [1591, 1613], "[currentPage, didInit, loadReports]", [2184, 2204], [2278, 2291], [15401, 15440], "[dispatch, currentPage, limit, didInit, inputValue, filterStatus, filterMonth, filterOverdue, filterClass]", [3986, 3988], "[dispatch, userId]"]