{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\DoExamPage.jsx\",\n  _s = $RefreshSig$();\nimport HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useState, useEffect, useRef, useCallback } from \"react\";\nimport { debounce } from \"lodash\";\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\nimport { useParams } from \"react-router-dom\";\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\nimport { AnimatePresence } from \"framer-motion\";\nimport { Menu } from \"lucide-react\";\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\nimport ExamContent from \"../../../components/questions/ExamContent\";\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\nimport { setRemainingTime, summitExam, setSaveQuestions, setErrorQuestions, getRemainingTime, logUserActivity, submitAnswerWithAttempt, leaveExam, joinExam } from \"../../../features/doExam/doExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoExamPage = () => {\n  _s();\n  var _examContentRef$curre;\n  const {\n    examId\n  } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    exam\n  } = useSelector(state => state.exams);\n  const {\n    questions\n  } = useSelector(state => state.questions);\n  const {\n    answers\n  } = useSelector(state => state.answers);\n  const [fontSize, setFontSize] = useState(14); // 14px mặc định\n  const [imageSize, setImageSize] = useState(12); // đơn vị: rem\n  const questionRefs = useRef([]);\n  const [isAgree, setIsAgree] = useState(false);\n  const [attemptId, setAttemptId] = useState(null);\n  const attemptRef = useRef(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [flag, setFlag] = useState(false);\n  const [startTime1, setStartTime1] = useState(null);\n  const hasSubmittedRef = useRef(false);\n  const examRef = useRef(null);\n  const examContentRef = useRef(null);\n  useEffect(() => {\n    examRef.current = exam;\n    if ((exam === null || exam === void 0 ? void 0 : exam.acceptDoExam) === false) {\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  }, [exam]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchPublicExamById(examId));\n    }\n  }, [dispatch, examId]);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    remainingTime,\n    saveQuestions,\n    errorQuestions,\n    loadingJoin\n  } = useSelector(state => state.doExam);\n  const [markedQuestions, setMarkedQuestions] = useState(new Set());\n  const [timeWarningShown, setTimeWarningShown] = useState({\n    fiveMinutes: false,\n    oneMinute: false\n  });\n  const [isTimeBlinking, setIsTimeBlinking] = useState(false);\n  const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    const saved = localStorage.getItem(\"isDarkMode\");\n    return saved ? JSON.parse(saved) : false;\n  });\n  const [loadingSubmit, setLoadingSubmit] = useState(false);\n  const [isTimeUp, setIsTimeUp] = useState(false);\n  const [questionTN, setQuestionTN] = useState([]);\n  const [savingQuestions, setSavingQuestions] = useState(new Set()); // Track questions being saved\n  const [questionDS, setQuestionDS] = useState([]);\n  const [questionTLN, setQuestionTLN] = useState([]);\n  const [answerTN, setAnswerTN] = useState([]);\n  const [answerTLN, setAnswerTLN] = useState([]);\n  const [dsAnswers, setDsAnswers] = useState({});\n  document.addEventListener(\"copy\", e => {\n    e.preventDefault();\n  });\n  const addQuestion = questionId => {\n    if (!saveQuestions.includes(questionId)) {\n      dispatch(setSaveQuestions([...saveQuestions, questionId]));\n    }\n    removeErrorQuestion(questionId);\n  };\n  const addErrorQuestion = questionId => {\n    if (!errorQuestions.includes(questionId)) {\n      dispatch(setErrorQuestions([...errorQuestions, questionId]));\n    }\n    removeQuestion(questionId);\n  };\n  const removeQuestion = questionId => {\n    dispatch(setSaveQuestions(saveQuestions.filter(id => id !== questionId)));\n  };\n  const removeErrorQuestion = questionId => {\n    dispatch(setErrorQuestions(errorQuestions.filter(id => id !== questionId)));\n  };\n\n  // Hàm đánh dấu câu hỏi để xem lại sau\n  const toggleMarkQuestion = questionId => {\n    setMarkedQuestions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(questionId)) {\n        newSet.delete(questionId);\n      } else {\n        newSet.add(questionId);\n      }\n      return newSet;\n    });\n  };\n  const handleExitFullscreen = () => {\n    try {\n      exitFullscreen();\n    } catch (err) {\n      // Chỉ ghi log lỗi, không bắt lỗi\n      console.warn(\"Không thể thoát fullscreen:\", err);\n    }\n  };\n  const handleFontSizeChange = e => {\n    setFontSize(Number(e.target.value));\n  };\n  const handleImageSizeChange = e => {\n    setImageSize(Number(e.target.value));\n  };\n  const formatTime = seconds => {\n    const min = String(Math.floor(seconds / 60)).padStart(2, '0');\n    const sec = String(seconds % 60).padStart(2, '0');\n    return \"\".concat(min, \":\").concat(sec);\n  };\n  const handleFullScreen = async () => {\n    try {\n      // Sử dụng joinExam action thay vì fetch\n      const result = await dispatch(joinExam(examId)).unwrap();\n\n      // Xử lý khi join exam thành công\n      const {\n        attemptId,\n        startTime\n      } = result;\n      console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\n      setIsAgree(true);\n      attemptRef.current = attemptId;\n      setAttemptId(attemptId);\n      if (examId) {\n        dispatch(fetchPublicQuestionsByExamId(examId));\n      }\n      setStartTime1(startTime);\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) {\n        return;\n      }\n      try {\n        const success = await requestFullscreen();\n        if (!success) {\n          console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\n        }\n      } catch (err) {\n        console.error(\"❌ Lỗi khi bật fullscreen:\", err);\n        alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\n      }\n    } catch (error) {\n      console.error(\"Lỗi khi tham gia bài thi:\", error);\n      dispatch(setErrorMessage(\"Lỗi: \" + error.message));\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  };\n\n  // Removed socket-based exam_started listener - now handled in handleFullScreen\n\n  useEffect(() => {\n    if (exam !== null && exam !== void 0 && exam.testDuration && startTime1) {\n      const start = new Date(startTime1);\n      const now = new Date();\n      const elapsedSeconds = Math.floor((now - start) / 1000);\n      const totalSeconds = exam.testDuration * 60;\n      const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\n      dispatch(setRemainingTime(remaining));\n\n      // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\n      if (attemptId) {\n        dispatch(getRemainingTime({\n          examId,\n          attemptId\n        })).then(result => {\n          var _result$payload, _result$payload$data;\n          if (((_result$payload = result.payload) === null || _result$payload === void 0 ? void 0 : (_result$payload$data = _result$payload.data) === null || _result$payload$data === void 0 ? void 0 : _result$payload$data.remainingTime) !== undefined) {\n            dispatch(setRemainingTime(result.payload.data.remainingTime));\n          }\n        }).catch(error => {\n          console.error(\"Lỗi khi lấy thời gian từ server:\", error);\n        });\n      }\n    }\n  }, [startTime1, exam, attemptId, examId, dispatch]);\n  useEffect(() => {\n    if (flag) return;\n    if (!remainingTime) setFlag(true);\n  }, [remainingTime]);\n  const handleAutoSubmit = async () => {\n    if (hasSubmittedRef.current) {\n      console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\n      return;\n    }\n    hasSubmittedRef.current = true; // Đánh dấu đã submit\n    console.log(\"Kiểm tra attemptId:\", attemptId);\n    if (!attemptId) {\n      console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\n      return;\n    }\n    console.log(\"Đang nộp bài với attemptId:\", attemptId);\n    dispatch(setSaveQuestions([]));\n    setLoadingSubmit(true);\n    try {\n      // Sử dụng API thay vì socket để nộp bài\n      const result = await dispatch(summitExam(attemptId)).unwrap();\n      console.log(\"Nộp bài thành công:\", result);\n\n      // Xử lý khi nộp bài thành công\n      dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\n\n      // Thoát fullscreen mà không bắt lỗi\n      try {\n        exitFullscreen();\n      } catch (err) {\n        // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\n        console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\n      }\n      const safeAttemptId = attemptRef.current;\n      const currentExam = examRef.current;\n      if (!safeAttemptId) {\n        console.error(\"Không có attemptId khi navigate!\");\n        return;\n      }\n\n      // Log để debug\n      console.log(\"Current exam state:\", currentExam);\n      console.log(\"Attempt ID:\", safeAttemptId);\n      if (!currentExam || !currentExam.seeCorrectAnswer) {\n        console.log(\"Chuyển về trang danh sách do:\", {\n          examNull: !currentExam,\n          cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\n        });\n        navigate(\"/practice/exam/\".concat(examId));\n        return;\n      }\n      navigate(\"/practice/exam/attempt/\".concat(safeAttemptId, \"/score\"));\n    } catch (error) {\n      console.error(\"Lỗi khi nộp bài:\", error);\n      setLoadingSubmit(false);\n      dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\n      hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\n\n      // Thử nộp lại sau 3 giây nếu lỗi xảy ra\n      setTimeout(() => {\n        if (!loadingSubmit && attemptRef.current) {\n          console.log(\"Thử nộp bài lại sau lỗi...\");\n          handleAutoSubmit();\n        }\n      }, 5000);\n    }\n  };\n\n  // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\n  const navigateToQuestion = useCallback(questionId => {\n    setSelectedQuestion(questionId);\n\n    // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\n    if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\n      // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\n      examContentRef.current.goToQuestionById(questionId);\n    } else {\n      // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\n      // Tìm phần tử câu hỏi bằng querySelector\n      setTimeout(() => {\n        // Thử tìm phần tử bằng data-question-id\n        const element = document.querySelector(\"[data-question-id=\\\"\".concat(questionId, \"\\\"]\"));\n        if (element) {\n          const offset = 80; // chiều cao của header sticky\n          const y = element.getBoundingClientRect().top + window.scrollY - offset;\n          window.scrollTo({\n            top: y,\n            behavior: \"smooth\"\n          });\n        } else {\n          // Fallback: Sử dụng refs\n          const refElement = questionRefs.current[questionId];\n          if (refElement) {\n            const offset = 80; // chiều cao của header sticky\n            const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\n            window.scrollTo({\n              top: y,\n              behavior: \"smooth\"\n            });\n          }\n        }\n      }, 0);\n    }\n  }, [questionRefs, examContentRef]);\n\n  // navigateToQuestion is used directly in components\n\n  // Create debounced submit functions with different delays for different question types\n  const submitAnswerTNDebounced = useCallback(debounce(payload => {\n    // Add to saving state\n    setSavingQuestions(prev => new Set(prev).add(payload.questionId));\n    dispatch(submitAnswerWithAttempt(payload)).then(result => {\n      // Remove from saving state\n      setSavingQuestions(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(payload.questionId);\n        return newSet;\n      });\n      if (result.type.endsWith('/fulfilled')) {\n        console.log(\"Đã lưu câu trả lời TN thành công\");\n        // API success - questionId will be added to saveQuestions by slice\n        // and removed from errorQuestions automatically\n      } else {\n        console.error(\"Lỗi khi lưu câu trả lời TN:\", result.error);\n        // API failed - questionId will be added to errorQuestions by slice\n        // and removed from saveQuestions automatically\n      }\n    });\n  }, 500),\n  // TN: 500ms - shorter delay for multiple choice\n  [dispatch, attemptId]);\n  const submitAnswerDSDebounced = useCallback(debounce(payload => {\n    // Add to saving state\n    setSavingQuestions(prev => new Set(prev).add(payload.questionId));\n    dispatch(submitAnswerWithAttempt(payload)).then(result => {\n      // Remove from saving state\n      setSavingQuestions(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(payload.questionId);\n        return newSet;\n      });\n      if (result.type.endsWith('/fulfilled')) {\n        console.log(\"Đã lưu câu trả lời DS thành công\");\n        // API success - questionId will be added to saveQuestions by slice\n        // and removed from errorQuestions automatically\n      } else {\n        console.error(\"Lỗi khi lưu câu trả lời DS:\", result.error);\n        // API failed - questionId will be added to errorQuestions by slice\n        // and removed from saveQuestions automatically\n      }\n    });\n  }, 800),\n  // DS: 800ms - medium delay for true/false\n  [dispatch, attemptId]);\n  const submitAnswerTLNDebounced = useCallback(debounce(payload => {\n    // Add to saving state\n    setSavingQuestions(prev => new Set(prev).add(payload.questionId));\n    dispatch(submitAnswerWithAttempt(payload)).then(result => {\n      // Remove from saving state\n      setSavingQuestions(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(payload.questionId);\n        return newSet;\n      });\n      if (result.type.endsWith('/fulfilled')) {\n        console.log(\"Đã lưu câu trả lời TLN thành công\");\n        // API success - questionId will be added to saveQuestions by slice\n        // and removed from errorQuestions automatically\n      } else {\n        console.error(\"Lỗi khi lưu câu trả lời TLN:\", result.error);\n        // API failed - questionId will be added to errorQuestions by slice\n        // and removed from saveQuestions automatically\n      }\n    });\n  }, 1500),\n  // TLN: 1500ms - longer delay for text input\n  [dispatch, attemptId]);\n\n  // Cleanup debounced functions on unmount\n  useEffect(() => {\n    return () => {\n      submitAnswerTNDebounced.cancel();\n      submitAnswerDSDebounced.cancel();\n      submitAnswerTLNDebounced.cancel();\n    };\n  }, [submitAnswerTNDebounced, submitAnswerDSDebounced, submitAnswerTLNDebounced]);\n  const handleSelectAnswerTN = (questionId, statementId, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const newAnswer = {\n      questionId,\n      answerContent: statementId,\n      typeOfQuestion: type\n    };\n    dispatch(setAnswers(newAnswer));\n\n    // Sử dụng debounced API call cho TN\n    submitAnswerTNDebounced({\n      questionId,\n      answerContent: statementId,\n      type,\n      attemptId\n    });\n  };\n  const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const currentAnswers = dsAnswers[questionId] || [];\n    const existing = currentAnswers.find(ans => ans.statementId === statementId);\n\n    // 🔁 Nếu đáp án đã giống thì không gửi lại\n    if (existing && existing.answer === selectedAnswer) {\n      return;\n    }\n    const updatedAnswers = currentAnswers.map(ans => ans.statementId === statementId ? {\n      ...ans,\n      answer: selectedAnswer\n    } : ans);\n\n    // Nếu chưa có statement này\n    if (!existing) {\n      updatedAnswers.push({\n        statementId,\n        answer: selectedAnswer\n      });\n    }\n    dispatch(setAnswers({\n      questionId,\n      answerContent: JSON.stringify(updatedAnswers),\n      typeOfQuestion: \"DS\"\n    }));\n\n    // Sử dụng debounced API call cho DS\n    submitAnswerDSDebounced({\n      questionId,\n      answerContent: updatedAnswers,\n      type: \"DS\",\n      attemptId\n    });\n  };\n  const handleSelectAnswerTLN = (questionId, answerContent, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    if (!answerContent || answerContent.trim() === \"\") {\n      return;\n    }\n    const formattedAnswer = answerContent.trim().replace(\",\", \".\");\n    dispatch(setAnswers({\n      questionId,\n      answerContent,\n      typeOfQuestion: type\n    }));\n\n    // Sử dụng debounced API call cho TLN - đặc biệt hữu ích khi user gõ liên tục\n    submitAnswerTLNDebounced({\n      questionId,\n      answerContent: formattedAnswer,\n      type,\n      attemptId\n    });\n  };\n\n  // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\n  const questionsToMarkAsSaved = useRef(new Set());\n\n  // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\n  useEffect(() => {\n    if (questionsToMarkAsSaved.current.size > 0) {\n      questionsToMarkAsSaved.current.forEach(questionId => {\n        if (!saveQuestions.includes(questionId)) {\n          addQuestion(questionId);\n        }\n      });\n      questionsToMarkAsSaved.current.clear();\n    }\n  }, [saveQuestions, addQuestion]);\n\n  // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\n  useEffect(() => {\n    // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\n    const frameId = requestAnimationFrame(() => {\n      if (questionsToMarkAsSaved.current.size > 0) {\n        const questionIds = [...questionsToMarkAsSaved.current];\n        questionsToMarkAsSaved.current.clear();\n\n        // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\n        questionIds.forEach(questionId => {\n          if (!saveQuestions.includes(questionId)) {\n            addQuestion(questionId);\n          }\n        });\n      }\n    });\n    return () => cancelAnimationFrame(frameId);\n  });\n  const isTNSelected = useCallback((questionId, statementId) => {\n    const isSelected = answerTN.some(ans => ans.questionId === questionId && ans.answerContent && String(ans.answerContent) === String(statementId));\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.includes(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [answerTN, saveQuestions]);\n  const isDSChecked = useCallback((questionId, statementId, bool) => {\n    var _dsAnswers$questionId, _dsAnswers$questionId2;\n    const isSelected = ((_dsAnswers$questionId = dsAnswers[questionId]) === null || _dsAnswers$questionId === void 0 ? void 0 : _dsAnswers$questionId.some(a => a.statementId === statementId && a.answer === bool)) || false;\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.includes(questionId) && ((_dsAnswers$questionId2 = dsAnswers[questionId]) === null || _dsAnswers$questionId2 === void 0 ? void 0 : _dsAnswers$questionId2.length) === 4) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [dsAnswers, saveQuestions]);\n  const getTLNDefaultValue = useCallback(questionId => {\n    var _matched$answerConten;\n    const matched = answerTLN.find(ans => ans.questionId === questionId);\n    const content = (matched === null || matched === void 0 ? void 0 : (_matched$answerConten = matched.answerContent) === null || _matched$answerConten === void 0 ? void 0 : _matched$answerConten.replace(/^\"|\"$/g, \"\")) || \"\";\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (content && !saveQuestions.includes(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return content;\n  }, [answerTLN, saveQuestions]);\n\n  // useEffect(() => {\n  //     if (examId) {\n  //         dispatch(fetchPublicQuestionsByExamId(examId));\n  //     }\n  // }, [dispatch, examId]);\n\n  useEffect(() => {\n    if (questions) {\n      setQuestionTN(questions.filter(question => question.typeOfQuestion === \"TN\"));\n      setQuestionDS(questions.filter(question => question.typeOfQuestion === \"DS\"));\n      setQuestionTLN(questions.filter(question => question.typeOfQuestion === \"TLN\"));\n    }\n  }, [questions]);\n  useEffect(() => {\n    // Kiểm tra answers có phải là mảng không\n    if (!Array.isArray(answers) || answers.length === 0) return;\n    const tn = [];\n    const tln = [];\n    const dsMap = {};\n\n    // Sử dụng for...of thay vì forEach để tránh lỗi\n    for (const answer of answers) {\n      if (answer.typeOfQuestion === \"TN\") {\n        tn.push(answer);\n      } else if (answer.typeOfQuestion === \"TLN\") {\n        tln.push(answer);\n      } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\n        try {\n          const parsed = JSON.parse(answer.answerContent);\n          dsMap[answer.questionId] = parsed;\n        } catch (err) {\n          console.error(\"Lỗi parse DS answerContent:\", err);\n        }\n      }\n    }\n    setAnswerTN(tn);\n    setAnswerTLN(tln);\n    setDsAnswers(dsMap);\n\n    // Note: Score calculation is now handled when submitting exam\n    // No need to calculate score in real-time\n  }, [answers]);\n  useEffect(() => {\n    if (attemptId) {\n      dispatch(fetchAnswersByAttempt(attemptId));\n    }\n  }, [dispatch, attemptId]);\n  useEffect(() => {\n    if (!(exam !== null && exam !== void 0 && exam.testDuration) || remainingTime === null || !isAgree) return;\n\n    // Kiểm tra và hiển thị cảnh báo thời gian\n    const checkTimeWarnings = time => {\n      // Cảnh báo khi còn 5 phút\n      if (time === 300 && !timeWarningShown.fiveMinutes) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          fiveMinutes: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\n\n        // Tắt hiệu ứng nhấp nháy sau 10 giây\n        setTimeout(() => {\n          setIsTimeBlinking(false);\n        }, 10000);\n      }\n\n      // Cảnh báo khi còn 1 phút\n      if (time === 60 && !timeWarningShown.oneMinute) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          oneMinute: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\n\n        // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\n      }\n    };\n\n    // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API\n    const syncTimeInterval = setInterval(() => {\n      if (attemptId) {\n        dispatch(getRemainingTime({\n          examId,\n          attemptId\n        })).then(result => {\n          var _result$payload2, _result$payload2$data;\n          if (((_result$payload2 = result.payload) === null || _result$payload2 === void 0 ? void 0 : (_result$payload2$data = _result$payload2.data) === null || _result$payload2$data === void 0 ? void 0 : _result$payload2$data.remainingTime) !== undefined) {\n            dispatch(setRemainingTime(result.payload.data.remainingTime));\n          }\n        }).catch(error => {\n          console.error(\"Lỗi khi đồng bộ thời gian:\", error);\n        });\n      }\n    }, 30000); // Đồng bộ thời gian mỗi 30 giây\n\n    const interval = setInterval(() => {\n      dispatch(setRemainingTime(prev => {\n        if (prev <= 1) {\n          // dùng <=1 để đảm bảo không bị âm\n          clearInterval(interval);\n          clearInterval(syncTimeInterval);\n          // Đánh dấu là đã hết thời gian\n          setIsTimeUp(true);\n          setIsTimeBlinking(false);\n          // Thử nộp bài\n          handleAutoSubmit();\n          return 0;\n        }\n\n        // Kiểm tra cảnh báo thời gian\n        checkTimeWarnings(prev);\n        return prev - 1;\n      }));\n    }, 1000);\n    return () => {\n      clearInterval(interval);\n      clearInterval(syncTimeInterval);\n    };\n  }, [exam === null || exam === void 0 ? void 0 : exam.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId]); // Chỉ phụ thuộc vào các giá trị cần thiết\n\n  // Removed socket connection management - using API only\n\n  // frontend\n  useEffect(() => {\n    if (!attemptId || !(user !== null && user !== void 0 && user.id) || !examId || attemptId === null || attemptId === undefined) return;\n    if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) return;\n    console.log(\"Đã bật theo dõi hành vi gian lận\");\n    const recentLogs = new Set(); // chống log lặp\n    const logOnce = (key, payload) => {\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled) || recentLogs.has(key)) return;\n      recentLogs.add(key);\n\n      // Sử dụng API thay vì socket\n      dispatch(logUserActivity({\n        examId,\n        attemptId,\n        activityType: payload.type || 'user_activity',\n        details: {\n          ...payload,\n          name: user.lastName + \" \" + user.firstName\n        }\n      }));\n      setTimeout(() => recentLogs.delete(key), 5000);\n    };\n\n    // 📌 Thoát fullscreen\n    const handleFullscreenChange = () => {\n      if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {\n        logOnce(\"exit_fullscreen\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"EF\",\n          action: \"exit_fullscreen\",\n          detail: JSON.stringify({\n            reason: \"User exited fullscreen mode\"\n          })\n        });\n      }\n    };\n\n    // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === \"hidden\") {\n        logOnce(\"tab_blur\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"TB\",\n          action: \"tab_blur\",\n          detail: JSON.stringify({\n            message: \"User switched tab or minimized window\"\n          })\n        });\n      }\n    };\n\n    // 📌 Copy nội dung\n    const handleCopy = () => {\n      logOnce(\"copy_detected\", {\n        studentId: user.id,\n        attemptId,\n        examId,\n        code: \"COP\",\n        action: \"copy_detected\",\n        detail: JSON.stringify({\n          message: \"User copied content\"\n        })\n      });\n    };\n\n    // 📌 Phím đáng ngờ\n    const handleSuspiciousKey = e => {\n      const suspiciousKeys = [\"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"];\n      const combo = \"\".concat(e.ctrlKey ? \"Ctrl+\" : \"\").concat(e.shiftKey ? \"Shift+\" : \"\").concat(e.altKey ? \"Alt+\" : \"\").concat(e.metaKey ? \"Meta+\" : \"\").concat(e.key);\n      if (suspiciousKeys.includes(e.key) || combo === \"Ctrl+Shift+I\" || combo === \"Ctrl+Shift+C\") {\n        logOnce(\"key_\".concat(combo), {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"SK\",\n          action: \"suspicious_key\",\n          detail: JSON.stringify({\n            key: e.key,\n            code: e.code,\n            combo\n          })\n        });\n      }\n    };\n    document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n    document.addEventListener(\"copy\", handleCopy);\n    document.addEventListener(\"keydown\", handleSuspiciousKey);\n    return () => {\n      document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      document.removeEventListener(\"copy\", handleCopy);\n      document.removeEventListener(\"keydown\", handleSuspiciousKey);\n    };\n  }, [user.id, examId, attemptId]);\n  useEffect(() => {\n    // Removed all socket event listeners - using API responses instead\n    // Answer save/error status is now handled in submitAnswerWithAttempt action responses\n    // Timer updates are handled via getRemainingTime API calls\n    // Auto-submit is handled via client-side timer logic\n  }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\n  useEffect(() => {\n    localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  // Hàm xử lý chuyển đổi câu hỏi\n  const handleKeyDown = useCallback(e => {\n    // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\n    if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\n      // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\n      e.preventDefault();\n\n      // Nếu không có câu hỏi, thoát khỏi hàm\n      if (!questions || questions.length === 0) return;\n      const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\n      const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\n      if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\n        const prevQuestionId = allQuestions[currentIndex - 1].id;\n        console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\n        navigateToQuestion(prevQuestionId);\n      } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\n        const nextQuestionId = allQuestions[currentIndex + 1].id;\n        console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\n        navigateToQuestion(nextQuestionId);\n      }\n    }\n  }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\n  // Lắng nghe sự kiện bàn phím\n  useEffect(() => {\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [handleKeyDown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full \".concat(isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'),\n    children: [/*#__PURE__*/_jsxDEV(HeaderDoExamPage, {\n      nameExam: exam === null || exam === void 0 ? void 0 : exam.name,\n      onExitFullscreen: handleExitFullscreen,\n      isDarkMode: !isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 860,\n      columnNumber: 13\n    }, this), isAgree ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\",\n      children: [/*#__PURE__*/_jsxDEV(ExamContent, {\n        ref: examContentRef,\n        loading1: loadingJoin,\n        isDarkMode: isDarkMode,\n        questionTN: questionTN,\n        questionDS: questionDS,\n        questionTLN: questionTLN,\n        handlers: {\n          handleSelectAnswerTN,\n          handleSelectAnswerDS,\n          handleSelectAnswerTLN,\n          isTNSelected,\n          isDSChecked,\n          getTLNDefaultValue,\n          setQuestionRef: (id, el) => questionRefs.current[id] = el,\n          setSelectedQuestion: id => setSelectedQuestion(id)\n        },\n        settings: {\n          selectedQuestion,\n          isDarkMode,\n          fontSize,\n          imageSize,\n          prefixStatementTN,\n          prefixStatementDS,\n          isTimeUp,\n          markedQuestions,\n          toggleMarkQuestion\n        },\n        isTimeUp: isTimeUp\n        // Để undefined để component tự quyết định dựa trên thiết bị\n        ,\n        initialSingleMode: undefined,\n        handleAutoSubmit: handleAutoSubmit,\n        loadingSubmit: loadingSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-full shadow-md \".concat(isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"),\n          onClick: () => setIsSidebarOpen(prev => !prev),\n          children: /*#__PURE__*/_jsxDEV(Menu, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 901,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: (isSidebarOpen || window.innerWidth > 1024) && /*#__PURE__*/_jsxDEV(ExamSidebar, {\n          isDarkMode: isDarkMode,\n          setIsDarkMode: setIsDarkMode,\n          fontSize: fontSize,\n          handleFontSizeChange: handleFontSizeChange,\n          imageSize: imageSize,\n          handleImageSizeChange: handleImageSizeChange,\n          questionTN: questionTN,\n          questionDS: questionDS,\n          questionTLN: questionTLN,\n          scrollToQuestion: navigateToQuestion,\n          selectedQuestion: selectedQuestion,\n          markedQuestions: markedQuestions,\n          toggleMarkQuestion: toggleMarkQuestion,\n          handleAutoSubmit: handleAutoSubmit,\n          loadingSubmit: loadingSubmit,\n          loadingLoadExam: loadingJoin,\n          exam: exam,\n          remainingTime: remainingTime,\n          formatTime: formatTime,\n          questions: questions,\n          savingQuestions: savingQuestions,\n          singleQuestionMode: ((_examContentRef$curre = examContentRef.current) === null || _examContentRef$curre === void 0 ? void 0 : _examContentRef$curre.isSingleQuestionMode()) || false,\n          setSingleQuestionMode: value => {\n            if (examContentRef.current) {\n              // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\n              examContentRef.current.setSingleQuestionMode(value);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 911,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 862,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(ExamRegulationModal, {\n        onClose: () => {\n          // Sử dụng API để leave exam nếu có attemptId\n          if (attemptId) {\n            dispatch(leaveExam({\n              examId,\n              attemptId\n            }));\n          }\n          navigate(\"/practice/exam/\".concat(examId));\n        },\n        isOpen: !isAgree,\n        onStartExam: handleFullScreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 949,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 948,\n      columnNumber: 17\n    }, this), (exam === null || exam === void 0 ? void 0 : exam.testDuration) && isAgree && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-2 rounded-md left-2 px-4 py-2\\n                    \".concat(isTimeBlinking ? 'bg-red-600 animate-pulse' : 'bg-slate-700 bg-opacity-80', \"\\n                    text-white z-50 transition-colors duration-300\"),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-bold\",\n          children: [formatTime(remainingTime), \" ph\\xFAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 964,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 859,\n    columnNumber: 9\n  }, this);\n};\n_s(DoExamPage, \"soIuFzATx5kDz2IQ/SRKxCZaT0s=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DoExamPage;\nexport default DoExamPage;\nvar _c;\n$RefreshReg$(_c, \"DoExamPage\");", "map": {"version": 3, "names": ["HeaderDoExamPage", "useDispatch", "useSelector", "useState", "useEffect", "useRef", "useCallback", "debounce", "fetchPublicQuestionsByExamId", "fetchPublicExamById", "useParams", "setErrorMessage", "setSuccessMessage", "useNavigate", "fetchAnswersByAttempt", "setAnswers", "ExamRegulationModal", "AnimatePresence", "<PERSON><PERSON>", "ExamSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestFullscreen", "exitFullscreen", "isFullscreen", "setRemainingTime", "summitExam", "setSaveQuestions", "setErrorQuestions", "getRemainingTime", "logUserActivity", "submitAnswerWithAttempt", "leaveExam", "joinExam", "jsxDEV", "_jsxDEV", "DoExamPage", "_s", "_examContentRef$curre", "examId", "dispatch", "navigate", "exam", "state", "exams", "questions", "answers", "fontSize", "setFontSize", "imageSize", "setImageSize", "questionRefs", "isAgree", "setIsAgree", "attemptId", "setAttemptId", "attemptRef", "isSidebarOpen", "setIsSidebarOpen", "flag", "setFlag", "startTime1", "setStartTime1", "hasSubmittedRef", "examRef", "examContentRef", "current", "acceptDoExam", "concat", "user", "auth", "remainingTime", "saveQuestions", "errorQuestions", "loadingJoin", "doExam", "markedQuestions", "setMarkedQuestions", "Set", "timeWarningShown", "setTimeWarningShown", "fiveMinutes", "oneMinute", "isTimeBlinking", "setIsTimeBlinking", "prefixStatementTN", "prefixStatementDS", "selectedQuestion", "setSelectedQuestion", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "loadingSubmit", "setLoadingSubmit", "isTimeUp", "setIsTimeUp", "questionTN", "setQuestionTN", "savingQuestions", "setSavingQuestions", "questionDS", "setQuestionDS", "questionTLN", "setQuestionTLN", "answerTN", "setAnswerTN", "answerTLN", "setAnswerTLN", "dsAnswers", "setDsAnswers", "document", "addEventListener", "e", "preventDefault", "addQuestion", "questionId", "includes", "removeErrorQuestion", "addErrorQuestion", "removeQuestion", "filter", "id", "toggleMarkQuestion", "prev", "newSet", "has", "delete", "add", "handleExitFullscreen", "err", "console", "warn", "handleFontSizeChange", "Number", "target", "value", "handleImageSizeChange", "formatTime", "seconds", "min", "String", "Math", "floor", "padStart", "sec", "handleFullScreen", "result", "unwrap", "startTime", "log", "isCheatingCheckEnabled", "success", "error", "alert", "message", "testDuration", "start", "Date", "now", "elapsedSeconds", "totalSeconds", "remaining", "max", "then", "_result$payload", "_result$payload$data", "payload", "data", "undefined", "catch", "handleAutoSubmit", "safeAttemptId", "currentExam", "seeCorrectAnswer", "examNull", "cantSeeAnswer", "setTimeout", "navigateToQuestion", "isSingleQuestionMode", "goToQuestionById", "element", "querySelector", "offset", "y", "getBoundingClientRect", "top", "window", "scrollY", "scrollTo", "behavior", "refElement", "submitAnswerTNDebounced", "type", "endsWith", "submitAnswerDSDebounced", "submitAnswerTLNDebounced", "cancel", "handleSelectAnswerTN", "statementId", "newAnswer", "answerContent", "typeOfQuestion", "handleSelectAnswerDS", "<PERSON><PERSON><PERSON><PERSON>", "currentAnswers", "existing", "find", "ans", "answer", "updatedAnswers", "map", "push", "stringify", "handleSelectAnswerTLN", "trim", "formattedAnswer", "replace", "questionsToMarkAsSaved", "size", "for<PERSON>ach", "clear", "frameId", "requestAnimationFrame", "questionIds", "cancelAnimationFrame", "isTNSelected", "isSelected", "some", "isDSChecked", "bool", "_dsAnswers$questionId", "_dsAnswers$questionId2", "a", "length", "getTLNDefaultValue", "_matched$answerConten", "matched", "content", "question", "Array", "isArray", "tn", "tln", "dsMap", "parsed", "checkTimeWarnings", "time", "syncTimeInterval", "setInterval", "_result$payload2", "_result$payload2$data", "interval", "clearInterval", "recentLogs", "logOnce", "key", "activityType", "details", "name", "lastName", "firstName", "handleFullscreenChange", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "studentId", "code", "action", "detail", "reason", "handleVisibilityChange", "visibilityState", "handleCopy", "handleSuspiciousKey", "<PERSON><PERSON><PERSON><PERSON>", "combo", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "removeEventListener", "setItem", "handleKeyDown", "allQuestions", "currentIndex", "findIndex", "q", "prevQuestionId", "nextQuestionId", "className", "children", "nameExam", "onExitFullscreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "loading1", "handlers", "setQuestionRef", "el", "settings", "initialSingleMode", "onClick", "innerWidth", "scrollToQuestion", "loadingLoadExam", "singleQuestionMode", "setSingleQuestionMode", "onClose", "isOpen", "onStartExam", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/DoExamPage.jsx"], "sourcesContent": ["import HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { debounce } from \"lodash\";\r\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\r\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\r\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\r\nimport { AnimatePresence } from \"framer-motion\";\r\nimport { Menu } from \"lucide-react\";\r\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\r\nimport ExamContent from \"../../../components/questions/ExamContent\";\r\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\r\nimport {\r\n    setRemainingTime,\r\n    summitExam,\r\n    setSaveQuestions,\r\n    setErrorQuestions,\r\n    getRemainingTime,\r\n    logUserActivity,\r\n    submitAnswerWithAttempt,\r\n    leaveExam,\r\n    joinExam,\r\n} from \"../../../features/doExam/doExamSlice\";\r\n\r\nconst DoExamPage = () => {\r\n    const { examId } = useParams();\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { exam } = useSelector(state => state.exams);\r\n    const { questions } = useSelector(state => state.questions);\r\n    const { answers } = useSelector(state => state.answers);\r\n    const [fontSize, setFontSize] = useState(14); // 14px mặc định\r\n    const [imageSize, setImageSize] = useState(12); // đơn vị: rem\r\n    const questionRefs = useRef([]);\r\n    const [isAgree, setIsAgree] = useState(false);\r\n    const [attemptId, setAttemptId] = useState(null);\r\n    const attemptRef = useRef(null);\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const [flag, setFlag] = useState(false);\r\n    const [startTime1, setStartTime1] = useState(null);\r\n    const hasSubmittedRef = useRef(false);\r\n    const examRef = useRef(null);\r\n    const examContentRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        examRef.current = exam;\r\n        if (exam?.acceptDoExam === false) {\r\n            navigate(`/practice/exam/${examId}`)\r\n        }\r\n    }, [exam]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchPublicExamById(examId));\r\n        }\r\n    }, [dispatch, examId]);\r\n\r\n\r\n    const { user } = useSelector((state) => state.auth);\r\n    const { remainingTime, saveQuestions, errorQuestions, loadingJoin } = useSelector((state) => state.doExam);\r\n\r\n\r\n    const [markedQuestions, setMarkedQuestions] = useState(new Set());\r\n    const [timeWarningShown, setTimeWarningShown] = useState({\r\n        fiveMinutes: false,\r\n        oneMinute: false\r\n    });\r\n    const [isTimeBlinking, setIsTimeBlinking] = useState(false);\r\n\r\n    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [isDarkMode, setIsDarkMode] = useState(() => {\r\n        const saved = localStorage.getItem(\"isDarkMode\");\r\n        return saved ? JSON.parse(saved) : false;\r\n    });\r\n\r\n    const [loadingSubmit, setLoadingSubmit] = useState(false);\r\n    const [isTimeUp, setIsTimeUp] = useState(false);\r\n\r\n    const [questionTN, setQuestionTN] = useState([]);\r\n    const [savingQuestions, setSavingQuestions] = useState(new Set()); // Track questions being saved\r\n    const [questionDS, setQuestionDS] = useState([]);\r\n    const [questionTLN, setQuestionTLN] = useState([]);\r\n\r\n    const [answerTN, setAnswerTN] = useState([]);\r\n    const [answerTLN, setAnswerTLN] = useState([]);\r\n    const [dsAnswers, setDsAnswers] = useState({});\r\n\r\n    document.addEventListener(\"copy\", (e) => {\r\n        e.preventDefault();\r\n    });\r\n\r\n    const addQuestion = (questionId) => {\r\n        if (!saveQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        removeErrorQuestion(questionId);\r\n    };\r\n\r\n    const addErrorQuestion = (questionId) => {\r\n        if (!errorQuestions.includes(questionId)) {\r\n            dispatch(setErrorQuestions([...errorQuestions, questionId]));\r\n        }\r\n        removeQuestion(questionId);\r\n    };\r\n\r\n    const removeQuestion = (questionId) => {\r\n        dispatch(setSaveQuestions(saveQuestions.filter(id => id !== questionId)));\r\n    };\r\n\r\n    const removeErrorQuestion = (questionId) => {\r\n        dispatch(setErrorQuestions(errorQuestions.filter(id => id !== questionId)));\r\n    };\r\n\r\n    // Hàm đánh dấu câu hỏi để xem lại sau\r\n    const toggleMarkQuestion = (questionId) => {\r\n        setMarkedQuestions(prev => {\r\n            const newSet = new Set(prev);\r\n            if (newSet.has(questionId)) {\r\n                newSet.delete(questionId);\r\n            } else {\r\n                newSet.add(questionId);\r\n            }\r\n            return newSet;\r\n        });\r\n    };\r\n\r\n\r\n    const handleExitFullscreen = () => {\r\n        try {\r\n            exitFullscreen();\r\n        } catch (err) {\r\n            // Chỉ ghi log lỗi, không bắt lỗi\r\n            console.warn(\"Không thể thoát fullscreen:\", err);\r\n        }\r\n    };\r\n\r\n    const handleFontSizeChange = (e) => {\r\n        setFontSize(Number(e.target.value));\r\n    };\r\n\r\n    const handleImageSizeChange = (e) => {\r\n        setImageSize(Number(e.target.value));\r\n    };\r\n\r\n    const formatTime = (seconds) => {\r\n        const min = String(Math.floor(seconds / 60)).padStart(2, '0');\r\n        const sec = String(seconds % 60).padStart(2, '0');\r\n        return `${min}:${sec}`;\r\n    };\r\n\r\n    const handleFullScreen = async () => {\r\n        try {\r\n            // Sử dụng joinExam action thay vì fetch\r\n            const result = await dispatch(joinExam(examId)).unwrap();\r\n\r\n            // Xử lý khi join exam thành công\r\n            const { attemptId, startTime } = result;\r\n            console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\r\n\r\n            setIsAgree(true);\r\n            attemptRef.current = attemptId;\r\n            setAttemptId(attemptId);\r\n\r\n            if (examId) {\r\n                dispatch(fetchPublicQuestionsByExamId(examId));\r\n            }\r\n            setStartTime1(startTime);\r\n\r\n            if (!exam?.isCheatingCheckEnabled) {\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const success = await requestFullscreen();\r\n                if (!success) {\r\n                    console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\r\n                }\r\n            } catch (err) {\r\n                console.error(\"❌ Lỗi khi bật fullscreen:\", err);\r\n                alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi tham gia bài thi:\", error);\r\n            dispatch(setErrorMessage(\"Lỗi: \" + error.message));\r\n            navigate(`/practice/exam/${examId}`);\r\n        }\r\n    };\r\n\r\n    // Removed socket-based exam_started listener - now handled in handleFullScreen\r\n\r\n    useEffect(() => {\r\n        if (exam?.testDuration && startTime1) {\r\n            const start = new Date(startTime1);\r\n            const now = new Date();\r\n            const elapsedSeconds = Math.floor((now - start) / 1000);\r\n            const totalSeconds = exam.testDuration * 60;\r\n            const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\r\n            dispatch(setRemainingTime(remaining));\r\n\r\n            // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\r\n            if (attemptId) {\r\n                dispatch(getRemainingTime({ examId, attemptId }))\r\n                    .then((result) => {\r\n                        if (result.payload?.data?.remainingTime !== undefined) {\r\n                            dispatch(setRemainingTime(result.payload.data.remainingTime));\r\n                        }\r\n                    })\r\n                    .catch((error) => {\r\n                        console.error(\"Lỗi khi lấy thời gian từ server:\", error);\r\n                    });\r\n            }\r\n        }\r\n    }, [startTime1, exam, attemptId, examId, dispatch]);\r\n\r\n    useEffect(() => {\r\n        if (flag) return\r\n        if (!remainingTime) setFlag(true)\r\n    }, [remainingTime])\r\n\r\n    const handleAutoSubmit = async () => {\r\n        if (hasSubmittedRef.current) {\r\n            console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\r\n            return;\r\n        }\r\n        hasSubmittedRef.current = true; // Đánh dấu đã submit\r\n        console.log(\"Kiểm tra attemptId:\", attemptId);\r\n        if (!attemptId) {\r\n            console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\r\n            return;\r\n        }\r\n\r\n        console.log(\"Đang nộp bài với attemptId:\", attemptId);\r\n        dispatch(setSaveQuestions([]));\r\n        setLoadingSubmit(true);\r\n\r\n        try {\r\n            // Sử dụng API thay vì socket để nộp bài\r\n            const result = await dispatch(summitExam(attemptId)).unwrap();\r\n            console.log(\"Nộp bài thành công:\", result);\r\n\r\n            // Xử lý khi nộp bài thành công\r\n            dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\r\n\r\n            // Thoát fullscreen mà không bắt lỗi\r\n            try {\r\n                exitFullscreen();\r\n            } catch (err) {\r\n                // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\r\n                console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\r\n            }\r\n\r\n            const safeAttemptId = attemptRef.current;\r\n            const currentExam = examRef.current;\r\n\r\n            if (!safeAttemptId) {\r\n                console.error(\"Không có attemptId khi navigate!\");\r\n                return;\r\n            }\r\n\r\n            // Log để debug\r\n            console.log(\"Current exam state:\", currentExam);\r\n            console.log(\"Attempt ID:\", safeAttemptId);\r\n\r\n            if (!currentExam || !currentExam.seeCorrectAnswer) {\r\n                console.log(\"Chuyển về trang danh sách do:\", {\r\n                    examNull: !currentExam,\r\n                    cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\r\n                });\r\n                navigate(`/practice/exam/${examId}`);\r\n                return;\r\n            }\r\n\r\n            navigate(`/practice/exam/attempt/${safeAttemptId}/score`);\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi nộp bài:\", error);\r\n            setLoadingSubmit(false);\r\n            dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\r\n            hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\r\n\r\n            // Thử nộp lại sau 3 giây nếu lỗi xảy ra\r\n            setTimeout(() => {\r\n                if (!loadingSubmit && attemptRef.current) {\r\n                    console.log(\"Thử nộp bài lại sau lỗi...\");\r\n                    handleAutoSubmit();\r\n                }\r\n            }, 5000);\r\n        }\r\n    };\r\n\r\n    // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\r\n    const navigateToQuestion = useCallback((questionId) => {\r\n        setSelectedQuestion(questionId);\r\n\r\n        // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\r\n        if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\r\n            // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\r\n            examContentRef.current.goToQuestionById(questionId);\r\n        } else {\r\n            // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\r\n            // Tìm phần tử câu hỏi bằng querySelector\r\n            setTimeout(() => {\r\n                // Thử tìm phần tử bằng data-question-id\r\n                const element = document.querySelector(`[data-question-id=\"${questionId}\"]`);\r\n\r\n                if (element) {\r\n                    const offset = 80; // chiều cao của header sticky\r\n                    const y = element.getBoundingClientRect().top + window.scrollY - offset;\r\n                    window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                } else {\r\n                    // Fallback: Sử dụng refs\r\n                    const refElement = questionRefs.current[questionId];\r\n\r\n                    if (refElement) {\r\n                        const offset = 80; // chiều cao của header sticky\r\n                        const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\r\n                        window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                    }\r\n                }\r\n            }, 0);\r\n        }\r\n    }, [questionRefs, examContentRef]);\r\n\r\n    // navigateToQuestion is used directly in components\r\n\r\n    // Create debounced submit functions with different delays for different question types\r\n    const submitAnswerTNDebounced = useCallback(\r\n        debounce((payload) => {\r\n            // Add to saving state\r\n            setSavingQuestions(prev => new Set(prev).add(payload.questionId));\r\n\r\n            dispatch(submitAnswerWithAttempt(payload)).then((result) => {\r\n                // Remove from saving state\r\n                setSavingQuestions(prev => {\r\n                    const newSet = new Set(prev);\r\n                    newSet.delete(payload.questionId);\r\n                    return newSet;\r\n                });\r\n\r\n                if (result.type.endsWith('/fulfilled')) {\r\n                    console.log(\"Đã lưu câu trả lời TN thành công\");\r\n                    // API success - questionId will be added to saveQuestions by slice\r\n                    // and removed from errorQuestions automatically\r\n                } else {\r\n                    console.error(\"Lỗi khi lưu câu trả lời TN:\", result.error);\r\n                    // API failed - questionId will be added to errorQuestions by slice\r\n                    // and removed from saveQuestions automatically\r\n                }\r\n            });\r\n        }, 500), // TN: 500ms - shorter delay for multiple choice\r\n        [dispatch, attemptId]\r\n    );\r\n\r\n    const submitAnswerDSDebounced = useCallback(\r\n        debounce((payload) => {\r\n            // Add to saving state\r\n            setSavingQuestions(prev => new Set(prev).add(payload.questionId));\r\n\r\n            dispatch(submitAnswerWithAttempt(payload)).then((result) => {\r\n                // Remove from saving state\r\n                setSavingQuestions(prev => {\r\n                    const newSet = new Set(prev);\r\n                    newSet.delete(payload.questionId);\r\n                    return newSet;\r\n                });\r\n\r\n                if (result.type.endsWith('/fulfilled')) {\r\n                    console.log(\"Đã lưu câu trả lời DS thành công\");\r\n                    // API success - questionId will be added to saveQuestions by slice\r\n                    // and removed from errorQuestions automatically\r\n                } else {\r\n                    console.error(\"Lỗi khi lưu câu trả lời DS:\", result.error);\r\n                    // API failed - questionId will be added to errorQuestions by slice\r\n                    // and removed from saveQuestions automatically\r\n                }\r\n            });\r\n        }, 800), // DS: 800ms - medium delay for true/false\r\n        [dispatch, attemptId]\r\n    );\r\n\r\n    const submitAnswerTLNDebounced = useCallback(\r\n        debounce((payload) => {\r\n            // Add to saving state\r\n            setSavingQuestions(prev => new Set(prev).add(payload.questionId));\r\n\r\n            dispatch(submitAnswerWithAttempt(payload)).then((result) => {\r\n                // Remove from saving state\r\n                setSavingQuestions(prev => {\r\n                    const newSet = new Set(prev);\r\n                    newSet.delete(payload.questionId);\r\n                    return newSet;\r\n                });\r\n\r\n                if (result.type.endsWith('/fulfilled')) {\r\n                    console.log(\"Đã lưu câu trả lời TLN thành công\");\r\n                    // API success - questionId will be added to saveQuestions by slice\r\n                    // and removed from errorQuestions automatically\r\n                } else {\r\n                    console.error(\"Lỗi khi lưu câu trả lời TLN:\", result.error);\r\n                    // API failed - questionId will be added to errorQuestions by slice\r\n                    // and removed from saveQuestions automatically\r\n                }\r\n            });\r\n        }, 1500), // TLN: 1500ms - longer delay for text input\r\n        [dispatch, attemptId]\r\n    );\r\n\r\n    // Cleanup debounced functions on unmount\r\n    useEffect(() => {\r\n        return () => {\r\n            submitAnswerTNDebounced.cancel();\r\n            submitAnswerDSDebounced.cancel();\r\n            submitAnswerTLNDebounced.cancel();\r\n        };\r\n    }, [submitAnswerTNDebounced, submitAnswerDSDebounced, submitAnswerTLNDebounced]);\r\n\r\n    const handleSelectAnswerTN = (questionId, statementId, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const newAnswer = {\r\n            questionId,\r\n            answerContent: statementId,\r\n            typeOfQuestion: type,\r\n        };\r\n        dispatch(setAnswers(newAnswer));\r\n\r\n        // Sử dụng debounced API call cho TN\r\n        submitAnswerTNDebounced({\r\n            questionId,\r\n            answerContent: statementId,\r\n            type,\r\n            attemptId\r\n        });\r\n    };\r\n\r\n    const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const currentAnswers = dsAnswers[questionId] || [];\r\n\r\n        const existing = currentAnswers.find(ans => ans.statementId === statementId);\r\n\r\n        // 🔁 Nếu đáp án đã giống thì không gửi lại\r\n        if (existing && existing.answer === selectedAnswer) {\r\n            return\r\n        }\r\n\r\n        const updatedAnswers = currentAnswers.map(ans =>\r\n            ans.statementId === statementId\r\n                ? { ...ans, answer: selectedAnswer }\r\n                : ans\r\n        );\r\n\r\n        // Nếu chưa có statement này\r\n        if (!existing) {\r\n            updatedAnswers.push({ statementId, answer: selectedAnswer });\r\n        }\r\n\r\n        dispatch(setAnswers({ questionId, answerContent: JSON.stringify(updatedAnswers), typeOfQuestion: \"DS\" }));\r\n\r\n        // Sử dụng debounced API call cho DS\r\n        submitAnswerDSDebounced({\r\n            questionId,\r\n            answerContent: updatedAnswers,\r\n            type: \"DS\",\r\n            attemptId\r\n        });\r\n    };\r\n\r\n\r\n    const handleSelectAnswerTLN = (questionId, answerContent, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        if (!answerContent || answerContent.trim() === \"\") {\r\n            return;\r\n        }\r\n\r\n        const formattedAnswer = answerContent.trim().replace(\",\", \".\");\r\n        dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));\r\n\r\n        // Sử dụng debounced API call cho TLN - đặc biệt hữu ích khi user gõ liên tục\r\n        submitAnswerTLNDebounced({\r\n            questionId,\r\n            answerContent: formattedAnswer,\r\n            type,\r\n            attemptId\r\n        });\r\n    }\r\n\r\n    // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\r\n    const questionsToMarkAsSaved = useRef(new Set());\r\n\r\n    // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\r\n    useEffect(() => {\r\n        if (questionsToMarkAsSaved.current.size > 0) {\r\n            questionsToMarkAsSaved.current.forEach(questionId => {\r\n                if (!saveQuestions.includes(questionId)) {\r\n                    addQuestion(questionId);\r\n                }\r\n            });\r\n            questionsToMarkAsSaved.current.clear();\r\n        }\r\n    }, [saveQuestions, addQuestion]);\r\n\r\n    // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\r\n    useEffect(() => {\r\n        // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\r\n        const frameId = requestAnimationFrame(() => {\r\n            if (questionsToMarkAsSaved.current.size > 0) {\r\n                const questionIds = [...questionsToMarkAsSaved.current];\r\n                questionsToMarkAsSaved.current.clear();\r\n\r\n                // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\r\n                questionIds.forEach(questionId => {\r\n                    if (!saveQuestions.includes(questionId)) {\r\n                        addQuestion(questionId);\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        return () => cancelAnimationFrame(frameId);\r\n    });\r\n\r\n    const isTNSelected = useCallback((questionId, statementId) => {\r\n        const isSelected = answerTN.some(\r\n            (ans) =>\r\n                ans.questionId === questionId &&\r\n                ans.answerContent &&\r\n                String(ans.answerContent) === String(statementId)\r\n        );\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.includes(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [answerTN, saveQuestions]);\r\n\r\n    const isDSChecked = useCallback((questionId, statementId, bool) => {\r\n        const isSelected = dsAnswers[questionId]?.some(\r\n            (a) => a.statementId === statementId && a.answer === bool\r\n        ) || false;\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.includes(questionId) && dsAnswers[questionId]?.length === 4) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [dsAnswers, saveQuestions]);\r\n\r\n    const getTLNDefaultValue = useCallback((questionId) => {\r\n        const matched = answerTLN.find((ans) => ans.questionId === questionId);\r\n        const content = matched?.answerContent?.replace(/^\"|\"$/g, \"\") || \"\";\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (content && !saveQuestions.includes(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return content;\r\n    }, [answerTLN, saveQuestions]);\r\n\r\n    // useEffect(() => {\r\n    //     if (examId) {\r\n    //         dispatch(fetchPublicQuestionsByExamId(examId));\r\n    //     }\r\n    // }, [dispatch, examId]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            setQuestionTN(questions.filter((question) => question.typeOfQuestion === \"TN\"));\r\n            setQuestionDS(questions.filter((question) => question.typeOfQuestion === \"DS\"));\r\n            setQuestionTLN(questions.filter((question) => question.typeOfQuestion === \"TLN\"));\r\n        }\r\n    }, [questions]);\r\n\r\n    useEffect(() => {\r\n        // Kiểm tra answers có phải là mảng không\r\n        if (!Array.isArray(answers) || answers.length === 0) return;\r\n\r\n        const tn = [];\r\n        const tln = [];\r\n        const dsMap = {};\r\n\r\n        // Sử dụng for...of thay vì forEach để tránh lỗi\r\n        for (const answer of answers) {\r\n            if (answer.typeOfQuestion === \"TN\") {\r\n                tn.push(answer);\r\n            } else if (answer.typeOfQuestion === \"TLN\") {\r\n                tln.push(answer);\r\n            } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\r\n                try {\r\n                    const parsed = JSON.parse(answer.answerContent);\r\n                    dsMap[answer.questionId] = parsed;\r\n                } catch (err) {\r\n                    console.error(\"Lỗi parse DS answerContent:\", err);\r\n                }\r\n            }\r\n        }\r\n\r\n        setAnswerTN(tn);\r\n        setAnswerTLN(tln);\r\n        setDsAnswers(dsMap);\r\n\r\n        // Note: Score calculation is now handled when submitting exam\r\n        // No need to calculate score in real-time\r\n    }, [answers]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (attemptId) {\r\n            dispatch(fetchAnswersByAttempt(attemptId));\r\n        }\r\n    }, [dispatch, attemptId]);\r\n\r\n    useEffect(() => {\r\n        if (!exam?.testDuration || remainingTime === null || !isAgree) return;\r\n\r\n        // Kiểm tra và hiển thị cảnh báo thời gian\r\n        const checkTimeWarnings = (time) => {\r\n            // Cảnh báo khi còn 5 phút\r\n            if (time === 300 && !timeWarningShown.fiveMinutes) {\r\n                setTimeWarningShown(prev => ({ ...prev, fiveMinutes: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Tắt hiệu ứng nhấp nháy sau 10 giây\r\n                setTimeout(() => {\r\n                    setIsTimeBlinking(false);\r\n                }, 10000);\r\n            }\r\n\r\n            // Cảnh báo khi còn 1 phút\r\n            if (time === 60 && !timeWarningShown.oneMinute) {\r\n                setTimeWarningShown(prev => ({ ...prev, oneMinute: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\r\n            }\r\n        };\r\n\r\n        // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API\r\n        const syncTimeInterval = setInterval(() => {\r\n            if (attemptId) {\r\n                dispatch(getRemainingTime({ examId, attemptId }))\r\n                    .then((result) => {\r\n                        if (result.payload?.data?.remainingTime !== undefined) {\r\n                            dispatch(setRemainingTime(result.payload.data.remainingTime));\r\n                        }\r\n                    })\r\n                    .catch((error) => {\r\n                        console.error(\"Lỗi khi đồng bộ thời gian:\", error);\r\n                    });\r\n            }\r\n        }, 30000); // Đồng bộ thời gian mỗi 30 giây\r\n\r\n        const interval = setInterval(() => {\r\n            dispatch(setRemainingTime((prev) => {\r\n                if (prev <= 1) { // dùng <=1 để đảm bảo không bị âm\r\n                    clearInterval(interval);\r\n                    clearInterval(syncTimeInterval);\r\n                    // Đánh dấu là đã hết thời gian\r\n                    setIsTimeUp(true);\r\n                    setIsTimeBlinking(false);\r\n                    // Thử nộp bài\r\n                    handleAutoSubmit();\r\n                    return 0;\r\n                }\r\n\r\n                // Kiểm tra cảnh báo thời gian\r\n                checkTimeWarnings(prev);\r\n\r\n                return prev - 1;\r\n            }));\r\n        }, 1000);\r\n\r\n        return () => {\r\n            clearInterval(interval);\r\n            clearInterval(syncTimeInterval);\r\n        };\r\n    }, [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId]);// Chỉ phụ thuộc vào các giá trị cần thiết\r\n\r\n    // Removed socket connection management - using API only\r\n\r\n    // frontend\r\n    useEffect(() => {\r\n        if (!attemptId || !user?.id || !examId || attemptId === null || attemptId === undefined) return;\r\n        if (!exam?.isCheatingCheckEnabled) return;\r\n        console.log(\"Đã bật theo dõi hành vi gian lận\");\r\n\r\n\r\n        const recentLogs = new Set(); // chống log lặp\r\n        const logOnce = (key, payload) => {\r\n            if (!exam?.isCheatingCheckEnabled || recentLogs.has(key)) return;\r\n\r\n            recentLogs.add(key);\r\n\r\n            // Sử dụng API thay vì socket\r\n            dispatch(logUserActivity({\r\n                examId,\r\n                attemptId,\r\n                activityType: payload.type || 'user_activity',\r\n                details: {\r\n                    ...payload,\r\n                    name: user.lastName + \" \" + user.firstName\r\n                }\r\n            }));\r\n\r\n            setTimeout(() => recentLogs.delete(key), 5000);\r\n        };\r\n\r\n        // 📌 Thoát fullscreen\r\n        const handleFullscreenChange = () => {\r\n            if (!document.fullscreenElement &&\r\n                !document.webkitFullscreenElement &&\r\n                !document.mozFullScreenElement &&\r\n                !document.msFullscreenElement) {\r\n                logOnce(\"exit_fullscreen\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"EF\",\r\n                    action: \"exit_fullscreen\",\r\n                    detail: JSON.stringify({ reason: \"User exited fullscreen mode\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\r\n        const handleVisibilityChange = () => {\r\n            if (document.visibilityState === \"hidden\") {\r\n                logOnce(\"tab_blur\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"TB\",\r\n                    action: \"tab_blur\",\r\n                    detail: JSON.stringify({ message: \"User switched tab or minimized window\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Copy nội dung\r\n        const handleCopy = () => {\r\n            logOnce(\"copy_detected\", {\r\n                studentId: user.id,\r\n                attemptId,\r\n                examId,\r\n                code: \"COP\",\r\n                action: \"copy_detected\",\r\n                detail: JSON.stringify({ message: \"User copied content\" }),\r\n            });\r\n        };\r\n\r\n        // 📌 Phím đáng ngờ\r\n        const handleSuspiciousKey = (e) => {\r\n            const suspiciousKeys = [\r\n                \"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"\r\n            ];\r\n            const combo = `${e.ctrlKey ? \"Ctrl+\" : \"\"}${e.shiftKey ? \"Shift+\" : \"\"}${e.altKey ? \"Alt+\" : \"\"}${e.metaKey ? \"Meta+\" : \"\"}${e.key}`;\r\n\r\n            if (\r\n                suspiciousKeys.includes(e.key) ||\r\n                combo === \"Ctrl+Shift+I\" ||\r\n                combo === \"Ctrl+Shift+C\"\r\n            ) {\r\n                logOnce(`key_${combo}`, {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"SK\",\r\n                    action: \"suspicious_key\",\r\n                    detail: JSON.stringify({ key: e.key, code: e.code, combo }),\r\n                });\r\n            }\r\n        };\r\n\r\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n        document.addEventListener(\"copy\", handleCopy);\r\n        document.addEventListener(\"keydown\", handleSuspiciousKey);\r\n\r\n        return () => {\r\n            document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n            document.removeEventListener(\"copy\", handleCopy);\r\n            document.removeEventListener(\"keydown\", handleSuspiciousKey);\r\n        };\r\n    }, [user.id, examId, attemptId]);\r\n\r\n\r\n    useEffect(() => {\r\n        // Removed all socket event listeners - using API responses instead\r\n        // Answer save/error status is now handled in submitAnswerWithAttempt action responses\r\n        // Timer updates are handled via getRemainingTime API calls\r\n        // Auto-submit is handled via client-side timer logic\r\n    }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\r\n\r\n    useEffect(() => {\r\n        localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\r\n    }, [isDarkMode]);\r\n\r\n    // Hàm xử lý chuyển đổi câu hỏi\r\n    const handleKeyDown = useCallback((e) => {\r\n        // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\r\n        if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\r\n            // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\r\n            e.preventDefault();\r\n\r\n            // Nếu không có câu hỏi, thoát khỏi hàm\r\n            if (!questions || questions.length === 0) return;\r\n\r\n            const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\r\n            const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\r\n\r\n            if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\r\n                const prevQuestionId = allQuestions[currentIndex - 1].id;\r\n                console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\r\n                navigateToQuestion(prevQuestionId);\r\n            } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\r\n                const nextQuestionId = allQuestions[currentIndex + 1].id;\r\n                console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\r\n                navigateToQuestion(nextQuestionId);\r\n            }\r\n        }\r\n    }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\r\n    // Lắng nghe sự kiện bàn phím\r\n    useEffect(() => {\r\n        document.addEventListener(\"keydown\", handleKeyDown);\r\n        return () => {\r\n            document.removeEventListener(\"keydown\", handleKeyDown);\r\n        };\r\n    }, [handleKeyDown]);\r\n\r\n    return (\r\n        <div className={`flex flex-col h-full ${isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'}`}>\r\n            <HeaderDoExamPage nameExam={exam?.name} onExitFullscreen={handleExitFullscreen} isDarkMode={!isDarkMode} />\r\n            {isAgree ? (\r\n                <div className=\"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\">\r\n                    {/* Main Content */}\r\n                    <ExamContent\r\n                        ref={examContentRef}\r\n                        loading1={loadingJoin}\r\n                        isDarkMode={isDarkMode}\r\n                        questionTN={questionTN}\r\n                        questionDS={questionDS}\r\n                        questionTLN={questionTLN}\r\n                        handlers={{\r\n                            handleSelectAnswerTN,\r\n                            handleSelectAnswerDS,\r\n                            handleSelectAnswerTLN,\r\n                            isTNSelected,\r\n                            isDSChecked,\r\n                            getTLNDefaultValue,\r\n                            setQuestionRef: (id, el) => (questionRefs.current[id] = el),\r\n                            setSelectedQuestion: (id) => setSelectedQuestion(id)\r\n                        }}\r\n                        settings={{\r\n                            selectedQuestion,\r\n                            isDarkMode,\r\n                            fontSize,\r\n                            imageSize,\r\n                            prefixStatementTN,\r\n                            prefixStatementDS,\r\n                            isTimeUp,\r\n                            markedQuestions,\r\n                            toggleMarkQuestion\r\n                        }}\r\n                        isTimeUp={isTimeUp}\r\n                        // Để undefined để component tự quyết định dựa trên thiết bị\r\n                        initialSingleMode={undefined}\r\n                        handleAutoSubmit={handleAutoSubmit}\r\n                        loadingSubmit={loadingSubmit}\r\n                    />\r\n\r\n\r\n                    {/* Button toggle cho mobile */}\r\n                    <div className=\"fixed bottom-4 right-4 z-50 lg:hidden\">\r\n                        <button\r\n                            className={`p-2 rounded-full shadow-md ${isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"}`}\r\n                            onClick={() => setIsSidebarOpen(prev => !prev)}\r\n                        >\r\n                            <Menu />\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Sidebar chính */}\r\n                    <AnimatePresence>\r\n                        {(isSidebarOpen || window.innerWidth > 1024) && (\r\n                            <ExamSidebar\r\n                                isDarkMode={isDarkMode}\r\n                                setIsDarkMode={setIsDarkMode}\r\n                                fontSize={fontSize}\r\n                                handleFontSizeChange={handleFontSizeChange}\r\n                                imageSize={imageSize}\r\n                                handleImageSizeChange={handleImageSizeChange}\r\n                                questionTN={questionTN}\r\n                                questionDS={questionDS}\r\n                                questionTLN={questionTLN}\r\n                                scrollToQuestion={navigateToQuestion}\r\n                                selectedQuestion={selectedQuestion}\r\n                                markedQuestions={markedQuestions}\r\n                                toggleMarkQuestion={toggleMarkQuestion}\r\n                                handleAutoSubmit={handleAutoSubmit}\r\n                                loadingSubmit={loadingSubmit}\r\n                                loadingLoadExam={loadingJoin}\r\n                                exam={exam}\r\n                                remainingTime={remainingTime}\r\n                                formatTime={formatTime}\r\n                                questions={questions}\r\n                                savingQuestions={savingQuestions}\r\n                                singleQuestionMode={examContentRef.current?.isSingleQuestionMode() || false}\r\n                                setSingleQuestionMode={(value) => {\r\n                                    if (examContentRef.current) {\r\n                                        // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\r\n                                        examContentRef.current.setSingleQuestionMode(value);\r\n                                    }\r\n                                }}\r\n                            />\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                </div>\r\n            ) : (\r\n                <div className=\"flex items-center justify-center\">\r\n                    <ExamRegulationModal\r\n                        onClose={() => {\r\n                            // Sử dụng API để leave exam nếu có attemptId\r\n                            if (attemptId) {\r\n                                dispatch(leaveExam({ examId, attemptId }));\r\n                            }\r\n                            navigate(`/practice/exam/${examId}`);\r\n                        }}\r\n                        isOpen={!isAgree}\r\n                        onStartExam={handleFullScreen}\r\n                    />\r\n                </div>\r\n            )}\r\n\r\n            {exam?.testDuration && isAgree && (\r\n                <div className={`fixed bottom-2 rounded-md left-2 px-4 py-2\r\n                    ${isTimeBlinking\r\n                        ? 'bg-red-600 animate-pulse'\r\n                        : 'bg-slate-700 bg-opacity-80'}\r\n                    text-white z-50 transition-colors duration-300`}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <div className=\"text-sm font-bold\">{formatTime(remainingTime)} phút</div>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DoExamPage;\r\n"], "mappings": ";;AAAA,OAAOA,gBAAgB,MAAM,6CAA6C;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,QAAQ,QAAQ,QAAQ;AACjC,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,uCAAuC;AAC1F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,sCAAsC;AACxF,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,WAAW,MAAM,2CAA2C;AACnE,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gCAAgC;AAChG,SACIC,gBAAgB,EAChBC,UAAU,EACVC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,uBAAuB,EACvBC,SAAS,EACTC,QAAQ,QACL,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAK,CAAC,GAAGvC,WAAW,CAACwC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAClD,MAAM;IAAEC;EAAU,CAAC,GAAG1C,WAAW,CAACwC,KAAK,IAAIA,KAAK,CAACE,SAAS,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAG3C,WAAW,CAACwC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;EACvD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM+C,YAAY,GAAG7C,MAAM,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMoD,UAAU,GAAGlD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuD,IAAI,EAAEC,OAAO,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM2D,eAAe,GAAGzD,MAAM,CAAC,KAAK,CAAC;EACrC,MAAM0D,OAAO,GAAG1D,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM2D,cAAc,GAAG3D,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACZ2D,OAAO,CAACE,OAAO,GAAGxB,IAAI;IACtB,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,YAAY,MAAK,KAAK,EAAE;MAC9B1B,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;EAEVrC,SAAS,CAAC,MAAM;IACZ,IAAIkC,MAAM,EAAE;MACRC,QAAQ,CAAC9B,mBAAmB,CAAC6B,MAAM,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,CAACC,QAAQ,EAAED,MAAM,CAAC,CAAC;EAGtB,MAAM;IAAE8B;EAAK,CAAC,GAAGlE,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAAC2B,IAAI,CAAC;EACnD,MAAM;IAAEC,aAAa;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGvE,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAACgC,MAAM,CAAC;EAG1G,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,IAAI0E,GAAG,CAAC,CAAC,CAAC;EACjE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5E,QAAQ,CAAC;IACrD6E,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMiF,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtF,MAAMC,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEtF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,MAAM;IAC/C,MAAMuF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC5C,CAAC,CAAC;EAEF,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkG,eAAe,EAAEC,kBAAkB,CAAC,GAAGnG,QAAQ,CAAC,IAAI0E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsG,WAAW,EAAEC,cAAc,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACwG,QAAQ,EAAEC,WAAW,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0G,SAAS,EAAEC,YAAY,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4G,SAAS,EAAEC,YAAY,CAAC,GAAG7G,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C8G,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAGC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAIC,UAAU,IAAK;IAChC,IAAI,CAAC/C,aAAa,CAACgD,QAAQ,CAACD,UAAU,CAAC,EAAE;MACrC/E,QAAQ,CAACb,gBAAgB,CAAC,CAAC,GAAG6C,aAAa,EAAE+C,UAAU,CAAC,CAAC,CAAC;IAC9D;IACAE,mBAAmB,CAACF,UAAU,CAAC;EACnC,CAAC;EAED,MAAMG,gBAAgB,GAAIH,UAAU,IAAK;IACrC,IAAI,CAAC9C,cAAc,CAAC+C,QAAQ,CAACD,UAAU,CAAC,EAAE;MACtC/E,QAAQ,CAACZ,iBAAiB,CAAC,CAAC,GAAG6C,cAAc,EAAE8C,UAAU,CAAC,CAAC,CAAC;IAChE;IACAI,cAAc,CAACJ,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMI,cAAc,GAAIJ,UAAU,IAAK;IACnC/E,QAAQ,CAACb,gBAAgB,CAAC6C,aAAa,CAACoD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,UAAU,CAAC,CAAC,CAAC;EAC7E,CAAC;EAED,MAAME,mBAAmB,GAAIF,UAAU,IAAK;IACxC/E,QAAQ,CAACZ,iBAAiB,CAAC6C,cAAc,CAACmD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,UAAU,CAAC,CAAC,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIP,UAAU,IAAK;IACvC1C,kBAAkB,CAACkD,IAAI,IAAI;MACvB,MAAMC,MAAM,GAAG,IAAIlD,GAAG,CAACiD,IAAI,CAAC;MAC5B,IAAIC,MAAM,CAACC,GAAG,CAACV,UAAU,CAAC,EAAE;QACxBS,MAAM,CAACE,MAAM,CAACX,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHS,MAAM,CAACG,GAAG,CAACZ,UAAU,CAAC;MAC1B;MACA,OAAOS,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAGD,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACA7G,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO8G,GAAG,EAAE;MACV;MACAC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,GAAG,CAAC;IACpD;EACJ,CAAC;EAED,MAAMG,oBAAoB,GAAIpB,CAAC,IAAK;IAChCpE,WAAW,CAACyF,MAAM,CAACrB,CAAC,CAACsB,MAAM,CAACC,KAAK,CAAC,CAAC;EACvC,CAAC;EAED,MAAMC,qBAAqB,GAAIxB,CAAC,IAAK;IACjClE,YAAY,CAACuF,MAAM,CAACrB,CAAC,CAACsB,MAAM,CAACC,KAAK,CAAC,CAAC;EACxC,CAAC;EAED,MAAME,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,MAAMC,GAAG,GAAGJ,MAAM,CAACF,OAAO,GAAG,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjD,UAAA/E,MAAA,CAAU2E,GAAG,OAAA3E,MAAA,CAAIgF,GAAG;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA;MACA,MAAMC,MAAM,GAAG,MAAM9G,QAAQ,CAACP,QAAQ,CAACM,MAAM,CAAC,CAAC,CAACgH,MAAM,CAAC,CAAC;;MAExD;MACA,MAAM;QAAEjG,SAAS;QAAEkG;MAAU,CAAC,GAAGF,MAAM;MACvChB,OAAO,CAACmB,GAAG,CAAC,+CAA+C,EAAEnG,SAAS,CAAC;MAEvED,UAAU,CAAC,IAAI,CAAC;MAChBG,UAAU,CAACU,OAAO,GAAGZ,SAAS;MAC9BC,YAAY,CAACD,SAAS,CAAC;MAEvB,IAAIf,MAAM,EAAE;QACRC,QAAQ,CAAC/B,4BAA4B,CAAC8B,MAAM,CAAC,CAAC;MAClD;MACAuB,aAAa,CAAC0F,SAAS,CAAC;MAExB,IAAI,EAAC9G,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgH,sBAAsB,GAAE;QAC/B;MACJ;MAEA,IAAI;QACA,MAAMC,OAAO,GAAG,MAAMrI,iBAAiB,CAAC,CAAC;QACzC,IAAI,CAACqI,OAAO,EAAE;UACVrB,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;QACxE;MACJ,CAAC,CAAC,OAAOF,GAAG,EAAE;QACVC,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEvB,GAAG,CAAC;QAC/CwB,KAAK,CAAC,yDAAyD,CAAC;MACpE;IAEJ,CAAC,CAAC,OAAOD,KAAK,EAAE;MACZtB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDpH,QAAQ,CAAC5B,eAAe,CAAC,OAAO,GAAGgJ,KAAK,CAACE,OAAO,CAAC,CAAC;MAClDrH,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC;;EAED;;EAEAlC,SAAS,CAAC,MAAM;IACZ,IAAIqC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqH,YAAY,IAAIlG,UAAU,EAAE;MAClC,MAAMmG,KAAK,GAAG,IAAIC,IAAI,CAACpG,UAAU,CAAC;MAClC,MAAMqG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,cAAc,GAAGlB,IAAI,CAACC,KAAK,CAAC,CAACgB,GAAG,GAAGF,KAAK,IAAI,IAAI,CAAC;MACvD,MAAMI,YAAY,GAAG1H,IAAI,CAACqH,YAAY,GAAG,EAAE;MAC3C,MAAMM,SAAS,GAAGpB,IAAI,CAACqB,GAAG,CAACF,YAAY,GAAGD,cAAc,EAAE,CAAC,CAAC;MAC5D3H,QAAQ,CAACf,gBAAgB,CAAC4I,SAAS,CAAC,CAAC;;MAErC;MACA,IAAI/G,SAAS,EAAE;QACXd,QAAQ,CAACX,gBAAgB,CAAC;UAAEU,MAAM;UAAEe;QAAU,CAAC,CAAC,CAAC,CAC5CiH,IAAI,CAAEjB,MAAM,IAAK;UAAA,IAAAkB,eAAA,EAAAC,oBAAA;UACd,IAAI,EAAAD,eAAA,GAAAlB,MAAM,CAACoB,OAAO,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBlG,aAAa,MAAKqG,SAAS,EAAE;YACnDpI,QAAQ,CAACf,gBAAgB,CAAC6H,MAAM,CAACoB,OAAO,CAACC,IAAI,CAACpG,aAAa,CAAC,CAAC;UACjE;QACJ,CAAC,CAAC,CACDsG,KAAK,CAAEjB,KAAK,IAAK;UACdtB,OAAO,CAACsB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC5D,CAAC,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAC/F,UAAU,EAAEnB,IAAI,EAAEY,SAAS,EAAEf,MAAM,EAAEC,QAAQ,CAAC,CAAC;EAEnDnC,SAAS,CAAC,MAAM;IACZ,IAAIsD,IAAI,EAAE;IACV,IAAI,CAACY,aAAa,EAAEX,OAAO,CAAC,IAAI,CAAC;EACrC,CAAC,EAAE,CAACW,aAAa,CAAC,CAAC;EAEnB,MAAMuG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI/G,eAAe,CAACG,OAAO,EAAE;MACzBoE,OAAO,CAACC,IAAI,CAAC,sCAAsC,CAAC;MACpD;IACJ;IACAxE,eAAe,CAACG,OAAO,GAAG,IAAI,CAAC,CAAC;IAChCoE,OAAO,CAACmB,GAAG,CAAC,qBAAqB,EAAEnG,SAAS,CAAC;IAC7C,IAAI,CAACA,SAAS,EAAE;MACZgF,OAAO,CAACmB,GAAG,CAAC,8EAA8E,CAAC;MAC3F;IACJ;IAEAnB,OAAO,CAACmB,GAAG,CAAC,6BAA6B,EAAEnG,SAAS,CAAC;IACrDd,QAAQ,CAACb,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC9BsE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACA;MACA,MAAMqD,MAAM,GAAG,MAAM9G,QAAQ,CAACd,UAAU,CAAC4B,SAAS,CAAC,CAAC,CAACiG,MAAM,CAAC,CAAC;MAC7DjB,OAAO,CAACmB,GAAG,CAAC,qBAAqB,EAAEH,MAAM,CAAC;;MAE1C;MACA9G,QAAQ,CAAC3B,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;;MAElD;MACA,IAAI;QACAU,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,OAAO8G,GAAG,EAAE;QACV;QACAC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAChE;MAEA,MAAM0C,aAAa,GAAGvH,UAAU,CAACU,OAAO;MACxC,MAAM8G,WAAW,GAAGhH,OAAO,CAACE,OAAO;MAEnC,IAAI,CAAC6G,aAAa,EAAE;QAChBzC,OAAO,CAACsB,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACJ;;MAEA;MACAtB,OAAO,CAACmB,GAAG,CAAC,qBAAqB,EAAEuB,WAAW,CAAC;MAC/C1C,OAAO,CAACmB,GAAG,CAAC,aAAa,EAAEsB,aAAa,CAAC;MAEzC,IAAI,CAACC,WAAW,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QAC/C3C,OAAO,CAACmB,GAAG,CAAC,+BAA+B,EAAE;UACzCyB,QAAQ,EAAE,CAACF,WAAW;UACtBG,aAAa,EAAEH,WAAW,IAAI,CAACA,WAAW,CAACC;QAC/C,CAAC,CAAC;QACFxI,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACpC;MACJ;MAEAE,QAAQ,2BAAA2B,MAAA,CAA2B2G,aAAa,WAAQ,CAAC;IAC7D,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACZtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC3D,gBAAgB,CAAC,KAAK,CAAC;MACvBzD,QAAQ,CAAC5B,eAAe,CAAC,oCAAoC,CAAC,CAAC;MAC/DmD,eAAe,CAACG,OAAO,GAAG,KAAK,CAAC,CAAC;;MAEjC;MACAkH,UAAU,CAAC,MAAM;QACb,IAAI,CAACpF,aAAa,IAAIxC,UAAU,CAACU,OAAO,EAAE;UACtCoE,OAAO,CAACmB,GAAG,CAAC,4BAA4B,CAAC;UACzCqB,gBAAgB,CAAC,CAAC;QACtB;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAG9K,WAAW,CAAEgH,UAAU,IAAK;IACnD/B,mBAAmB,CAAC+B,UAAU,CAAC;;IAE/B;IACA,IAAItD,cAAc,CAACC,OAAO,IAAID,cAAc,CAACC,OAAO,CAACoH,oBAAoB,CAAC,CAAC,EAAE;MACzE;MACArH,cAAc,CAACC,OAAO,CAACqH,gBAAgB,CAAChE,UAAU,CAAC;IACvD,CAAC,MAAM;MACH;MACA;MACA6D,UAAU,CAAC,MAAM;QACb;QACA,MAAMI,OAAO,GAAGtE,QAAQ,CAACuE,aAAa,wBAAArH,MAAA,CAAuBmD,UAAU,QAAI,CAAC;QAE5E,IAAIiE,OAAO,EAAE;UACT,MAAME,MAAM,GAAG,EAAE,CAAC,CAAC;UACnB,MAAMC,CAAC,GAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;UACvEI,MAAM,CAACE,QAAQ,CAAC;YAAEH,GAAG,EAAEF,CAAC;YAAEM,QAAQ,EAAE;UAAS,CAAC,CAAC;QACnD,CAAC,MAAM;UACH;UACA,MAAMC,UAAU,GAAG/I,YAAY,CAACe,OAAO,CAACqD,UAAU,CAAC;UAEnD,IAAI2E,UAAU,EAAE;YACZ,MAAMR,MAAM,GAAG,EAAE,CAAC,CAAC;YACnB,MAAMC,CAAC,GAAGO,UAAU,CAACN,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;YAC1EI,MAAM,CAACE,QAAQ,CAAC;cAAEH,GAAG,EAAEF,CAAC;cAAEM,QAAQ,EAAE;YAAS,CAAC,CAAC;UACnD;QACJ;MACJ,CAAC,EAAE,CAAC,CAAC;IACT;EACJ,CAAC,EAAE,CAAC9I,YAAY,EAAEc,cAAc,CAAC,CAAC;;EAElC;;EAEA;EACA,MAAMkI,uBAAuB,GAAG5L,WAAW,CACvCC,QAAQ,CAAEkK,OAAO,IAAK;IAClB;IACAnE,kBAAkB,CAACwB,IAAI,IAAI,IAAIjD,GAAG,CAACiD,IAAI,CAAC,CAACI,GAAG,CAACuC,OAAO,CAACnD,UAAU,CAAC,CAAC;IAEjE/E,QAAQ,CAACT,uBAAuB,CAAC2I,OAAO,CAAC,CAAC,CAACH,IAAI,CAAEjB,MAAM,IAAK;MACxD;MACA/C,kBAAkB,CAACwB,IAAI,IAAI;QACvB,MAAMC,MAAM,GAAG,IAAIlD,GAAG,CAACiD,IAAI,CAAC;QAC5BC,MAAM,CAACE,MAAM,CAACwC,OAAO,CAACnD,UAAU,CAAC;QACjC,OAAOS,MAAM;MACjB,CAAC,CAAC;MAEF,IAAIsB,MAAM,CAAC8C,IAAI,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC/D,OAAO,CAACmB,GAAG,CAAC,kCAAkC,CAAC;QAC/C;QACA;MACJ,CAAC,MAAM;QACHnB,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAEN,MAAM,CAACM,KAAK,CAAC;QAC1D;QACA;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,GAAG,CAAC;EAAE;EACT,CAACpH,QAAQ,EAAEc,SAAS,CACxB,CAAC;EAED,MAAMgJ,uBAAuB,GAAG/L,WAAW,CACvCC,QAAQ,CAAEkK,OAAO,IAAK;IAClB;IACAnE,kBAAkB,CAACwB,IAAI,IAAI,IAAIjD,GAAG,CAACiD,IAAI,CAAC,CAACI,GAAG,CAACuC,OAAO,CAACnD,UAAU,CAAC,CAAC;IAEjE/E,QAAQ,CAACT,uBAAuB,CAAC2I,OAAO,CAAC,CAAC,CAACH,IAAI,CAAEjB,MAAM,IAAK;MACxD;MACA/C,kBAAkB,CAACwB,IAAI,IAAI;QACvB,MAAMC,MAAM,GAAG,IAAIlD,GAAG,CAACiD,IAAI,CAAC;QAC5BC,MAAM,CAACE,MAAM,CAACwC,OAAO,CAACnD,UAAU,CAAC;QACjC,OAAOS,MAAM;MACjB,CAAC,CAAC;MAEF,IAAIsB,MAAM,CAAC8C,IAAI,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC/D,OAAO,CAACmB,GAAG,CAAC,kCAAkC,CAAC;QAC/C;QACA;MACJ,CAAC,MAAM;QACHnB,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAEN,MAAM,CAACM,KAAK,CAAC;QAC1D;QACA;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,GAAG,CAAC;EAAE;EACT,CAACpH,QAAQ,EAAEc,SAAS,CACxB,CAAC;EAED,MAAMiJ,wBAAwB,GAAGhM,WAAW,CACxCC,QAAQ,CAAEkK,OAAO,IAAK;IAClB;IACAnE,kBAAkB,CAACwB,IAAI,IAAI,IAAIjD,GAAG,CAACiD,IAAI,CAAC,CAACI,GAAG,CAACuC,OAAO,CAACnD,UAAU,CAAC,CAAC;IAEjE/E,QAAQ,CAACT,uBAAuB,CAAC2I,OAAO,CAAC,CAAC,CAACH,IAAI,CAAEjB,MAAM,IAAK;MACxD;MACA/C,kBAAkB,CAACwB,IAAI,IAAI;QACvB,MAAMC,MAAM,GAAG,IAAIlD,GAAG,CAACiD,IAAI,CAAC;QAC5BC,MAAM,CAACE,MAAM,CAACwC,OAAO,CAACnD,UAAU,CAAC;QACjC,OAAOS,MAAM;MACjB,CAAC,CAAC;MAEF,IAAIsB,MAAM,CAAC8C,IAAI,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC/D,OAAO,CAACmB,GAAG,CAAC,mCAAmC,CAAC;QAChD;QACA;MACJ,CAAC,MAAM;QACHnB,OAAO,CAACsB,KAAK,CAAC,8BAA8B,EAAEN,MAAM,CAACM,KAAK,CAAC;QAC3D;QACA;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,IAAI,CAAC;EAAE;EACV,CAACpH,QAAQ,EAAEc,SAAS,CACxB,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACZ,OAAO,MAAM;MACT8L,uBAAuB,CAACK,MAAM,CAAC,CAAC;MAChCF,uBAAuB,CAACE,MAAM,CAAC,CAAC;MAChCD,wBAAwB,CAACC,MAAM,CAAC,CAAC;IACrC,CAAC;EACL,CAAC,EAAE,CAACL,uBAAuB,EAAEG,uBAAuB,EAAEC,wBAAwB,CAAC,CAAC;EAEhF,MAAME,oBAAoB,GAAGA,CAAClF,UAAU,EAAEmF,WAAW,EAAEN,IAAI,KAAK;IAC5D;IACA,IAAIlG,QAAQ,EAAE;MACV1D,QAAQ,CAAC5B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAM+L,SAAS,GAAG;MACdpF,UAAU;MACVqF,aAAa,EAAEF,WAAW;MAC1BG,cAAc,EAAET;IACpB,CAAC;IACD5J,QAAQ,CAACxB,UAAU,CAAC2L,SAAS,CAAC,CAAC;;IAE/B;IACAR,uBAAuB,CAAC;MACpB5E,UAAU;MACVqF,aAAa,EAAEF,WAAW;MAC1BN,IAAI;MACJ9I;IACJ,CAAC,CAAC;EACN,CAAC;EAED,MAAMwJ,oBAAoB,GAAGA,CAACvF,UAAU,EAAEmF,WAAW,EAAEK,cAAc,KAAK;IACtE;IACA,IAAI7G,QAAQ,EAAE;MACV1D,QAAQ,CAAC5B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAMoM,cAAc,GAAGhG,SAAS,CAACO,UAAU,CAAC,IAAI,EAAE;IAElD,MAAM0F,QAAQ,GAAGD,cAAc,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACT,WAAW,KAAKA,WAAW,CAAC;;IAE5E;IACA,IAAIO,QAAQ,IAAIA,QAAQ,CAACG,MAAM,KAAKL,cAAc,EAAE;MAChD;IACJ;IAEA,MAAMM,cAAc,GAAGL,cAAc,CAACM,GAAG,CAACH,GAAG,IACzCA,GAAG,CAACT,WAAW,KAAKA,WAAW,GACzB;MAAE,GAAGS,GAAG;MAAEC,MAAM,EAAEL;IAAe,CAAC,GAClCI,GACV,CAAC;;IAED;IACA,IAAI,CAACF,QAAQ,EAAE;MACXI,cAAc,CAACE,IAAI,CAAC;QAAEb,WAAW;QAAEU,MAAM,EAAEL;MAAe,CAAC,CAAC;IAChE;IAEAvK,QAAQ,CAACxB,UAAU,CAAC;MAAEuG,UAAU;MAAEqF,aAAa,EAAE9G,IAAI,CAAC0H,SAAS,CAACH,cAAc,CAAC;MAAER,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC;;IAEzG;IACAP,uBAAuB,CAAC;MACpB/E,UAAU;MACVqF,aAAa,EAAES,cAAc;MAC7BjB,IAAI,EAAE,IAAI;MACV9I;IACJ,CAAC,CAAC;EACN,CAAC;EAGD,MAAMmK,qBAAqB,GAAGA,CAAClG,UAAU,EAAEqF,aAAa,EAAER,IAAI,KAAK;IAC/D;IACA,IAAIlG,QAAQ,EAAE;MACV1D,QAAQ,CAAC5B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,IAAI,CAACgM,aAAa,IAAIA,aAAa,CAACc,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/C;IACJ;IAEA,MAAMC,eAAe,GAAGf,aAAa,CAACc,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC9DpL,QAAQ,CAACxB,UAAU,CAAC;MAAEuG,UAAU;MAAEqF,aAAa;MAAEC,cAAc,EAAET;IAAK,CAAC,CAAC,CAAC;;IAEzE;IACAG,wBAAwB,CAAC;MACrBhF,UAAU;MACVqF,aAAa,EAAEe,eAAe;MAC9BvB,IAAI;MACJ9I;IACJ,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMuK,sBAAsB,GAAGvN,MAAM,CAAC,IAAIwE,GAAG,CAAC,CAAC,CAAC;;EAEhD;EACAzE,SAAS,CAAC,MAAM;IACZ,IAAIwN,sBAAsB,CAAC3J,OAAO,CAAC4J,IAAI,GAAG,CAAC,EAAE;MACzCD,sBAAsB,CAAC3J,OAAO,CAAC6J,OAAO,CAACxG,UAAU,IAAI;QACjD,IAAI,CAAC/C,aAAa,CAACgD,QAAQ,CAACD,UAAU,CAAC,EAAE;UACrCD,WAAW,CAACC,UAAU,CAAC;QAC3B;MACJ,CAAC,CAAC;MACFsG,sBAAsB,CAAC3J,OAAO,CAAC8J,KAAK,CAAC,CAAC;IAC1C;EACJ,CAAC,EAAE,CAACxJ,aAAa,EAAE8C,WAAW,CAAC,CAAC;;EAEhC;EACAjH,SAAS,CAAC,MAAM;IACZ;IACA,MAAM4N,OAAO,GAAGC,qBAAqB,CAAC,MAAM;MACxC,IAAIL,sBAAsB,CAAC3J,OAAO,CAAC4J,IAAI,GAAG,CAAC,EAAE;QACzC,MAAMK,WAAW,GAAG,CAAC,GAAGN,sBAAsB,CAAC3J,OAAO,CAAC;QACvD2J,sBAAsB,CAAC3J,OAAO,CAAC8J,KAAK,CAAC,CAAC;;QAEtC;QACAG,WAAW,CAACJ,OAAO,CAACxG,UAAU,IAAI;UAC9B,IAAI,CAAC/C,aAAa,CAACgD,QAAQ,CAACD,UAAU,CAAC,EAAE;YACrCD,WAAW,CAACC,UAAU,CAAC;UAC3B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IAEF,OAAO,MAAM6G,oBAAoB,CAACH,OAAO,CAAC;EAC9C,CAAC,CAAC;EAEF,MAAMI,YAAY,GAAG9N,WAAW,CAAC,CAACgH,UAAU,EAAEmF,WAAW,KAAK;IAC1D,MAAM4B,UAAU,GAAG1H,QAAQ,CAAC2H,IAAI,CAC3BpB,GAAG,IACAA,GAAG,CAAC5F,UAAU,KAAKA,UAAU,IAC7B4F,GAAG,CAACP,aAAa,IACjB5D,MAAM,CAACmE,GAAG,CAACP,aAAa,CAAC,KAAK5D,MAAM,CAAC0D,WAAW,CACxD,CAAC;;IAED;IACA,IAAI4B,UAAU,IAAI,CAAC9J,aAAa,CAACgD,QAAQ,CAACD,UAAU,CAAC,EAAE;MACnDsG,sBAAsB,CAAC3J,OAAO,CAACiE,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAO+G,UAAU;EACrB,CAAC,EAAE,CAAC1H,QAAQ,EAAEpC,aAAa,CAAC,CAAC;EAE7B,MAAMgK,WAAW,GAAGjO,WAAW,CAAC,CAACgH,UAAU,EAAEmF,WAAW,EAAE+B,IAAI,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC/D,MAAML,UAAU,GAAG,EAAAI,qBAAA,GAAA1H,SAAS,CAACO,UAAU,CAAC,cAAAmH,qBAAA,uBAArBA,qBAAA,CAAuBH,IAAI,CACzCK,CAAC,IAAKA,CAAC,CAAClC,WAAW,KAAKA,WAAW,IAAIkC,CAAC,CAACxB,MAAM,KAAKqB,IACzD,CAAC,KAAI,KAAK;;IAEV;IACA,IAAIH,UAAU,IAAI,CAAC9J,aAAa,CAACgD,QAAQ,CAACD,UAAU,CAAC,IAAI,EAAAoH,sBAAA,GAAA3H,SAAS,CAACO,UAAU,CAAC,cAAAoH,sBAAA,uBAArBA,sBAAA,CAAuBE,MAAM,MAAK,CAAC,EAAE;MAC1FhB,sBAAsB,CAAC3J,OAAO,CAACiE,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAO+G,UAAU;EACrB,CAAC,EAAE,CAACtH,SAAS,EAAExC,aAAa,CAAC,CAAC;EAE9B,MAAMsK,kBAAkB,GAAGvO,WAAW,CAAEgH,UAAU,IAAK;IAAA,IAAAwH,qBAAA;IACnD,MAAMC,OAAO,GAAGlI,SAAS,CAACoG,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAAC5F,UAAU,KAAKA,UAAU,CAAC;IACtE,MAAM0H,OAAO,GAAG,CAAAD,OAAO,aAAPA,OAAO,wBAAAD,qBAAA,GAAPC,OAAO,CAAEpC,aAAa,cAAAmC,qBAAA,uBAAtBA,qBAAA,CAAwBnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAI,EAAE;;IAEnE;IACA,IAAIqB,OAAO,IAAI,CAACzK,aAAa,CAACgD,QAAQ,CAACD,UAAU,CAAC,EAAE;MAChDsG,sBAAsB,CAAC3J,OAAO,CAACiE,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAO0H,OAAO;EAClB,CAAC,EAAE,CAACnI,SAAS,EAAEtC,aAAa,CAAC,CAAC;;EAE9B;EACA;EACA;EACA;EACA;;EAEAnE,SAAS,CAAC,MAAM;IACZ,IAAIwC,SAAS,EAAE;MACXwD,aAAa,CAACxD,SAAS,CAAC+E,MAAM,CAAEsH,QAAQ,IAAKA,QAAQ,CAACrC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/EpG,aAAa,CAAC5D,SAAS,CAAC+E,MAAM,CAAEsH,QAAQ,IAAKA,QAAQ,CAACrC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/ElG,cAAc,CAAC9D,SAAS,CAAC+E,MAAM,CAAEsH,QAAQ,IAAKA,QAAQ,CAACrC,cAAc,KAAK,KAAK,CAAC,CAAC;IACrF;EACJ,CAAC,EAAE,CAAChK,SAAS,CAAC,CAAC;EAEfxC,SAAS,CAAC,MAAM;IACZ;IACA,IAAI,CAAC8O,KAAK,CAACC,OAAO,CAACtM,OAAO,CAAC,IAAIA,OAAO,CAAC+L,MAAM,KAAK,CAAC,EAAE;IAErD,MAAMQ,EAAE,GAAG,EAAE;IACb,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,KAAK,MAAMnC,MAAM,IAAItK,OAAO,EAAE;MAC1B,IAAIsK,MAAM,CAACP,cAAc,KAAK,IAAI,EAAE;QAChCwC,EAAE,CAAC9B,IAAI,CAACH,MAAM,CAAC;MACnB,CAAC,MAAM,IAAIA,MAAM,CAACP,cAAc,KAAK,KAAK,EAAE;QACxCyC,GAAG,CAAC/B,IAAI,CAACH,MAAM,CAAC;MACpB,CAAC,MAAM,IAAIA,MAAM,CAACP,cAAc,KAAK,IAAI,IAAIO,MAAM,CAACR,aAAa,EAAE;QAC/D,IAAI;UACA,MAAM4C,MAAM,GAAG1J,IAAI,CAACC,KAAK,CAACqH,MAAM,CAACR,aAAa,CAAC;UAC/C2C,KAAK,CAACnC,MAAM,CAAC7F,UAAU,CAAC,GAAGiI,MAAM;QACrC,CAAC,CAAC,OAAOnH,GAAG,EAAE;UACVC,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAEvB,GAAG,CAAC;QACrD;MACJ;IACJ;IAEAxB,WAAW,CAACwI,EAAE,CAAC;IACftI,YAAY,CAACuI,GAAG,CAAC;IACjBrI,YAAY,CAACsI,KAAK,CAAC;;IAEnB;IACA;EACJ,CAAC,EAAE,CAACzM,OAAO,CAAC,CAAC;EAGbzC,SAAS,CAAC,MAAM;IACZ,IAAIiD,SAAS,EAAE;MACXd,QAAQ,CAACzB,qBAAqB,CAACuC,SAAS,CAAC,CAAC;IAC9C;EACJ,CAAC,EAAE,CAACd,QAAQ,EAAEc,SAAS,CAAC,CAAC;EAEzBjD,SAAS,CAAC,MAAM;IACZ,IAAI,EAACqC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqH,YAAY,KAAIxF,aAAa,KAAK,IAAI,IAAI,CAACnB,OAAO,EAAE;;IAE/D;IACA,MAAMqM,iBAAiB,GAAIC,IAAI,IAAK;MAChC;MACA,IAAIA,IAAI,KAAK,GAAG,IAAI,CAAC3K,gBAAgB,CAACE,WAAW,EAAE;QAC/CD,mBAAmB,CAAC+C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE9C,WAAW,EAAE;QAAK,CAAC,CAAC,CAAC;QAC7DG,iBAAiB,CAAC,IAAI,CAAC;QACvB5C,QAAQ,CAAC5B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;QACAwK,UAAU,CAAC,MAAM;UACbhG,iBAAiB,CAAC,KAAK,CAAC;QAC5B,CAAC,EAAE,KAAK,CAAC;MACb;;MAEA;MACA,IAAIsK,IAAI,KAAK,EAAE,IAAI,CAAC3K,gBAAgB,CAACG,SAAS,EAAE;QAC5CF,mBAAmB,CAAC+C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE7C,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC;QAC3DE,iBAAiB,CAAC,IAAI,CAAC;QACvB5C,QAAQ,CAAC5B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;MACJ;IACJ,CAAC;;IAED;IACA,MAAM+O,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACvC,IAAItM,SAAS,EAAE;QACXd,QAAQ,CAACX,gBAAgB,CAAC;UAAEU,MAAM;UAAEe;QAAU,CAAC,CAAC,CAAC,CAC5CiH,IAAI,CAAEjB,MAAM,IAAK;UAAA,IAAAuG,gBAAA,EAAAC,qBAAA;UACd,IAAI,EAAAD,gBAAA,GAAAvG,MAAM,CAACoB,OAAO,cAAAmF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlF,IAAI,cAAAmF,qBAAA,uBAApBA,qBAAA,CAAsBvL,aAAa,MAAKqG,SAAS,EAAE;YACnDpI,QAAQ,CAACf,gBAAgB,CAAC6H,MAAM,CAACoB,OAAO,CAACC,IAAI,CAACpG,aAAa,CAAC,CAAC;UACjE;QACJ,CAAC,CAAC,CACDsG,KAAK,CAAEjB,KAAK,IAAK;UACdtB,OAAO,CAACsB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACtD,CAAC,CAAC;MACV;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,MAAMmG,QAAQ,GAAGH,WAAW,CAAC,MAAM;MAC/BpN,QAAQ,CAACf,gBAAgB,CAAEsG,IAAI,IAAK;QAChC,IAAIA,IAAI,IAAI,CAAC,EAAE;UAAE;UACbiI,aAAa,CAACD,QAAQ,CAAC;UACvBC,aAAa,CAACL,gBAAgB,CAAC;UAC/B;UACAxJ,WAAW,CAAC,IAAI,CAAC;UACjBf,iBAAiB,CAAC,KAAK,CAAC;UACxB;UACA0F,gBAAgB,CAAC,CAAC;UAClB,OAAO,CAAC;QACZ;;QAEA;QACA2E,iBAAiB,CAAC1H,IAAI,CAAC;QAEvB,OAAOA,IAAI,GAAG,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACTiI,aAAa,CAACD,QAAQ,CAAC;MACvBC,aAAa,CAACL,gBAAgB,CAAC;IACnC,CAAC;EACL,CAAC,EAAE,CAACjN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqH,YAAY,EAAE3G,OAAO,EAAEmB,aAAa,EAAEQ,gBAAgB,EAAEvC,QAAQ,EAAEc,SAAS,EAAEf,MAAM,CAAC,CAAC,CAAC;;EAEhG;;EAEA;EACAlC,SAAS,CAAC,MAAM;IACZ,IAAI,CAACiD,SAAS,IAAI,EAACe,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwD,EAAE,KAAI,CAACtF,MAAM,IAAIe,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKsH,SAAS,EAAE;IACzF,IAAI,EAAClI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgH,sBAAsB,GAAE;IACnCpB,OAAO,CAACmB,GAAG,CAAC,kCAAkC,CAAC;IAG/C,MAAMwG,UAAU,GAAG,IAAInL,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAMoL,OAAO,GAAGA,CAACC,GAAG,EAAEzF,OAAO,KAAK;MAC9B,IAAI,EAAChI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgH,sBAAsB,KAAIuG,UAAU,CAAChI,GAAG,CAACkI,GAAG,CAAC,EAAE;MAE1DF,UAAU,CAAC9H,GAAG,CAACgI,GAAG,CAAC;;MAEnB;MACA3N,QAAQ,CAACV,eAAe,CAAC;QACrBS,MAAM;QACNe,SAAS;QACT8M,YAAY,EAAE1F,OAAO,CAAC0B,IAAI,IAAI,eAAe;QAC7CiE,OAAO,EAAE;UACL,GAAG3F,OAAO;UACV4F,IAAI,EAAEjM,IAAI,CAACkM,QAAQ,GAAG,GAAG,GAAGlM,IAAI,CAACmM;QACrC;MACJ,CAAC,CAAC,CAAC;MAEHpF,UAAU,CAAC,MAAM6E,UAAU,CAAC/H,MAAM,CAACiI,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC;;IAED;IACA,MAAMM,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAACvJ,QAAQ,CAACwJ,iBAAiB,IAC3B,CAACxJ,QAAQ,CAACyJ,uBAAuB,IACjC,CAACzJ,QAAQ,CAAC0J,oBAAoB,IAC9B,CAAC1J,QAAQ,CAAC2J,mBAAmB,EAAE;QAC/BX,OAAO,CAAC,iBAAiB,EAAE;UACvBY,SAAS,EAAEzM,IAAI,CAACwD,EAAE;UAClBvE,SAAS;UACTf,MAAM;UACNwO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,iBAAiB;UACzBC,MAAM,EAAEnL,IAAI,CAAC0H,SAAS,CAAC;YAAE0D,MAAM,EAAE;UAA8B,CAAC;QACpE,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAIjK,QAAQ,CAACkK,eAAe,KAAK,QAAQ,EAAE;QACvClB,OAAO,CAAC,UAAU,EAAE;UAChBY,SAAS,EAAEzM,IAAI,CAACwD,EAAE;UAClBvE,SAAS;UACTf,MAAM;UACNwO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,UAAU;UAClBC,MAAM,EAAEnL,IAAI,CAAC0H,SAAS,CAAC;YAAE1D,OAAO,EAAE;UAAwC,CAAC;QAC/E,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMuH,UAAU,GAAGA,CAAA,KAAM;MACrBnB,OAAO,CAAC,eAAe,EAAE;QACrBY,SAAS,EAAEzM,IAAI,CAACwD,EAAE;QAClBvE,SAAS;QACTf,MAAM;QACNwO,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAEnL,IAAI,CAAC0H,SAAS,CAAC;UAAE1D,OAAO,EAAE;QAAsB,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC;;IAED;IACA,MAAMwH,mBAAmB,GAAIlK,CAAC,IAAK;MAC/B,MAAMmK,cAAc,GAAG,CACnB,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CACjE;MACD,MAAMC,KAAK,MAAApN,MAAA,CAAMgD,CAAC,CAACqK,OAAO,GAAG,OAAO,GAAG,EAAE,EAAArN,MAAA,CAAGgD,CAAC,CAACsK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAAtN,MAAA,CAAGgD,CAAC,CAACuK,MAAM,GAAG,MAAM,GAAG,EAAE,EAAAvN,MAAA,CAAGgD,CAAC,CAACwK,OAAO,GAAG,OAAO,GAAG,EAAE,EAAAxN,MAAA,CAAGgD,CAAC,CAAC+I,GAAG,CAAE;MAEpI,IACIoB,cAAc,CAAC/J,QAAQ,CAACJ,CAAC,CAAC+I,GAAG,CAAC,IAC9BqB,KAAK,KAAK,cAAc,IACxBA,KAAK,KAAK,cAAc,EAC1B;QACEtB,OAAO,QAAA9L,MAAA,CAAQoN,KAAK,GAAI;UACpBV,SAAS,EAAEzM,IAAI,CAACwD,EAAE;UAClBvE,SAAS;UACTf,MAAM;UACNwO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,gBAAgB;UACxBC,MAAM,EAAEnL,IAAI,CAAC0H,SAAS,CAAC;YAAE2C,GAAG,EAAE/I,CAAC,CAAC+I,GAAG;YAAEY,IAAI,EAAE3J,CAAC,CAAC2J,IAAI;YAAES;UAAM,CAAC;QAC9D,CAAC,CAAC;MACN;IACJ,CAAC;IAEDtK,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEsJ,sBAAsB,CAAC;IACrEvJ,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEgK,sBAAsB,CAAC;IACrEjK,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEkK,UAAU,CAAC;IAC7CnK,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEmK,mBAAmB,CAAC;IAEzD,OAAO,MAAM;MACTpK,QAAQ,CAAC2K,mBAAmB,CAAC,kBAAkB,EAAEpB,sBAAsB,CAAC;MACxEvJ,QAAQ,CAAC2K,mBAAmB,CAAC,kBAAkB,EAAEV,sBAAsB,CAAC;MACxEjK,QAAQ,CAAC2K,mBAAmB,CAAC,MAAM,EAAER,UAAU,CAAC;MAChDnK,QAAQ,CAAC2K,mBAAmB,CAAC,SAAS,EAAEP,mBAAmB,CAAC;IAChE,CAAC;EACL,CAAC,EAAE,CAACjN,IAAI,CAACwD,EAAE,EAAEtF,MAAM,EAAEe,SAAS,CAAC,CAAC;EAGhCjD,SAAS,CAAC,MAAM;IACZ;IACA;IACA;IACA;EAAA,CACH,EAAE,CAACqC,IAAI,EAAEH,MAAM,EAAEE,QAAQ,EAAED,QAAQ,EAAE8E,WAAW,EAAEK,cAAc,EAAEF,mBAAmB,EAAEC,gBAAgB,CAAC,CAAC;EAE1GrH,SAAS,CAAC,MAAM;IACZuF,YAAY,CAACkM,OAAO,CAAC,YAAY,EAAEhM,IAAI,CAAC0H,SAAS,CAAC/H,UAAU,CAAC,CAAC;EAClE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsM,aAAa,GAAGxR,WAAW,CAAE6G,CAAC,IAAK;IACrC;IACA,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAACI,QAAQ,CAACJ,CAAC,CAAC+I,GAAG,CAAC,EAAE;MACrE;MACA/I,CAAC,CAACC,cAAc,CAAC,CAAC;;MAElB;MACA,IAAI,CAACxE,SAAS,IAAIA,SAAS,CAACgM,MAAM,KAAK,CAAC,EAAE;MAE1C,MAAMmD,YAAY,GAAG,CAAC,GAAG5L,UAAU,EAAE,GAAGI,UAAU,EAAE,GAAGE,WAAW,CAAC;MACnE,MAAMuL,YAAY,GAAGD,YAAY,CAACE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACtK,EAAE,KAAKtC,gBAAgB,CAAC;MAE3E,IAAI,CAAC6B,CAAC,CAAC+I,GAAG,KAAK,SAAS,IAAI/I,CAAC,CAAC+I,GAAG,KAAK,WAAW,KAAK8B,YAAY,GAAG,CAAC,EAAE;QACpE,MAAMG,cAAc,GAAGJ,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACpK,EAAE;QACxDS,OAAO,CAACmB,GAAG,CAAC,+CAA+C,EAAE2I,cAAc,CAAC;QAC5E/G,kBAAkB,CAAC+G,cAAc,CAAC;MACtC,CAAC,MAAM,IAAI,CAAChL,CAAC,CAAC+I,GAAG,KAAK,WAAW,IAAI/I,CAAC,CAAC+I,GAAG,KAAK,YAAY,KAAK8B,YAAY,GAAGD,YAAY,CAACnD,MAAM,GAAG,CAAC,EAAE;QACpG,MAAMwD,cAAc,GAAGL,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACpK,EAAE;QACxDS,OAAO,CAACmB,GAAG,CAAC,kDAAkD,EAAE4I,cAAc,CAAC;QAC/EhH,kBAAkB,CAACgH,cAAc,CAAC;MACtC;IACJ;EACJ,CAAC,EAAE,CAACxP,SAAS,EAAEuD,UAAU,EAAEI,UAAU,EAAEE,WAAW,EAAEnB,gBAAgB,EAAE8F,kBAAkB,CAAC,CAAC;EAC1F;EACAhL,SAAS,CAAC,MAAM;IACZ6G,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE4K,aAAa,CAAC;IACnD,OAAO,MAAM;MACT7K,QAAQ,CAAC2K,mBAAmB,CAAC,SAAS,EAAEE,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACI5P,OAAA;IAAKmQ,SAAS,0BAAAlO,MAAA,CAA0BqB,UAAU,GAAG,yBAAyB,GAAG,uBAAuB,CAAG;IAAA8M,QAAA,gBACvGpQ,OAAA,CAAClC,gBAAgB;MAACuS,QAAQ,EAAE9P,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4N,IAAK;MAACmC,gBAAgB,EAAErK,oBAAqB;MAAC3C,UAAU,EAAE,CAACA;IAAW;MAAAiN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1GzP,OAAO,gBACJjB,OAAA;MAAKmQ,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAEhFpQ,OAAA,CAACd,WAAW;QACRyR,GAAG,EAAE7O,cAAe;QACpB8O,QAAQ,EAAErO,WAAY;QACtBe,UAAU,EAAEA,UAAW;QACvBW,UAAU,EAAEA,UAAW;QACvBI,UAAU,EAAEA,UAAW;QACvBE,WAAW,EAAEA,WAAY;QACzBsM,QAAQ,EAAE;UACNvG,oBAAoB;UACpBK,oBAAoB;UACpBW,qBAAqB;UACrBY,YAAY;UACZG,WAAW;UACXM,kBAAkB;UAClBmE,cAAc,EAAEA,CAACpL,EAAE,EAAEqL,EAAE,KAAM/P,YAAY,CAACe,OAAO,CAAC2D,EAAE,CAAC,GAAGqL,EAAG;UAC3D1N,mBAAmB,EAAGqC,EAAE,IAAKrC,mBAAmB,CAACqC,EAAE;QACvD,CAAE;QACFsL,QAAQ,EAAE;UACN5N,gBAAgB;UAChBE,UAAU;UACV1C,QAAQ;UACRE,SAAS;UACToC,iBAAiB;UACjBC,iBAAiB;UACjBY,QAAQ;UACRtB,eAAe;UACfkD;QACJ,CAAE;QACF5B,QAAQ,EAAEA;QACV;QAAA;QACAkN,iBAAiB,EAAExI,SAAU;QAC7BE,gBAAgB,EAAEA,gBAAiB;QACnC9E,aAAa,EAAEA;MAAc;QAAA0M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAIF1Q,OAAA;QAAKmQ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDpQ,OAAA;UACImQ,SAAS,gCAAAlO,MAAA,CAAgCqB,UAAU,GAAG,wBAAwB,GAAG,qBAAqB,CAAG;UACzG4N,OAAO,EAAEA,CAAA,KAAM3P,gBAAgB,CAACqE,IAAI,IAAI,CAACA,IAAI,CAAE;UAAAwK,QAAA,eAE/CpQ,OAAA,CAAChB,IAAI;YAAAuR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGN1Q,OAAA,CAACjB,eAAe;QAAAqR,QAAA,EACX,CAAC9O,aAAa,IAAIqI,MAAM,CAACwH,UAAU,GAAG,IAAI,kBACvCnR,OAAA,CAACf,WAAW;UACRqE,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7B3C,QAAQ,EAAEA,QAAS;UACnByF,oBAAoB,EAAEA,oBAAqB;UAC3CvF,SAAS,EAAEA,SAAU;UACrB2F,qBAAqB,EAAEA,qBAAsB;UAC7CxC,UAAU,EAAEA,UAAW;UACvBI,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzB6M,gBAAgB,EAAElI,kBAAmB;UACrC9F,gBAAgB,EAAEA,gBAAiB;UACnCX,eAAe,EAAEA,eAAgB;UACjCkD,kBAAkB,EAAEA,kBAAmB;UACvCgD,gBAAgB,EAAEA,gBAAiB;UACnC9E,aAAa,EAAEA,aAAc;UAC7BwN,eAAe,EAAE9O,WAAY;UAC7BhC,IAAI,EAAEA,IAAK;UACX6B,aAAa,EAAEA,aAAc;UAC7BsE,UAAU,EAAEA,UAAW;UACvBhG,SAAS,EAAEA,SAAU;UACrByD,eAAe,EAAEA,eAAgB;UACjCmN,kBAAkB,EAAE,EAAAnR,qBAAA,GAAA2B,cAAc,CAACC,OAAO,cAAA5B,qBAAA,uBAAtBA,qBAAA,CAAwBgJ,oBAAoB,CAAC,CAAC,KAAI,KAAM;UAC5EoI,qBAAqB,EAAG/K,KAAK,IAAK;YAC9B,IAAI1E,cAAc,CAACC,OAAO,EAAE;cACxB;cACAD,cAAc,CAACC,OAAO,CAACwP,qBAAqB,CAAC/K,KAAK,CAAC;YACvD;UACJ;QAAE;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CAAC,gBAEN1Q,OAAA;MAAKmQ,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC7CpQ,OAAA,CAAClB,mBAAmB;QAChB0S,OAAO,EAAEA,CAAA,KAAM;UACX;UACA,IAAIrQ,SAAS,EAAE;YACXd,QAAQ,CAACR,SAAS,CAAC;cAAEO,MAAM;cAAEe;YAAU,CAAC,CAAC,CAAC;UAC9C;UACAb,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACxC,CAAE;QACFqR,MAAM,EAAE,CAACxQ,OAAQ;QACjByQ,WAAW,EAAExK;MAAiB;QAAAqJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA,CAAAnQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqH,YAAY,KAAI3G,OAAO,iBAC1BjB,OAAA;MAAKmQ,SAAS,qEAAAlO,MAAA,CACRe,cAAc,GACV,0BAA0B,GAC1B,4BAA4B,yEACc;MAAAoN,QAAA,eAChDpQ,OAAA;QAAKmQ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCpQ,OAAA;UAAKmQ,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAAE1J,UAAU,CAACtE,aAAa,CAAC,EAAC,UAAK;QAAA;UAAAmO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEA,CAAC;AAEd,CAAC;AAACxQ,EAAA,CAp7BID,UAAU;EAAA,QACOzB,SAAS,EACXT,WAAW,EACXY,WAAW,EACXX,WAAW,EACNA,WAAW,EACbA,WAAW,EA4BdA,WAAW,EAC0CA,WAAW;AAAA;AAAA2T,EAAA,GAnC/E1R,UAAU;AAs7BhB,eAAeA,UAAU;AAAC,IAAA0R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}