import express from 'express'
import asyncHandler from '../middlewares/asyncHandler.js'
import validate from '../middlewares/validate.js'
import UserType from '../constants/UserType.js'
import { requireRoles } from '../middlewares/jwtMiddleware.js'
import * as ClassTuitionController from '../controllers/ClassTuitionController.js'

const router = express.Router()

// Lấy danh sách học phí của tất cả các lớp (chỉ admin, gi<PERSON>o viên, trợ giảng)
router.get('/v1/admin/class-tuition',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(ClassTuitionController.getAllClassTuition)
)

// Lấy danh sách học phí của một lớp cụ thể (chỉ admin, gi<PERSON><PERSON> viên, trợ giảng)
router.get('/v1/admin/class/:classId/tuition',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(ClassTuitionController.getClassTuitionByClassId)
)

// Lấy thông tin chi tiết một học phí (chỉ admin, giáo viên, trợ giảng)
router.get('/v1/admin/class-tuition/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER, UserType.ASSISTANT]),
    asyncHandler(ClassTuitionController.getClassTuitionById)
)

// Tạo mới học phí cho lớp (chỉ admin, giáo viên)
router.post('/v1/admin/class-tuition',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(ClassTuitionController.createClassTuition)
)

// Cập nhật thông tin học phí (chỉ admin, giáo viên)
router.put('/v1/admin/class-tuition/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(ClassTuitionController.updateClassTuition)
)

// Xóa học phí (chỉ admin, giáo viên)
router.delete('/v1/admin/class-tuition/:id',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(ClassTuitionController.deleteClassTuition)
)

// Tạo hàng loạt học phí cho các lớp có status là LHD và thuộc tính class (chỉ admin, giáo viên)
router.post('/v1/admin/class-tuition/batch',
    requireRoles([UserType.ADMIN, UserType.TEACHER]),
    asyncHandler(ClassTuitionController.createBatchClassTuition)
)

export default router
