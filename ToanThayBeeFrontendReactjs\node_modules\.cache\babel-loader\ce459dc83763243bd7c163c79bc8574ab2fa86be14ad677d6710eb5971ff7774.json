{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\DoExamPage.jsx\",\n  _s = $RefreshSig$();\nimport HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useState, useEffect, useRef, useCallback } from \"react\";\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\nimport { useParams } from \"react-router-dom\";\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\nimport { AnimatePresence } from \"framer-motion\";\nimport { Menu } from \"lucide-react\";\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\nimport ExamContent from \"../../../components/questions/ExamContent\";\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\nimport { setRemainingTime, summitExam, setSaveQuestions, setErrorQuestions, getRemainingTime, logUserActivity, submitAnswerWithAttempt, leaveExam } from \"../../../features/doExam/doExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoExamPage = () => {\n  _s();\n  var _examContentRef$curre;\n  const {\n    examId\n  } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    exam\n  } = useSelector(state => state.exams);\n  const {\n    questions\n  } = useSelector(state => state.questions);\n  const {\n    answers\n  } = useSelector(state => state.answers);\n  const [fontSize, setFontSize] = useState(14); // 14px mặc định\n  const [imageSize, setImageSize] = useState(12); // đơn vị: rem\n  const questionRefs = useRef([]);\n  const [isAgree, setIsAgree] = useState(false);\n  const [attemptId, setAttemptId] = useState(null);\n  const attemptRef = useRef(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [flag, setFlag] = useState(false);\n  const [startTime1, setStartTime1] = useState(null);\n  const hasSubmittedRef = useRef(false);\n  const examRef = useRef(null);\n  const examContentRef = useRef(null);\n  useEffect(() => {\n    examRef.current = exam;\n    if ((exam === null || exam === void 0 ? void 0 : exam.acceptDoExam) === false) {\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  }, [exam]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchPublicExamById(examId));\n    }\n  }, [dispatch, examId]);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    remainingTime,\n    saveQuestions,\n    errorQuestions\n  } = useSelector(state => state.doExam);\n  const [markedQuestions, setMarkedQuestions] = useState(new Set());\n  const [timeWarningShown, setTimeWarningShown] = useState({\n    fiveMinutes: false,\n    oneMinute: false\n  });\n  const [isTimeBlinking, setIsTimeBlinking] = useState(false);\n  const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    const saved = localStorage.getItem(\"isDarkMode\");\n    return saved ? JSON.parse(saved) : false;\n  });\n  const [loadingSubmit, setLoadingSubmit] = useState(false);\n  const [loadingLoadExam, setLoadingLoadExam] = useState(false);\n  const [isTimeUp, setIsTimeUp] = useState(false);\n  const [questionTN, setQuestionTN] = useState([]);\n  const [questionDS, setQuestionDS] = useState([]);\n  const [questionTLN, setQuestionTLN] = useState([]);\n  const [answerTN, setAnswerTN] = useState([]);\n  const [answerTLN, setAnswerTLN] = useState([]);\n  const [dsAnswers, setDsAnswers] = useState({});\n  document.addEventListener(\"copy\", e => {\n    e.preventDefault();\n  });\n  const addQuestion = questionId => {\n    const newSet = new Set(saveQuestions);\n    newSet.add(questionId);\n    dispatch(setSaveQuestions(Array.from(newSet))); // convert to array before saving\n    removeErrorQuestion(questionId);\n  };\n  const addErrorQuestion = questionId => {\n    const newSet = new Set(errorQuestions);\n    newSet.add(questionId);\n    dispatch(setErrorQuestions(Array.from(newSet)));\n    removeQuestion(questionId);\n  };\n  const removeQuestion = questionId => {\n    const newSet = new Set(saveQuestions);\n    newSet.delete(questionId);\n    dispatch(setSaveQuestions(Array.from(newSet)));\n  };\n  const removeErrorQuestion = questionId => {\n    const newSet = new Set(errorQuestions);\n    newSet.delete(questionId);\n    dispatch(setErrorQuestions(Array.from(newSet)));\n  };\n\n  // Hàm đánh dấu câu hỏi để xem lại sau\n  const toggleMarkQuestion = questionId => {\n    setMarkedQuestions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(questionId)) {\n        newSet.delete(questionId);\n      } else {\n        newSet.add(questionId);\n      }\n      return newSet;\n    });\n  };\n  const handleExitFullscreen = () => {\n    try {\n      exitFullscreen();\n    } catch (err) {\n      // Chỉ ghi log lỗi, không bắt lỗi\n      console.warn(\"Không thể thoát fullscreen:\", err);\n    }\n  };\n  const handleFontSizeChange = e => {\n    setFontSize(Number(e.target.value));\n  };\n  const handleImageSizeChange = e => {\n    setImageSize(Number(e.target.value));\n  };\n  const formatTime = seconds => {\n    const min = String(Math.floor(seconds / 60)).padStart(2, '0');\n    const sec = String(seconds % 60).padStart(2, '0');\n    return \"\".concat(min, \":\").concat(sec);\n  };\n  const handleFullScreen = async () => {\n    setLoadingLoadExam(true);\n    try {\n      // Gọi API join exam thay vì socket\n      const response = await fetch(\"/api/v1/user/join-exam/\".concat(examId), {\n        method: 'GET',\n        headers: {\n          'Authorization': \"Bearer \".concat(localStorage.getItem('token')),\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.message || 'Lỗi khi tham gia bài thi');\n      }\n\n      // Xử lý khi join exam thành công\n      const {\n        attemptId,\n        startTime\n      } = data;\n      console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\n      setIsAgree(true);\n      attemptRef.current = attemptId;\n      setAttemptId(attemptId);\n      if (examId) {\n        dispatch(fetchPublicQuestionsByExamId(examId));\n      }\n      setStartTime1(startTime);\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) {\n        setLoadingLoadExam(false);\n        return;\n      }\n      try {\n        const success = await requestFullscreen();\n        if (success) {\n          setTimeout(() => {\n            setLoadingLoadExam(false);\n          }, 800);\n        } else {\n          console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\n          setLoadingLoadExam(false);\n        }\n      } catch (err) {\n        console.error(\"❌ Lỗi khi bật fullscreen:\", err);\n        alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\n        setLoadingLoadExam(false);\n      }\n    } catch (error) {\n      console.error(\"Lỗi khi tham gia bài thi:\", error);\n      dispatch(setErrorMessage(\"Lỗi: \" + error.message));\n      setLoadingLoadExam(false);\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  };\n\n  // Removed socket-based exam_started listener - now handled in handleFullScreen\n\n  useEffect(() => {\n    if (exam !== null && exam !== void 0 && exam.testDuration && startTime1) {\n      const start = new Date(startTime1);\n      const now = new Date();\n      const elapsedSeconds = Math.floor((now - start) / 1000);\n      const totalSeconds = exam.testDuration * 60;\n      const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\n      dispatch(setRemainingTime(remaining));\n\n      // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\n      if (attemptId) {\n        dispatch(getRemainingTime({\n          examId,\n          attemptId\n        })).then(result => {\n          var _result$payload;\n          if (((_result$payload = result.payload) === null || _result$payload === void 0 ? void 0 : _result$payload.remainingTime) !== undefined) {\n            dispatch(setRemainingTime(result.payload.remainingTime));\n          }\n        }).catch(error => {\n          console.error(\"Lỗi khi lấy thời gian từ server:\", error);\n        });\n      }\n    }\n  }, [startTime1, exam, attemptId, examId, dispatch]);\n  useEffect(() => {\n    if (flag) return;\n    if (!remainingTime) setFlag(true);\n  }, [remainingTime]);\n  const handleAutoSubmit = async () => {\n    if (hasSubmittedRef.current) {\n      console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\n      return;\n    }\n    hasSubmittedRef.current = true; // Đánh dấu đã submit\n    console.log(\"Kiểm tra attemptId:\", attemptId);\n    if (!attemptId) {\n      console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\n      return;\n    }\n    console.log(\"Đang nộp bài với attemptId:\", attemptId);\n    dispatch(setSaveQuestions(new Set()));\n    setLoadingSubmit(true);\n    try {\n      // Sử dụng API thay vì socket để nộp bài\n      const result = await dispatch(summitExam(attemptId)).unwrap();\n      console.log(\"Nộp bài thành công:\", result);\n\n      // Xử lý khi nộp bài thành công\n      dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\n\n      // Thoát fullscreen mà không bắt lỗi\n      try {\n        exitFullscreen();\n      } catch (err) {\n        // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\n        console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\n      }\n      const safeAttemptId = attemptRef.current;\n      const currentExam = examRef.current;\n      if (!safeAttemptId) {\n        console.error(\"Không có attemptId khi navigate!\");\n        return;\n      }\n\n      // Log để debug\n      console.log(\"Current exam state:\", currentExam);\n      console.log(\"Attempt ID:\", safeAttemptId);\n      if (!currentExam || !currentExam.seeCorrectAnswer) {\n        console.log(\"Chuyển về trang danh sách do:\", {\n          examNull: !currentExam,\n          cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\n        });\n        navigate(\"/practice/exam/\".concat(examId));\n        return;\n      }\n      navigate(\"/practice/exam/attempt/\".concat(safeAttemptId, \"/score\"));\n    } catch (error) {\n      console.error(\"Lỗi khi nộp bài:\", error);\n      setLoadingSubmit(false);\n      dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\n      hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\n\n      // Thử nộp lại sau 3 giây nếu lỗi xảy ra\n      setTimeout(() => {\n        if (!loadingSubmit && attemptRef.current) {\n          console.log(\"Thử nộp bài lại sau lỗi...\");\n          handleAutoSubmit();\n        }\n      }, 5000);\n    }\n  };\n\n  // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\n  const navigateToQuestion = useCallback(questionId => {\n    setSelectedQuestion(questionId);\n\n    // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\n    if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\n      // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\n      examContentRef.current.goToQuestionById(questionId);\n    } else {\n      // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\n      // Tìm phần tử câu hỏi bằng querySelector\n      setTimeout(() => {\n        // Thử tìm phần tử bằng data-question-id\n        const element = document.querySelector(\"[data-question-id=\\\"\".concat(questionId, \"\\\"]\"));\n        if (element) {\n          const offset = 80; // chiều cao của header sticky\n          const y = element.getBoundingClientRect().top + window.scrollY - offset;\n          window.scrollTo({\n            top: y,\n            behavior: \"smooth\"\n          });\n        } else {\n          // Fallback: Sử dụng refs\n          const refElement = questionRefs.current[questionId];\n          if (refElement) {\n            const offset = 80; // chiều cao của header sticky\n            const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\n            window.scrollTo({\n              top: y,\n              behavior: \"smooth\"\n            });\n          }\n        }\n      }, 0);\n    }\n  }, [questionRefs, examContentRef]);\n\n  // Alias cho navigateToQuestion để tương thích với các component khác\n  const scrollToQuestion = navigateToQuestion;\n  const handleSelectAnswerTN = (questionId, statementId, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const newAnswer = {\n      questionId,\n      answerContent: statementId,\n      typeOfQuestion: type\n    };\n    dispatch(setAnswers(newAnswer));\n\n    // Sử dụng API thay vì socket\n    dispatch(submitAnswerWithAttempt({\n      questionId,\n      answerContent: statementId,\n      type,\n      attemptId\n    })).then(result => {\n      if (result.type.endsWith('/fulfilled')) {\n        // Answer submitted successfully\n        console.log(\"Đã lưu câu trả lời thành công\");\n      } else {\n        // Handle error\n        console.error(\"Lỗi khi lưu câu trả lời:\", result.error);\n      }\n    });\n  };\n  const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const currentAnswers = dsAnswers[questionId] || [];\n    const existing = currentAnswers.find(ans => ans.statementId === statementId);\n\n    // 🔁 Nếu đáp án đã giống thì không gửi lại\n    if (existing && existing.answer === selectedAnswer) {\n      return;\n    }\n    const updatedAnswers = currentAnswers.map(ans => ans.statementId === statementId ? {\n      ...ans,\n      answer: selectedAnswer\n    } : ans);\n\n    // Nếu chưa có statement này\n    if (!existing) {\n      updatedAnswers.push({\n        statementId,\n        answer: selectedAnswer\n      });\n    }\n\n    // ✨ Gửi toàn bộ lên server\n    socket.emit(\"select_answer\", {\n      questionId,\n      answerContent: updatedAnswers,\n      studentId: user.id,\n      attemptId,\n      type: \"DS\",\n      examId,\n      name: user.lastName + \" \" + user.firstName\n    });\n    dispatch(setAnswers({\n      questionId,\n      answerContent: JSON.stringify(updatedAnswers),\n      typeOfQuestion: \"DS\"\n    }));\n  };\n  const handleSelectAnswerTLN = (questionId, answerContent, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    if (!answerContent || answerContent.trim() === \"\") {\n      return;\n    }\n    const payload = {\n      attemptId,\n      questionId,\n      answerContent: answerContent.trim().replace(\",\", \".\"),\n      studentId: user.id,\n      type,\n      examId,\n      name: user.lastName + \" \" + user.firstName\n    };\n    dispatch(setAnswers({\n      questionId,\n      answerContent,\n      typeOfQuestion: type\n    }));\n    socket.emit(\"select_answer\", payload);\n  };\n\n  // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\n  const questionsToMarkAsSaved = useRef(new Set());\n\n  // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\n  useEffect(() => {\n    if (questionsToMarkAsSaved.current.size > 0) {\n      questionsToMarkAsSaved.current.forEach(questionId => {\n        if (!saveQuestions.has(questionId)) {\n          addQuestion(questionId);\n        }\n      });\n      questionsToMarkAsSaved.current.clear();\n    }\n  }, [saveQuestions, addQuestion]);\n\n  // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\n  useEffect(() => {\n    // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\n    const frameId = requestAnimationFrame(() => {\n      if (questionsToMarkAsSaved.current.size > 0) {\n        const questionIds = [...questionsToMarkAsSaved.current];\n        questionsToMarkAsSaved.current.clear();\n\n        // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\n        questionIds.forEach(questionId => {\n          if (!saveQuestions.has(questionId)) {\n            addQuestion(questionId);\n          }\n        });\n      }\n    });\n    return () => cancelAnimationFrame(frameId);\n  });\n  const isTNSelected = useCallback((questionId, statementId) => {\n    const isSelected = answerTN.some(ans => ans.questionId === questionId && ans.answerContent && String(ans.answerContent) === String(statementId));\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.has(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [answerTN, saveQuestions]);\n  const isDSChecked = useCallback((questionId, statementId, bool) => {\n    var _dsAnswers$questionId, _dsAnswers$questionId2;\n    const isSelected = ((_dsAnswers$questionId = dsAnswers[questionId]) === null || _dsAnswers$questionId === void 0 ? void 0 : _dsAnswers$questionId.some(a => a.statementId === statementId && a.answer === bool)) || false;\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.has(questionId) && ((_dsAnswers$questionId2 = dsAnswers[questionId]) === null || _dsAnswers$questionId2 === void 0 ? void 0 : _dsAnswers$questionId2.length) === 4) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [dsAnswers, saveQuestions]);\n  const getTLNDefaultValue = useCallback(questionId => {\n    var _matched$answerConten;\n    const matched = answerTLN.find(ans => ans.questionId === questionId);\n    const content = (matched === null || matched === void 0 ? void 0 : (_matched$answerConten = matched.answerContent) === null || _matched$answerConten === void 0 ? void 0 : _matched$answerConten.replace(/^\"|\"$/g, \"\")) || \"\";\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (content && !saveQuestions.has(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return content;\n  }, [answerTLN, saveQuestions]);\n\n  // useEffect(() => {\n  //     if (examId) {\n  //         dispatch(fetchPublicQuestionsByExamId(examId));\n  //     }\n  // }, [dispatch, examId]);\n\n  useEffect(() => {\n    if (questions) {\n      setQuestionTN(questions.filter(question => question.typeOfQuestion === \"TN\"));\n      setQuestionDS(questions.filter(question => question.typeOfQuestion === \"DS\"));\n      setQuestionTLN(questions.filter(question => question.typeOfQuestion === \"TLN\"));\n    }\n  }, [questions]);\n  useEffect(() => {\n    // Kiểm tra answers có phải là mảng không\n    if (!Array.isArray(answers) || answers.length === 0) return;\n    const tn = [];\n    const tln = [];\n    const dsMap = {};\n\n    // Sử dụng for...of thay vì forEach để tránh lỗi\n    for (const answer of answers) {\n      if (answer.typeOfQuestion === \"TN\") {\n        tn.push(answer);\n      } else if (answer.typeOfQuestion === \"TLN\") {\n        tln.push(answer);\n      } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\n        try {\n          const parsed = JSON.parse(answer.answerContent);\n          dsMap[answer.questionId] = parsed;\n        } catch (err) {\n          console.error(\"Lỗi parse DS answerContent:\", err);\n        }\n      }\n    }\n    setAnswerTN(tn);\n    setAnswerTLN(tln);\n    setDsAnswers(dsMap);\n    if (!socket || !socket.connected || !attemptId || !examId) return;\n    socket.emit(\"calculate_score\", {\n      attemptId,\n      answers,\n      examId,\n      student: user\n    });\n  }, [answers]);\n  useEffect(() => {\n    if (attemptId) {\n      dispatch(fetchAnswersByAttempt(attemptId));\n    }\n  }, [dispatch, attemptId]);\n  useEffect(() => {\n    if (!(exam !== null && exam !== void 0 && exam.testDuration) || remainingTime === null || !isAgree) return;\n\n    // Kiểm tra và hiển thị cảnh báo thời gian\n    const checkTimeWarnings = time => {\n      // Cảnh báo khi còn 5 phút\n      if (time === 300 && !timeWarningShown.fiveMinutes) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          fiveMinutes: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\n\n        // Tắt hiệu ứng nhấp nháy sau 10 giây\n        setTimeout(() => {\n          setIsTimeBlinking(false);\n        }, 10000);\n      }\n\n      // Cảnh báo khi còn 1 phút\n      if (time === 60 && !timeWarningShown.oneMinute) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          oneMinute: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\n\n        // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\n      }\n    };\n\n    // Định kỳ yêu cầu thời gian từ server để đồng bộ\n    const syncTimeInterval = setInterval(() => {\n      if (socket.connected && attemptId) {\n        socket.emit(\"request_time\", {\n          examId,\n          attemptId\n        });\n      }\n    }, 30000); // Đồng bộ thời gian mỗi 30 giây\n\n    const interval = setInterval(() => {\n      dispatch(setRemainingTime(prev => {\n        if (prev <= 1) {\n          // dùng <=1 để đảm bảo không bị âm\n          clearInterval(interval);\n          clearInterval(syncTimeInterval);\n          // Đánh dấu là đã hết thời gian\n          setIsTimeUp(true);\n          setIsTimeBlinking(false);\n          // Thử nộp bài\n          handleAutoSubmit();\n          return 0;\n        }\n\n        // Kiểm tra cảnh báo thời gian\n        checkTimeWarnings(prev);\n        return prev - 1;\n      }));\n    }, 1000);\n    return () => {\n      clearInterval(interval);\n      clearInterval(syncTimeInterval);\n    };\n  }, [exam === null || exam === void 0 ? void 0 : exam.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, socket, attemptId, examId]); // Chỉ phụ thuộc vào các giá trị cần thiết\n\n  useEffect(() => {\n    if (isAgree && !socket.connected) {\n      socket.connect();\n    }\n    return () => {\n      socket.disconnect();\n    };\n  }, [isAgree]);\n\n  // frontend\n  useEffect(() => {\n    if (!attemptId || !(user !== null && user !== void 0 && user.id) || !examId || attemptId === null || attemptId === undefined) return;\n    if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) return;\n    console.log(\"Đã bật theo dõi hành vi gian lận\");\n    const recentLogs = new Set(); // chống log lặp\n    const logOnce = (key, payload) => {\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled) || recentLogs.has(key)) return;\n      recentLogs.add(key);\n      socket.emit(\"user_log\", {\n        ...payload,\n        name: user.lastName + \" \" + user.firstName\n      });\n      setTimeout(() => recentLogs.delete(key), 5000);\n    };\n\n    // 📌 Thoát fullscreen\n    const handleFullscreenChange = () => {\n      if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {\n        logOnce(\"exit_fullscreen\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"EF\",\n          action: \"exit_fullscreen\",\n          detail: JSON.stringify({\n            reason: \"User exited fullscreen mode\"\n          })\n        });\n      }\n    };\n\n    // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === \"hidden\") {\n        logOnce(\"tab_blur\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"TB\",\n          action: \"tab_blur\",\n          detail: JSON.stringify({\n            message: \"User switched tab or minimized window\"\n          })\n        });\n      }\n    };\n\n    // 📌 Copy nội dung\n    const handleCopy = () => {\n      logOnce(\"copy_detected\", {\n        studentId: user.id,\n        attemptId,\n        examId,\n        code: \"COP\",\n        action: \"copy_detected\",\n        detail: JSON.stringify({\n          message: \"User copied content\"\n        })\n      });\n    };\n\n    // 📌 Phím đáng ngờ\n    const handleSuspiciousKey = e => {\n      const suspiciousKeys = [\"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"];\n      const combo = \"\".concat(e.ctrlKey ? \"Ctrl+\" : \"\").concat(e.shiftKey ? \"Shift+\" : \"\").concat(e.altKey ? \"Alt+\" : \"\").concat(e.metaKey ? \"Meta+\" : \"\").concat(e.key);\n      if (suspiciousKeys.includes(e.key) || combo === \"Ctrl+Shift+I\" || combo === \"Ctrl+Shift+C\") {\n        logOnce(\"key_\".concat(combo), {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"SK\",\n          action: \"suspicious_key\",\n          detail: JSON.stringify({\n            key: e.key,\n            code: e.code,\n            combo\n          })\n        });\n      }\n    };\n    document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n    document.addEventListener(\"copy\", handleCopy);\n    document.addEventListener(\"keydown\", handleSuspiciousKey);\n    return () => {\n      document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      document.removeEventListener(\"copy\", handleCopy);\n      document.removeEventListener(\"keydown\", handleSuspiciousKey);\n    };\n  }, [socket, user.id, examId, attemptId]);\n  useEffect(() => {\n    // Chỉ lắng nghe các sự kiện liên quan đến câu trả lời\n    const handleAnswerSaved = _ref => {\n      let {\n        questionId\n      } = _ref;\n      addQuestion(questionId);\n      removeErrorQuestion(questionId);\n    };\n    const handleAnswerError = _ref2 => {\n      let {\n        questionId,\n        message\n      } = _ref2;\n      dispatch(setErrorMessage(message));\n      removeQuestion(questionId);\n      addErrorQuestion(questionId);\n    };\n\n    // Lắng nghe sự kiện cập nhật thời gian từ server\n    const handleExamTimer = _ref3 => {\n      let {\n        remainingTime: serverRemainingTime\n      } = _ref3;\n      console.log(\"Nhận thời gian từ server:\", serverRemainingTime);\n      dispatch(setRemainingTime(serverRemainingTime));\n    };\n\n    // Lắng nghe sự kiện bài thi tự động nộp\n    const handleExamAutoSubmitted = _ref4 => {\n      let {\n        message,\n        attemptId: autoSubmitAttemptId,\n        score\n      } = _ref4;\n      console.log(\"Bài thi đã tự động nộp:\", {\n        message,\n        autoSubmitAttemptId,\n        score\n      });\n      dispatch(setSuccessMessage(message));\n      setIsTimeUp(true);\n\n      // Thoát fullscreen\n      try {\n        exitFullscreen();\n      } catch (err) {\n        console.warn(\"Không thể thoát fullscreen khi bài thi tự động nộp:\", err);\n      }\n\n      // Chuyển hướng đến trang kết quả nếu được phép xem đáp án\n      if (exam !== null && exam !== void 0 && exam.seeCorrectAnswer) {\n        navigate(\"/practice/exam/attempt/\".concat(autoSubmitAttemptId, \"/score\"));\n      } else {\n        navigate(\"/practice/exam/\".concat(examId));\n      }\n    };\n\n    // Lắng nghe thông báo từ giáo viên hoặc hệ thống\n    const handleExamNotification = _ref5 => {\n      let {\n        message\n      } = _ref5;\n      console.log(\"Nhận thông báo:\", message);\n      dispatch(setSuccessMessage(message));\n    };\n\n    // Đăng ký các event listeners\n    socket.on(\"answer_saved\", handleAnswerSaved);\n    socket.on(\"answer_error\", handleAnswerError);\n    socket.on(\"exam_timer\", handleExamTimer);\n    socket.on(\"exam_auto_submitted\", handleExamAutoSubmitted);\n    socket.on(\"exam_notification\", handleExamNotification);\n    return () => {\n      // Hủy đăng ký các event listeners\n      socket.off(\"answer_saved\", handleAnswerSaved);\n      socket.off(\"answer_error\", handleAnswerError);\n      socket.off(\"exam_timer\", handleExamTimer);\n      socket.off(\"exam_auto_submitted\", handleExamAutoSubmitted);\n      socket.off(\"exam_notification\", handleExamNotification);\n    };\n  }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\n  useEffect(() => {\n    localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  // Hàm xử lý chuyển đổi câu hỏi\n  const handleKeyDown = useCallback(e => {\n    // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\n    if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\n      // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\n      e.preventDefault();\n\n      // Nếu không có câu hỏi, thoát khỏi hàm\n      if (!questions || questions.length === 0) return;\n      const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\n      const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\n      if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\n        const prevQuestionId = allQuestions[currentIndex - 1].id;\n        console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\n        navigateToQuestion(prevQuestionId);\n      } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\n        const nextQuestionId = allQuestions[currentIndex + 1].id;\n        console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\n        navigateToQuestion(nextQuestionId);\n      }\n    }\n  }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\n  // Lắng nghe sự kiện bàn phím\n  useEffect(() => {\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [handleKeyDown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full \".concat(isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'),\n    children: [/*#__PURE__*/_jsxDEV(HeaderDoExamPage, {\n      nameExam: exam === null || exam === void 0 ? void 0 : exam.name,\n      onExitFullscreen: handleExitFullscreen,\n      isDarkMode: !isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 13\n    }, this), isAgree ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\",\n      children: [/*#__PURE__*/_jsxDEV(ExamContent, {\n        ref: examContentRef,\n        loading1: loadingLoadExam,\n        isDarkMode: isDarkMode,\n        questionTN: questionTN,\n        questionDS: questionDS,\n        questionTLN: questionTLN,\n        handlers: {\n          handleSelectAnswerTN,\n          handleSelectAnswerDS,\n          handleSelectAnswerTLN,\n          isTNSelected,\n          isDSChecked,\n          getTLNDefaultValue,\n          setQuestionRef: (id, el) => questionRefs.current[id] = el,\n          setSelectedQuestion: id => setSelectedQuestion(id)\n        },\n        settings: {\n          selectedQuestion,\n          isDarkMode,\n          fontSize,\n          imageSize,\n          prefixStatementTN,\n          prefixStatementDS,\n          isTimeUp,\n          markedQuestions,\n          toggleMarkQuestion\n        },\n        isTimeUp: isTimeUp\n        // Để undefined để component tự quyết định dựa trên thiết bị\n        ,\n        initialSingleMode: undefined,\n        handleAutoSubmit: handleAutoSubmit,\n        loadingSubmit: loadingSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-full shadow-md \".concat(isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"),\n          onClick: () => setIsSidebarOpen(prev => !prev),\n          children: /*#__PURE__*/_jsxDEV(Menu, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: (isSidebarOpen || window.innerWidth > 1024) && /*#__PURE__*/_jsxDEV(ExamSidebar, {\n          isDarkMode: isDarkMode,\n          setIsDarkMode: setIsDarkMode,\n          fontSize: fontSize,\n          handleFontSizeChange: handleFontSizeChange,\n          imageSize: imageSize,\n          handleImageSizeChange: handleImageSizeChange,\n          questionTN: questionTN,\n          questionDS: questionDS,\n          questionTLN: questionTLN,\n          scrollToQuestion: navigateToQuestion,\n          selectedQuestion: selectedQuestion,\n          markedQuestions: markedQuestions,\n          toggleMarkQuestion: toggleMarkQuestion,\n          handleAutoSubmit: handleAutoSubmit,\n          loadingSubmit: loadingSubmit,\n          loadingLoadExam: loadingLoadExam,\n          exam: exam,\n          remainingTime: remainingTime,\n          formatTime: formatTime,\n          questions: questions,\n          singleQuestionMode: ((_examContentRef$curre = examContentRef.current) === null || _examContentRef$curre === void 0 ? void 0 : _examContentRef$curre.isSingleQuestionMode()) || false,\n          setSingleQuestionMode: value => {\n            if (examContentRef.current) {\n              // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\n              examContentRef.current.setSingleQuestionMode(value);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 908,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 859,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(ExamRegulationModal, {\n        onClose: () => {\n          if (socket.connected) {\n            socket.emit(\"leave_exam\", {\n              studentId: user === null || user === void 0 ? void 0 : user.id,\n              examId\n            });\n            socket.disconnect();\n          }\n          socket.removeAllListeners(); // Xóa hết listener để tránh lỗi khi component bị unmount\n          navigate(\"/practice/exam/\".concat(examId));\n        },\n        isOpen: !isAgree,\n        onStartExam: handleFullScreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 945,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 944,\n      columnNumber: 17\n    }, this), (exam === null || exam === void 0 ? void 0 : exam.testDuration) && isAgree && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-2 rounded-md left-2 px-4 py-2\\n                    \".concat(isTimeBlinking ? 'bg-red-600 animate-pulse' : 'bg-slate-700 bg-opacity-80', \"\\n                    text-white z-50 transition-colors duration-300\"),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-bold\",\n          children: [formatTime(remainingTime), \" ph\\xFAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 966,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 961,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 856,\n    columnNumber: 9\n  }, this);\n};\n_s(DoExamPage, \"R0/BZJvy3dLn7Yt4E4U7FrhDxwY=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DoExamPage;\nexport default DoExamPage;\nvar _c;\n$RefreshReg$(_c, \"DoExamPage\");", "map": {"version": 3, "names": ["HeaderDoExamPage", "useDispatch", "useSelector", "useState", "useEffect", "useRef", "useCallback", "fetchPublicQuestionsByExamId", "fetchPublicExamById", "useParams", "setErrorMessage", "setSuccessMessage", "useNavigate", "fetchAnswersByAttempt", "setAnswers", "ExamRegulationModal", "AnimatePresence", "<PERSON><PERSON>", "ExamSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestFullscreen", "exitFullscreen", "isFullscreen", "setRemainingTime", "summitExam", "setSaveQuestions", "setErrorQuestions", "getRemainingTime", "logUserActivity", "submitAnswerWithAttempt", "leaveExam", "jsxDEV", "_jsxDEV", "DoExamPage", "_s", "_examContentRef$curre", "examId", "dispatch", "navigate", "exam", "state", "exams", "questions", "answers", "fontSize", "setFontSize", "imageSize", "setImageSize", "questionRefs", "isAgree", "setIsAgree", "attemptId", "setAttemptId", "attemptRef", "isSidebarOpen", "setIsSidebarOpen", "flag", "setFlag", "startTime1", "setStartTime1", "hasSubmittedRef", "examRef", "examContentRef", "current", "acceptDoExam", "concat", "user", "auth", "remainingTime", "saveQuestions", "errorQuestions", "doExam", "markedQuestions", "setMarkedQuestions", "Set", "timeWarningShown", "setTimeWarningShown", "fiveMinutes", "oneMinute", "isTimeBlinking", "setIsTimeBlinking", "prefixStatementTN", "prefixStatementDS", "selectedQuestion", "setSelectedQuestion", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "loadingSubmit", "setLoadingSubmit", "loadingLoadExam", "setLoadingLoadExam", "isTimeUp", "setIsTimeUp", "questionTN", "setQuestionTN", "questionDS", "setQuestionDS", "questionTLN", "setQuestionTLN", "answerTN", "setAnswerTN", "answerTLN", "setAnswerTLN", "dsAnswers", "setDsAnswers", "document", "addEventListener", "e", "preventDefault", "addQuestion", "questionId", "newSet", "add", "Array", "from", "removeErrorQuestion", "addErrorQuestion", "removeQuestion", "delete", "toggleMarkQuestion", "prev", "has", "handleExitFullscreen", "err", "console", "warn", "handleFontSizeChange", "Number", "target", "value", "handleImageSizeChange", "formatTime", "seconds", "min", "String", "Math", "floor", "padStart", "sec", "handleFullScreen", "response", "fetch", "method", "headers", "data", "json", "ok", "Error", "message", "startTime", "log", "isCheatingCheckEnabled", "success", "setTimeout", "error", "alert", "testDuration", "start", "Date", "now", "elapsedSeconds", "totalSeconds", "remaining", "max", "then", "result", "_result$payload", "payload", "undefined", "catch", "handleAutoSubmit", "unwrap", "safeAttemptId", "currentExam", "seeCorrectAnswer", "examNull", "cantSeeAnswer", "navigateToQuestion", "isSingleQuestionMode", "goToQuestionById", "element", "querySelector", "offset", "y", "getBoundingClientRect", "top", "window", "scrollY", "scrollTo", "behavior", "refElement", "scrollToQuestion", "handleSelectAnswerTN", "statementId", "type", "newAnswer", "answerContent", "typeOfQuestion", "endsWith", "handleSelectAnswerDS", "<PERSON><PERSON><PERSON><PERSON>", "currentAnswers", "existing", "find", "ans", "answer", "updatedAnswers", "map", "push", "socket", "emit", "studentId", "id", "name", "lastName", "firstName", "stringify", "handleSelectAnswerTLN", "trim", "replace", "questionsToMarkAsSaved", "size", "for<PERSON>ach", "clear", "frameId", "requestAnimationFrame", "questionIds", "cancelAnimationFrame", "isTNSelected", "isSelected", "some", "isDSChecked", "bool", "_dsAnswers$questionId", "_dsAnswers$questionId2", "a", "length", "getTLNDefaultValue", "_matched$answerConten", "matched", "content", "filter", "question", "isArray", "tn", "tln", "dsMap", "parsed", "connected", "student", "checkTimeWarnings", "time", "syncTimeInterval", "setInterval", "interval", "clearInterval", "connect", "disconnect", "recentLogs", "logOnce", "key", "handleFullscreenChange", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "code", "action", "detail", "reason", "handleVisibilityChange", "visibilityState", "handleCopy", "handleSuspiciousKey", "<PERSON><PERSON><PERSON><PERSON>", "combo", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "includes", "removeEventListener", "handleAnswerSaved", "_ref", "handleAnswerError", "_ref2", "handleExamTimer", "_ref3", "serverRemainingTime", "handleExamAutoSubmitted", "_ref4", "autoSubmitAttemptId", "score", "handleExamNotification", "_ref5", "on", "off", "setItem", "handleKeyDown", "allQuestions", "currentIndex", "findIndex", "q", "prevQuestionId", "nextQuestionId", "className", "children", "nameExam", "onExitFullscreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "loading1", "handlers", "setQuestionRef", "el", "settings", "initialSingleMode", "onClick", "innerWidth", "singleQuestionMode", "setSingleQuestionMode", "onClose", "removeAllListeners", "isOpen", "onStartExam", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/DoExamPage.jsx"], "sourcesContent": ["import HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\r\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\r\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\r\nimport { AnimatePresence } from \"framer-motion\";\r\nimport { Menu } from \"lucide-react\";\r\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\r\nimport ExamContent from \"../../../components/questions/ExamContent\";\r\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\r\nimport {\r\n    setRemainingTime,\r\n    summitExam,\r\n    setSaveQuestions,\r\n    setErrorQuestions,\r\n    getRemainingTime,\r\n    logUserActivity,\r\n    submitAnswerWithAttempt,\r\n    leaveExam,\r\n} from \"../../../features/doExam/doExamSlice\";\r\n\r\nconst DoExamPage = () => {\r\n    const { examId } = useParams();\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { exam } = useSelector(state => state.exams);\r\n    const { questions } = useSelector(state => state.questions);\r\n    const { answers } = useSelector(state => state.answers);\r\n    const [fontSize, setFontSize] = useState(14); // 14px mặc định\r\n    const [imageSize, setImageSize] = useState(12); // đơn vị: rem\r\n    const questionRefs = useRef([]);\r\n    const [isAgree, setIsAgree] = useState(false);\r\n    const [attemptId, setAttemptId] = useState(null);\r\n    const attemptRef = useRef(null);\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const [flag, setFlag] = useState(false);\r\n    const [startTime1, setStartTime1] = useState(null);\r\n    const hasSubmittedRef = useRef(false);\r\n    const examRef = useRef(null);\r\n    const examContentRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        examRef.current = exam;\r\n        if (exam?.acceptDoExam === false) {\r\n            navigate(`/practice/exam/${examId}`)\r\n        }\r\n    }, [exam]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchPublicExamById(examId));\r\n        }\r\n    }, [dispatch, examId]);\r\n\r\n\r\n    const { user } = useSelector((state) => state.auth);\r\n    const { remainingTime, saveQuestions, errorQuestions } = useSelector((state) => state.doExam);\r\n\r\n\r\n    const [markedQuestions, setMarkedQuestions] = useState(new Set());\r\n    const [timeWarningShown, setTimeWarningShown] = useState({\r\n        fiveMinutes: false,\r\n        oneMinute: false\r\n    });\r\n    const [isTimeBlinking, setIsTimeBlinking] = useState(false);\r\n\r\n    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [isDarkMode, setIsDarkMode] = useState(() => {\r\n        const saved = localStorage.getItem(\"isDarkMode\");\r\n        return saved ? JSON.parse(saved) : false;\r\n    });\r\n\r\n    const [loadingSubmit, setLoadingSubmit] = useState(false);\r\n    const [loadingLoadExam, setLoadingLoadExam] = useState(false);\r\n    const [isTimeUp, setIsTimeUp] = useState(false);\r\n\r\n    const [questionTN, setQuestionTN] = useState([]);\r\n    const [questionDS, setQuestionDS] = useState([]);\r\n    const [questionTLN, setQuestionTLN] = useState([]);\r\n\r\n    const [answerTN, setAnswerTN] = useState([]);\r\n    const [answerTLN, setAnswerTLN] = useState([]);\r\n    const [dsAnswers, setDsAnswers] = useState({});\r\n\r\n    document.addEventListener(\"copy\", (e) => {\r\n        e.preventDefault();\r\n    });\r\n\r\n    const addQuestion = (questionId) => {\r\n        const newSet = new Set(saveQuestions);\r\n        newSet.add(questionId);\r\n        dispatch(setSaveQuestions(Array.from(newSet))); // convert to array before saving\r\n        removeErrorQuestion(questionId);\r\n    };\r\n\r\n    const addErrorQuestion = (questionId) => {\r\n        const newSet = new Set(errorQuestions);\r\n        newSet.add(questionId);\r\n        dispatch(setErrorQuestions(Array.from(newSet)));\r\n        removeQuestion(questionId);\r\n    };\r\n\r\n    const removeQuestion = (questionId) => {\r\n        const newSet = new Set(saveQuestions);\r\n        newSet.delete(questionId);\r\n        dispatch(setSaveQuestions(Array.from(newSet)));\r\n    };\r\n\r\n    const removeErrorQuestion = (questionId) => {\r\n        const newSet = new Set(errorQuestions);\r\n        newSet.delete(questionId);\r\n        dispatch(setErrorQuestions(Array.from(newSet)));\r\n    };\r\n\r\n    // Hàm đánh dấu câu hỏi để xem lại sau\r\n    const toggleMarkQuestion = (questionId) => {\r\n        setMarkedQuestions(prev => {\r\n            const newSet = new Set(prev);\r\n            if (newSet.has(questionId)) {\r\n                newSet.delete(questionId);\r\n            } else {\r\n                newSet.add(questionId);\r\n            }\r\n            return newSet;\r\n        });\r\n    };\r\n\r\n\r\n    const handleExitFullscreen = () => {\r\n        try {\r\n            exitFullscreen();\r\n        } catch (err) {\r\n            // Chỉ ghi log lỗi, không bắt lỗi\r\n            console.warn(\"Không thể thoát fullscreen:\", err);\r\n        }\r\n    };\r\n\r\n    const handleFontSizeChange = (e) => {\r\n        setFontSize(Number(e.target.value));\r\n    };\r\n\r\n    const handleImageSizeChange = (e) => {\r\n        setImageSize(Number(e.target.value));\r\n    };\r\n\r\n    const formatTime = (seconds) => {\r\n        const min = String(Math.floor(seconds / 60)).padStart(2, '0');\r\n        const sec = String(seconds % 60).padStart(2, '0');\r\n        return `${min}:${sec}`;\r\n    };\r\n\r\n    const handleFullScreen = async () => {\r\n        setLoadingLoadExam(true);\r\n\r\n        try {\r\n            // Gọi API join exam thay vì socket\r\n            const response = await fetch(`/api/v1/user/join-exam/${examId}`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n\r\n            const data = await response.json();\r\n\r\n            if (!response.ok) {\r\n                throw new Error(data.message || 'Lỗi khi tham gia bài thi');\r\n            }\r\n\r\n            // Xử lý khi join exam thành công\r\n            const { attemptId, startTime } = data;\r\n            console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\r\n\r\n            setIsAgree(true);\r\n            attemptRef.current = attemptId;\r\n            setAttemptId(attemptId);\r\n\r\n            if (examId) {\r\n                dispatch(fetchPublicQuestionsByExamId(examId));\r\n            }\r\n            setStartTime1(startTime);\r\n\r\n            if (!exam?.isCheatingCheckEnabled) {\r\n                setLoadingLoadExam(false);\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const success = await requestFullscreen();\r\n                if (success) {\r\n                    setTimeout(() => {\r\n                        setLoadingLoadExam(false);\r\n                    }, 800);\r\n                } else {\r\n                    console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\r\n                    setLoadingLoadExam(false);\r\n                }\r\n            } catch (err) {\r\n                console.error(\"❌ Lỗi khi bật fullscreen:\", err);\r\n                alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\r\n                setLoadingLoadExam(false);\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi tham gia bài thi:\", error);\r\n            dispatch(setErrorMessage(\"Lỗi: \" + error.message));\r\n            setLoadingLoadExam(false);\r\n            navigate(`/practice/exam/${examId}`);\r\n        }\r\n    };\r\n\r\n    // Removed socket-based exam_started listener - now handled in handleFullScreen\r\n\r\n    useEffect(() => {\r\n        if (exam?.testDuration && startTime1) {\r\n            const start = new Date(startTime1);\r\n            const now = new Date();\r\n            const elapsedSeconds = Math.floor((now - start) / 1000);\r\n            const totalSeconds = exam.testDuration * 60;\r\n            const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\r\n            dispatch(setRemainingTime(remaining));\r\n\r\n            // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\r\n            if (attemptId) {\r\n                dispatch(getRemainingTime({ examId, attemptId }))\r\n                    .then((result) => {\r\n                        if (result.payload?.remainingTime !== undefined) {\r\n                            dispatch(setRemainingTime(result.payload.remainingTime));\r\n                        }\r\n                    })\r\n                    .catch((error) => {\r\n                        console.error(\"Lỗi khi lấy thời gian từ server:\", error);\r\n                    });\r\n            }\r\n        }\r\n    }, [startTime1, exam, attemptId, examId, dispatch]);\r\n\r\n    useEffect(() => {\r\n        if (flag) return\r\n        if (!remainingTime) setFlag(true)\r\n    }, [remainingTime])\r\n\r\n    const handleAutoSubmit = async () => {\r\n        if (hasSubmittedRef.current) {\r\n            console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\r\n            return;\r\n        }\r\n        hasSubmittedRef.current = true; // Đánh dấu đã submit\r\n        console.log(\"Kiểm tra attemptId:\", attemptId);\r\n        if (!attemptId) {\r\n            console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\r\n            return;\r\n        }\r\n\r\n        console.log(\"Đang nộp bài với attemptId:\", attemptId);\r\n        dispatch(setSaveQuestions(new Set()));\r\n        setLoadingSubmit(true);\r\n\r\n        try {\r\n            // Sử dụng API thay vì socket để nộp bài\r\n            const result = await dispatch(summitExam(attemptId)).unwrap();\r\n            console.log(\"Nộp bài thành công:\", result);\r\n\r\n            // Xử lý khi nộp bài thành công\r\n            dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\r\n\r\n            // Thoát fullscreen mà không bắt lỗi\r\n            try {\r\n                exitFullscreen();\r\n            } catch (err) {\r\n                // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\r\n                console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\r\n            }\r\n\r\n            const safeAttemptId = attemptRef.current;\r\n            const currentExam = examRef.current;\r\n\r\n            if (!safeAttemptId) {\r\n                console.error(\"Không có attemptId khi navigate!\");\r\n                return;\r\n            }\r\n\r\n            // Log để debug\r\n            console.log(\"Current exam state:\", currentExam);\r\n            console.log(\"Attempt ID:\", safeAttemptId);\r\n\r\n            if (!currentExam || !currentExam.seeCorrectAnswer) {\r\n                console.log(\"Chuyển về trang danh sách do:\", {\r\n                    examNull: !currentExam,\r\n                    cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\r\n                });\r\n                navigate(`/practice/exam/${examId}`);\r\n                return;\r\n            }\r\n\r\n            navigate(`/practice/exam/attempt/${safeAttemptId}/score`);\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi nộp bài:\", error);\r\n            setLoadingSubmit(false);\r\n            dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\r\n            hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\r\n\r\n            // Thử nộp lại sau 3 giây nếu lỗi xảy ra\r\n            setTimeout(() => {\r\n                if (!loadingSubmit && attemptRef.current) {\r\n                    console.log(\"Thử nộp bài lại sau lỗi...\");\r\n                    handleAutoSubmit();\r\n                }\r\n            }, 5000);\r\n        }\r\n    };\r\n\r\n    // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\r\n    const navigateToQuestion = useCallback((questionId) => {\r\n        setSelectedQuestion(questionId);\r\n\r\n        // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\r\n        if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\r\n            // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\r\n            examContentRef.current.goToQuestionById(questionId);\r\n        } else {\r\n            // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\r\n            // Tìm phần tử câu hỏi bằng querySelector\r\n            setTimeout(() => {\r\n                // Thử tìm phần tử bằng data-question-id\r\n                const element = document.querySelector(`[data-question-id=\"${questionId}\"]`);\r\n\r\n                if (element) {\r\n                    const offset = 80; // chiều cao của header sticky\r\n                    const y = element.getBoundingClientRect().top + window.scrollY - offset;\r\n                    window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                } else {\r\n                    // Fallback: Sử dụng refs\r\n                    const refElement = questionRefs.current[questionId];\r\n\r\n                    if (refElement) {\r\n                        const offset = 80; // chiều cao của header sticky\r\n                        const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\r\n                        window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                    }\r\n                }\r\n            }, 0);\r\n        }\r\n    }, [questionRefs, examContentRef]);\r\n\r\n    // Alias cho navigateToQuestion để tương thích với các component khác\r\n    const scrollToQuestion = navigateToQuestion;\r\n\r\n    const handleSelectAnswerTN = (questionId, statementId, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const newAnswer = {\r\n            questionId,\r\n            answerContent: statementId,\r\n            typeOfQuestion: type,\r\n        };\r\n        dispatch(setAnswers(newAnswer));\r\n\r\n        // Sử dụng API thay vì socket\r\n        dispatch(submitAnswerWithAttempt({\r\n            questionId,\r\n            answerContent: statementId,\r\n            type,\r\n            attemptId\r\n        })).then((result) => {\r\n            if (result.type.endsWith('/fulfilled')) {\r\n                // Answer submitted successfully\r\n                console.log(\"Đã lưu câu trả lời thành công\");\r\n            } else {\r\n                // Handle error\r\n                console.error(\"Lỗi khi lưu câu trả lời:\", result.error);\r\n            }\r\n        });\r\n    };\r\n\r\n    const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const currentAnswers = dsAnswers[questionId] || [];\r\n\r\n        const existing = currentAnswers.find(ans => ans.statementId === statementId);\r\n\r\n        // 🔁 Nếu đáp án đã giống thì không gửi lại\r\n        if (existing && existing.answer === selectedAnswer) {\r\n            return\r\n        }\r\n\r\n        const updatedAnswers = currentAnswers.map(ans =>\r\n            ans.statementId === statementId\r\n                ? { ...ans, answer: selectedAnswer }\r\n                : ans\r\n        );\r\n\r\n        // Nếu chưa có statement này\r\n        if (!existing) {\r\n            updatedAnswers.push({ statementId, answer: selectedAnswer });\r\n        }\r\n\r\n        // ✨ Gửi toàn bộ lên server\r\n        socket.emit(\"select_answer\", {\r\n            questionId,\r\n            answerContent: updatedAnswers,\r\n            studentId: user.id,\r\n            attemptId,\r\n            type: \"DS\",\r\n            examId,\r\n            name: user.lastName + \" \" + user.firstName,\r\n        });\r\n        dispatch(setAnswers({ questionId, answerContent: JSON.stringify(updatedAnswers), typeOfQuestion: \"DS\" }));\r\n    };\r\n\r\n\r\n    const handleSelectAnswerTLN = (questionId, answerContent, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        if (!answerContent || answerContent.trim() === \"\") {\r\n            return;\r\n        }\r\n\r\n        const payload = {\r\n            attemptId,\r\n            questionId,\r\n            answerContent: answerContent.trim().replace(\",\", \".\"),\r\n            studentId: user.id,\r\n            type,\r\n            examId,\r\n            name: user.lastName + \" \" + user.firstName,\r\n        };\r\n\r\n        dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));\r\n\r\n        socket.emit(\"select_answer\", payload);\r\n    }\r\n\r\n    // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\r\n    const questionsToMarkAsSaved = useRef(new Set());\r\n\r\n    // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\r\n    useEffect(() => {\r\n        if (questionsToMarkAsSaved.current.size > 0) {\r\n            questionsToMarkAsSaved.current.forEach(questionId => {\r\n                if (!saveQuestions.has(questionId)) {\r\n                    addQuestion(questionId);\r\n                }\r\n            });\r\n            questionsToMarkAsSaved.current.clear();\r\n        }\r\n    }, [saveQuestions, addQuestion]);\r\n\r\n    // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\r\n    useEffect(() => {\r\n        // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\r\n        const frameId = requestAnimationFrame(() => {\r\n            if (questionsToMarkAsSaved.current.size > 0) {\r\n                const questionIds = [...questionsToMarkAsSaved.current];\r\n                questionsToMarkAsSaved.current.clear();\r\n\r\n                // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\r\n                questionIds.forEach(questionId => {\r\n                    if (!saveQuestions.has(questionId)) {\r\n                        addQuestion(questionId);\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        return () => cancelAnimationFrame(frameId);\r\n    });\r\n\r\n    const isTNSelected = useCallback((questionId, statementId) => {\r\n        const isSelected = answerTN.some(\r\n            (ans) =>\r\n                ans.questionId === questionId &&\r\n                ans.answerContent &&\r\n                String(ans.answerContent) === String(statementId)\r\n        );\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.has(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [answerTN, saveQuestions]);\r\n\r\n    const isDSChecked = useCallback((questionId, statementId, bool) => {\r\n        const isSelected = dsAnswers[questionId]?.some(\r\n            (a) => a.statementId === statementId && a.answer === bool\r\n        ) || false;\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.has(questionId) && dsAnswers[questionId]?.length === 4) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [dsAnswers, saveQuestions]);\r\n\r\n    const getTLNDefaultValue = useCallback((questionId) => {\r\n        const matched = answerTLN.find((ans) => ans.questionId === questionId);\r\n        const content = matched?.answerContent?.replace(/^\"|\"$/g, \"\") || \"\";\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (content && !saveQuestions.has(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return content;\r\n    }, [answerTLN, saveQuestions]);\r\n\r\n    // useEffect(() => {\r\n    //     if (examId) {\r\n    //         dispatch(fetchPublicQuestionsByExamId(examId));\r\n    //     }\r\n    // }, [dispatch, examId]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            setQuestionTN(questions.filter((question) => question.typeOfQuestion === \"TN\"));\r\n            setQuestionDS(questions.filter((question) => question.typeOfQuestion === \"DS\"));\r\n            setQuestionTLN(questions.filter((question) => question.typeOfQuestion === \"TLN\"));\r\n        }\r\n    }, [questions]);\r\n\r\n    useEffect(() => {\r\n        // Kiểm tra answers có phải là mảng không\r\n        if (!Array.isArray(answers) || answers.length === 0) return;\r\n\r\n        const tn = [];\r\n        const tln = [];\r\n        const dsMap = {};\r\n\r\n        // Sử dụng for...of thay vì forEach để tránh lỗi\r\n        for (const answer of answers) {\r\n            if (answer.typeOfQuestion === \"TN\") {\r\n                tn.push(answer);\r\n            } else if (answer.typeOfQuestion === \"TLN\") {\r\n                tln.push(answer);\r\n            } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\r\n                try {\r\n                    const parsed = JSON.parse(answer.answerContent);\r\n                    dsMap[answer.questionId] = parsed;\r\n                } catch (err) {\r\n                    console.error(\"Lỗi parse DS answerContent:\", err);\r\n                }\r\n            }\r\n        }\r\n\r\n        setAnswerTN(tn);\r\n        setAnswerTLN(tln);\r\n        setDsAnswers(dsMap);\r\n        if (!socket || !socket.connected || !attemptId || !examId) return;\r\n        socket.emit(\"calculate_score\", {\r\n            attemptId,\r\n            answers,\r\n            examId,\r\n            student: user,\r\n        });\r\n    }, [answers]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (attemptId) {\r\n            dispatch(fetchAnswersByAttempt(attemptId));\r\n        }\r\n    }, [dispatch, attemptId]);\r\n\r\n    useEffect(() => {\r\n        if (!exam?.testDuration || remainingTime === null || !isAgree) return;\r\n\r\n        // Kiểm tra và hiển thị cảnh báo thời gian\r\n        const checkTimeWarnings = (time) => {\r\n            // Cảnh báo khi còn 5 phút\r\n            if (time === 300 && !timeWarningShown.fiveMinutes) {\r\n                setTimeWarningShown(prev => ({ ...prev, fiveMinutes: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Tắt hiệu ứng nhấp nháy sau 10 giây\r\n                setTimeout(() => {\r\n                    setIsTimeBlinking(false);\r\n                }, 10000);\r\n            }\r\n\r\n            // Cảnh báo khi còn 1 phút\r\n            if (time === 60 && !timeWarningShown.oneMinute) {\r\n                setTimeWarningShown(prev => ({ ...prev, oneMinute: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\r\n            }\r\n        };\r\n\r\n        // Định kỳ yêu cầu thời gian từ server để đồng bộ\r\n        const syncTimeInterval = setInterval(() => {\r\n            if (socket.connected && attemptId) {\r\n                socket.emit(\"request_time\", { examId, attemptId });\r\n            }\r\n        }, 30000); // Đồng bộ thời gian mỗi 30 giây\r\n\r\n        const interval = setInterval(() => {\r\n            dispatch(setRemainingTime((prev) => {\r\n                if (prev <= 1) { // dùng <=1 để đảm bảo không bị âm\r\n                    clearInterval(interval);\r\n                    clearInterval(syncTimeInterval);\r\n                    // Đánh dấu là đã hết thời gian\r\n                    setIsTimeUp(true);\r\n                    setIsTimeBlinking(false);\r\n                    // Thử nộp bài\r\n                    handleAutoSubmit();\r\n                    return 0;\r\n                }\r\n\r\n                // Kiểm tra cảnh báo thời gian\r\n                checkTimeWarnings(prev);\r\n\r\n                return prev - 1;\r\n            }));\r\n        }, 1000);\r\n\r\n        return () => {\r\n            clearInterval(interval);\r\n            clearInterval(syncTimeInterval);\r\n        };\r\n    }, [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, socket, attemptId, examId]);// Chỉ phụ thuộc vào các giá trị cần thiết\r\n\r\n    useEffect(() => {\r\n        if (isAgree && !socket.connected) {\r\n            socket.connect();\r\n        }\r\n        return () => {\r\n            socket.disconnect();\r\n        };\r\n    }, [isAgree]);\r\n\r\n    // frontend\r\n    useEffect(() => {\r\n        if (!attemptId || !user?.id || !examId || attemptId === null || attemptId === undefined) return;\r\n        if (!exam?.isCheatingCheckEnabled) return;\r\n        console.log(\"Đã bật theo dõi hành vi gian lận\");\r\n\r\n\r\n        const recentLogs = new Set(); // chống log lặp\r\n        const logOnce = (key, payload) => {\r\n\r\n            if (!exam?.isCheatingCheckEnabled || recentLogs.has(key)) return;\r\n\r\n            recentLogs.add(key);\r\n            socket.emit(\"user_log\", { ...payload, name: user.lastName + \" \" + user.firstName });\r\n\r\n            setTimeout(() => recentLogs.delete(key), 5000);\r\n        };\r\n\r\n        // 📌 Thoát fullscreen\r\n        const handleFullscreenChange = () => {\r\n            if (!document.fullscreenElement &&\r\n                !document.webkitFullscreenElement &&\r\n                !document.mozFullScreenElement &&\r\n                !document.msFullscreenElement) {\r\n                logOnce(\"exit_fullscreen\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"EF\",\r\n                    action: \"exit_fullscreen\",\r\n                    detail: JSON.stringify({ reason: \"User exited fullscreen mode\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\r\n        const handleVisibilityChange = () => {\r\n            if (document.visibilityState === \"hidden\") {\r\n                logOnce(\"tab_blur\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"TB\",\r\n                    action: \"tab_blur\",\r\n                    detail: JSON.stringify({ message: \"User switched tab or minimized window\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Copy nội dung\r\n        const handleCopy = () => {\r\n            logOnce(\"copy_detected\", {\r\n                studentId: user.id,\r\n                attemptId,\r\n                examId,\r\n                code: \"COP\",\r\n                action: \"copy_detected\",\r\n                detail: JSON.stringify({ message: \"User copied content\" }),\r\n            });\r\n        };\r\n\r\n        // 📌 Phím đáng ngờ\r\n        const handleSuspiciousKey = (e) => {\r\n            const suspiciousKeys = [\r\n                \"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"\r\n            ];\r\n            const combo = `${e.ctrlKey ? \"Ctrl+\" : \"\"}${e.shiftKey ? \"Shift+\" : \"\"}${e.altKey ? \"Alt+\" : \"\"}${e.metaKey ? \"Meta+\" : \"\"}${e.key}`;\r\n\r\n            if (\r\n                suspiciousKeys.includes(e.key) ||\r\n                combo === \"Ctrl+Shift+I\" ||\r\n                combo === \"Ctrl+Shift+C\"\r\n            ) {\r\n                logOnce(`key_${combo}`, {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"SK\",\r\n                    action: \"suspicious_key\",\r\n                    detail: JSON.stringify({ key: e.key, code: e.code, combo }),\r\n                });\r\n            }\r\n        };\r\n\r\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n        document.addEventListener(\"copy\", handleCopy);\r\n        document.addEventListener(\"keydown\", handleSuspiciousKey);\r\n\r\n        return () => {\r\n            document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n            document.removeEventListener(\"copy\", handleCopy);\r\n            document.removeEventListener(\"keydown\", handleSuspiciousKey);\r\n        };\r\n    }, [socket, user.id, examId, attemptId]);\r\n\r\n\r\n    useEffect(() => {\r\n        // Chỉ lắng nghe các sự kiện liên quan đến câu trả lời\r\n        const handleAnswerSaved = ({ questionId }) => {\r\n            addQuestion(questionId);\r\n            removeErrorQuestion(questionId);\r\n        };\r\n\r\n        const handleAnswerError = ({ questionId, message }) => {\r\n            dispatch(setErrorMessage(message));\r\n            removeQuestion(questionId);\r\n            addErrorQuestion(questionId);\r\n        };\r\n\r\n        // Lắng nghe sự kiện cập nhật thời gian từ server\r\n        const handleExamTimer = ({ remainingTime: serverRemainingTime }) => {\r\n            console.log(\"Nhận thời gian từ server:\", serverRemainingTime);\r\n            dispatch(setRemainingTime(serverRemainingTime));\r\n        };\r\n\r\n        // Lắng nghe sự kiện bài thi tự động nộp\r\n        const handleExamAutoSubmitted = ({ message, attemptId: autoSubmitAttemptId, score }) => {\r\n            console.log(\"Bài thi đã tự động nộp:\", { message, autoSubmitAttemptId, score });\r\n            dispatch(setSuccessMessage(message));\r\n            setIsTimeUp(true);\r\n\r\n            // Thoát fullscreen\r\n            try {\r\n                exitFullscreen();\r\n            } catch (err) {\r\n                console.warn(\"Không thể thoát fullscreen khi bài thi tự động nộp:\", err);\r\n            }\r\n\r\n            // Chuyển hướng đến trang kết quả nếu được phép xem đáp án\r\n            if (exam?.seeCorrectAnswer) {\r\n                navigate(`/practice/exam/attempt/${autoSubmitAttemptId}/score`);\r\n            } else {\r\n                navigate(`/practice/exam/${examId}`);\r\n            }\r\n        };\r\n\r\n        // Lắng nghe thông báo từ giáo viên hoặc hệ thống\r\n        const handleExamNotification = ({ message }) => {\r\n            console.log(\"Nhận thông báo:\", message);\r\n            dispatch(setSuccessMessage(message));\r\n        };\r\n\r\n        // Đăng ký các event listeners\r\n        socket.on(\"answer_saved\", handleAnswerSaved);\r\n        socket.on(\"answer_error\", handleAnswerError);\r\n        socket.on(\"exam_timer\", handleExamTimer);\r\n        socket.on(\"exam_auto_submitted\", handleExamAutoSubmitted);\r\n        socket.on(\"exam_notification\", handleExamNotification);\r\n\r\n        return () => {\r\n            // Hủy đăng ký các event listeners\r\n            socket.off(\"answer_saved\", handleAnswerSaved);\r\n            socket.off(\"answer_error\", handleAnswerError);\r\n            socket.off(\"exam_timer\", handleExamTimer);\r\n            socket.off(\"exam_auto_submitted\", handleExamAutoSubmitted);\r\n            socket.off(\"exam_notification\", handleExamNotification);\r\n        };\r\n    }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\r\n\r\n    useEffect(() => {\r\n        localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\r\n    }, [isDarkMode]);\r\n\r\n    // Hàm xử lý chuyển đổi câu hỏi\r\n    const handleKeyDown = useCallback((e) => {\r\n        // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\r\n        if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\r\n            // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\r\n            e.preventDefault();\r\n\r\n            // Nếu không có câu hỏi, thoát khỏi hàm\r\n            if (!questions || questions.length === 0) return;\r\n\r\n            const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\r\n            const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\r\n\r\n            if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\r\n                const prevQuestionId = allQuestions[currentIndex - 1].id;\r\n                console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\r\n                navigateToQuestion(prevQuestionId);\r\n            } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\r\n                const nextQuestionId = allQuestions[currentIndex + 1].id;\r\n                console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\r\n                navigateToQuestion(nextQuestionId);\r\n            }\r\n        }\r\n    }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\r\n    // Lắng nghe sự kiện bàn phím\r\n    useEffect(() => {\r\n        document.addEventListener(\"keydown\", handleKeyDown);\r\n        return () => {\r\n            document.removeEventListener(\"keydown\", handleKeyDown);\r\n        };\r\n    }, [handleKeyDown]);\r\n\r\n    return (\r\n        <div className={`flex flex-col h-full ${isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'}`}>\r\n            <HeaderDoExamPage nameExam={exam?.name} onExitFullscreen={handleExitFullscreen} isDarkMode={!isDarkMode} />\r\n            {isAgree ? (\r\n                <div className=\"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\">\r\n                    {/* Main Content */}\r\n                    <ExamContent\r\n                        ref={examContentRef}\r\n                        loading1={loadingLoadExam}\r\n                        isDarkMode={isDarkMode}\r\n                        questionTN={questionTN}\r\n                        questionDS={questionDS}\r\n                        questionTLN={questionTLN}\r\n                        handlers={{\r\n                            handleSelectAnswerTN,\r\n                            handleSelectAnswerDS,\r\n                            handleSelectAnswerTLN,\r\n                            isTNSelected,\r\n                            isDSChecked,\r\n                            getTLNDefaultValue,\r\n                            setQuestionRef: (id, el) => (questionRefs.current[id] = el),\r\n                            setSelectedQuestion: (id) => setSelectedQuestion(id)\r\n                        }}\r\n                        settings={{\r\n                            selectedQuestion,\r\n                            isDarkMode,\r\n                            fontSize,\r\n                            imageSize,\r\n                            prefixStatementTN,\r\n                            prefixStatementDS,\r\n                            isTimeUp,\r\n                            markedQuestions,\r\n                            toggleMarkQuestion\r\n                        }}\r\n                        isTimeUp={isTimeUp}\r\n                        // Để undefined để component tự quyết định dựa trên thiết bị\r\n                        initialSingleMode={undefined}\r\n                        handleAutoSubmit={handleAutoSubmit}\r\n                        loadingSubmit={loadingSubmit}\r\n                    />\r\n\r\n\r\n                    {/* Button toggle cho mobile */}\r\n                    <div className=\"fixed bottom-4 right-4 z-50 lg:hidden\">\r\n                        <button\r\n                            className={`p-2 rounded-full shadow-md ${isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"}`}\r\n                            onClick={() => setIsSidebarOpen(prev => !prev)}\r\n                        >\r\n                            <Menu />\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Sidebar chính */}\r\n                    <AnimatePresence>\r\n                        {(isSidebarOpen || window.innerWidth > 1024) && (\r\n                            <ExamSidebar\r\n                                isDarkMode={isDarkMode}\r\n                                setIsDarkMode={setIsDarkMode}\r\n                                fontSize={fontSize}\r\n                                handleFontSizeChange={handleFontSizeChange}\r\n                                imageSize={imageSize}\r\n                                handleImageSizeChange={handleImageSizeChange}\r\n                                questionTN={questionTN}\r\n                                questionDS={questionDS}\r\n                                questionTLN={questionTLN}\r\n                                scrollToQuestion={navigateToQuestion}\r\n                                selectedQuestion={selectedQuestion}\r\n                                markedQuestions={markedQuestions}\r\n                                toggleMarkQuestion={toggleMarkQuestion}\r\n                                handleAutoSubmit={handleAutoSubmit}\r\n                                loadingSubmit={loadingSubmit}\r\n                                loadingLoadExam={loadingLoadExam}\r\n                                exam={exam}\r\n                                remainingTime={remainingTime}\r\n                                formatTime={formatTime}\r\n                                questions={questions}\r\n                                singleQuestionMode={examContentRef.current?.isSingleQuestionMode() || false}\r\n                                setSingleQuestionMode={(value) => {\r\n                                    if (examContentRef.current) {\r\n                                        // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\r\n                                        examContentRef.current.setSingleQuestionMode(value);\r\n                                    }\r\n                                }}\r\n                            />\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                </div>\r\n            ) : (\r\n                <div className=\"flex items-center justify-center\">\r\n                    <ExamRegulationModal\r\n                        onClose={() => {\r\n                            if (socket.connected) {\r\n                                socket.emit(\"leave_exam\", { studentId: user?.id, examId });\r\n                                socket.disconnect();\r\n                            }\r\n                            socket.removeAllListeners(); // Xóa hết listener để tránh lỗi khi component bị unmount\r\n                            navigate(`/practice/exam/${examId}`);\r\n                        }}\r\n                        isOpen={!isAgree}\r\n                        onStartExam={handleFullScreen}\r\n                    />\r\n                </div>\r\n            )}\r\n\r\n            {exam?.testDuration && isAgree && (\r\n                <div className={`fixed bottom-2 rounded-md left-2 px-4 py-2\r\n                    ${isTimeBlinking\r\n                        ? 'bg-red-600 animate-pulse'\r\n                        : 'bg-slate-700 bg-opacity-80'}\r\n                    text-white z-50 transition-colors duration-300`}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <div className=\"text-sm font-bold\">{formatTime(remainingTime)} phút</div>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DoExamPage;\r\n"], "mappings": ";;AAAA,OAAOA,gBAAgB,MAAM,6CAA6C;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,uCAAuC;AAC1F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,sCAAsC;AACxF,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,WAAW,MAAM,2CAA2C;AACnE,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gCAAgC;AAChG,SACIC,gBAAgB,EAChBC,UAAU,EACVC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,uBAAuB,EACvBC,SAAS,QACN,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAG3B,SAAS,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAK,CAAC,GAAGrC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAClD,MAAM;IAAEC;EAAU,CAAC,GAAGxC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACE,SAAS,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAGzC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;EACvD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM6C,YAAY,GAAG3C,MAAM,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMkD,UAAU,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMyD,eAAe,GAAGvD,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMwD,OAAO,GAAGxD,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMyD,cAAc,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACZyD,OAAO,CAACE,OAAO,GAAGxB,IAAI;IACtB,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,YAAY,MAAK,KAAK,EAAE;MAC9B1B,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;EAEVnC,SAAS,CAAC,MAAM;IACZ,IAAIgC,MAAM,EAAE;MACRC,QAAQ,CAAC7B,mBAAmB,CAAC4B,MAAM,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,CAACC,QAAQ,EAAED,MAAM,CAAC,CAAC;EAGtB,MAAM;IAAE8B;EAAK,CAAC,GAAGhE,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAAC2B,IAAI,CAAC;EACnD,MAAM;IAAEC,aAAa;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAGpE,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAAC+B,MAAM,CAAC;EAG7F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,IAAIuE,GAAG,CAAC,CAAC,CAAC;EACjE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC;IACrD0E,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM8E,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtF,MAAMC,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEtF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,MAAM;IAC/C,MAAMoF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC5C,CAAC,CAAC;EAEF,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2F,eAAe,EAAEC,kBAAkB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM,CAAC+F,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmG,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuG,SAAS,EAAEC,YAAY,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyG,SAAS,EAAEC,YAAY,CAAC,GAAG1G,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C2G,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAGC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAIC,UAAU,IAAK;IAChC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACL,aAAa,CAAC;IACrC+C,MAAM,CAACC,GAAG,CAACF,UAAU,CAAC;IACtB9E,QAAQ,CAACZ,gBAAgB,CAAC6F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAChDI,mBAAmB,CAACL,UAAU,CAAC;EACnC,CAAC;EAED,MAAMM,gBAAgB,GAAIN,UAAU,IAAK;IACrC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACJ,cAAc,CAAC;IACtC8C,MAAM,CAACC,GAAG,CAACF,UAAU,CAAC;IACtB9E,QAAQ,CAACX,iBAAiB,CAAC4F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;IAC/CM,cAAc,CAACP,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMO,cAAc,GAAIP,UAAU,IAAK;IACnC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACL,aAAa,CAAC;IACrC+C,MAAM,CAACO,MAAM,CAACR,UAAU,CAAC;IACzB9E,QAAQ,CAACZ,gBAAgB,CAAC6F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMI,mBAAmB,GAAIL,UAAU,IAAK;IACxC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACJ,cAAc,CAAC;IACtC8C,MAAM,CAACO,MAAM,CAACR,UAAU,CAAC;IACzB9E,QAAQ,CAACX,iBAAiB,CAAC4F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAIT,UAAU,IAAK;IACvC1C,kBAAkB,CAACoD,IAAI,IAAI;MACvB,MAAMT,MAAM,GAAG,IAAI1C,GAAG,CAACmD,IAAI,CAAC;MAC5B,IAAIT,MAAM,CAACU,GAAG,CAACX,UAAU,CAAC,EAAE;QACxBC,MAAM,CAACO,MAAM,CAACR,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHC,MAAM,CAACC,GAAG,CAACF,UAAU,CAAC;MAC1B;MACA,OAAOC,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAGD,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACA1G,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO2G,GAAG,EAAE;MACV;MACAC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,GAAG,CAAC;IACpD;EACJ,CAAC;EAED,MAAMG,oBAAoB,GAAInB,CAAC,IAAK;IAChCnE,WAAW,CAACuF,MAAM,CAACpB,CAAC,CAACqB,MAAM,CAACC,KAAK,CAAC,CAAC;EACvC,CAAC;EAED,MAAMC,qBAAqB,GAAIvB,CAAC,IAAK;IACjCjE,YAAY,CAACqF,MAAM,CAACpB,CAAC,CAACqB,MAAM,CAACC,KAAK,CAAC,CAAC;EACxC,CAAC;EAED,MAAME,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,MAAMC,GAAG,GAAGJ,MAAM,CAACF,OAAO,GAAG,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjD,UAAA7E,MAAA,CAAUyE,GAAG,OAAAzE,MAAA,CAAI8E,GAAG;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjCjD,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACA;MACA,MAAMkD,QAAQ,GAAG,MAAMC,KAAK,2BAAAjF,MAAA,CAA2B7B,MAAM,GAAI;QAC7D+G,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,YAAAnF,MAAA,CAAYuB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAE;UAC1D,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,MAAM4D,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAI,CAACL,QAAQ,CAACM,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,OAAO,IAAI,0BAA0B,CAAC;MAC/D;;MAEA;MACA,MAAM;QAAEtG,SAAS;QAAEuG;MAAU,CAAC,GAAGL,IAAI;MACrCpB,OAAO,CAAC0B,GAAG,CAAC,+CAA+C,EAAExG,SAAS,CAAC;MAEvED,UAAU,CAAC,IAAI,CAAC;MAChBG,UAAU,CAACU,OAAO,GAAGZ,SAAS;MAC9BC,YAAY,CAACD,SAAS,CAAC;MAEvB,IAAIf,MAAM,EAAE;QACRC,QAAQ,CAAC9B,4BAA4B,CAAC6B,MAAM,CAAC,CAAC;MAClD;MACAuB,aAAa,CAAC+F,SAAS,CAAC;MAExB,IAAI,EAACnH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqH,sBAAsB,GAAE;QAC/B7D,kBAAkB,CAAC,KAAK,CAAC;QACzB;MACJ;MAEA,IAAI;QACA,MAAM8D,OAAO,GAAG,MAAMzI,iBAAiB,CAAC,CAAC;QACzC,IAAIyI,OAAO,EAAE;UACTC,UAAU,CAAC,MAAM;YACb/D,kBAAkB,CAAC,KAAK,CAAC;UAC7B,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,MAAM;UACHkC,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;UACpEnC,kBAAkB,CAAC,KAAK,CAAC;QAC7B;MACJ,CAAC,CAAC,OAAOiC,GAAG,EAAE;QACVC,OAAO,CAAC8B,KAAK,CAAC,2BAA2B,EAAE/B,GAAG,CAAC;QAC/CgC,KAAK,CAAC,yDAAyD,CAAC;QAChEjE,kBAAkB,CAAC,KAAK,CAAC;MAC7B;IAEJ,CAAC,CAAC,OAAOgE,KAAK,EAAE;MACZ9B,OAAO,CAAC8B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1H,QAAQ,CAAC3B,eAAe,CAAC,OAAO,GAAGqJ,KAAK,CAACN,OAAO,CAAC,CAAC;MAClD1D,kBAAkB,CAAC,KAAK,CAAC;MACzBzD,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC;;EAED;;EAEAhC,SAAS,CAAC,MAAM;IACZ,IAAImC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0H,YAAY,IAAIvG,UAAU,EAAE;MAClC,MAAMwG,KAAK,GAAG,IAAIC,IAAI,CAACzG,UAAU,CAAC;MAClC,MAAM0G,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,cAAc,GAAGzB,IAAI,CAACC,KAAK,CAAC,CAACuB,GAAG,GAAGF,KAAK,IAAI,IAAI,CAAC;MACvD,MAAMI,YAAY,GAAG/H,IAAI,CAAC0H,YAAY,GAAG,EAAE;MAC3C,MAAMM,SAAS,GAAG3B,IAAI,CAAC4B,GAAG,CAACF,YAAY,GAAGD,cAAc,EAAE,CAAC,CAAC;MAC5DhI,QAAQ,CAACd,gBAAgB,CAACgJ,SAAS,CAAC,CAAC;;MAErC;MACA,IAAIpH,SAAS,EAAE;QACXd,QAAQ,CAACV,gBAAgB,CAAC;UAAES,MAAM;UAAEe;QAAU,CAAC,CAAC,CAAC,CAC5CsH,IAAI,CAAEC,MAAM,IAAK;UAAA,IAAAC,eAAA;UACd,IAAI,EAAAA,eAAA,GAAAD,MAAM,CAACE,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBvG,aAAa,MAAKyG,SAAS,EAAE;YAC7CxI,QAAQ,CAACd,gBAAgB,CAACmJ,MAAM,CAACE,OAAO,CAACxG,aAAa,CAAC,CAAC;UAC5D;QACJ,CAAC,CAAC,CACD0G,KAAK,CAAEf,KAAK,IAAK;UACd9B,OAAO,CAAC8B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC5D,CAAC,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAACrG,UAAU,EAAEnB,IAAI,EAAEY,SAAS,EAAEf,MAAM,EAAEC,QAAQ,CAAC,CAAC;EAEnDjC,SAAS,CAAC,MAAM;IACZ,IAAIoD,IAAI,EAAE;IACV,IAAI,CAACY,aAAa,EAAEX,OAAO,CAAC,IAAI,CAAC;EACrC,CAAC,EAAE,CAACW,aAAa,CAAC,CAAC;EAEnB,MAAM2G,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAInH,eAAe,CAACG,OAAO,EAAE;MACzBkE,OAAO,CAACC,IAAI,CAAC,sCAAsC,CAAC;MACpD;IACJ;IACAtE,eAAe,CAACG,OAAO,GAAG,IAAI,CAAC,CAAC;IAChCkE,OAAO,CAAC0B,GAAG,CAAC,qBAAqB,EAAExG,SAAS,CAAC;IAC7C,IAAI,CAACA,SAAS,EAAE;MACZ8E,OAAO,CAAC0B,GAAG,CAAC,8EAA8E,CAAC;MAC3F;IACJ;IAEA1B,OAAO,CAAC0B,GAAG,CAAC,6BAA6B,EAAExG,SAAS,CAAC;IACrDd,QAAQ,CAACZ,gBAAgB,CAAC,IAAIiD,GAAG,CAAC,CAAC,CAAC,CAAC;IACrCmB,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACA;MACA,MAAM6E,MAAM,GAAG,MAAMrI,QAAQ,CAACb,UAAU,CAAC2B,SAAS,CAAC,CAAC,CAAC6H,MAAM,CAAC,CAAC;MAC7D/C,OAAO,CAAC0B,GAAG,CAAC,qBAAqB,EAAEe,MAAM,CAAC;;MAE1C;MACArI,QAAQ,CAAC1B,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;;MAElD;MACA,IAAI;QACAU,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,OAAO2G,GAAG,EAAE;QACV;QACAC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAChE;MAEA,MAAMiD,aAAa,GAAG5H,UAAU,CAACU,OAAO;MACxC,MAAMmH,WAAW,GAAGrH,OAAO,CAACE,OAAO;MAEnC,IAAI,CAACkH,aAAa,EAAE;QAChBhD,OAAO,CAAC8B,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACJ;;MAEA;MACA9B,OAAO,CAAC0B,GAAG,CAAC,qBAAqB,EAAEuB,WAAW,CAAC;MAC/CjD,OAAO,CAAC0B,GAAG,CAAC,aAAa,EAAEsB,aAAa,CAAC;MAEzC,IAAI,CAACC,WAAW,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QAC/ClD,OAAO,CAAC0B,GAAG,CAAC,+BAA+B,EAAE;UACzCyB,QAAQ,EAAE,CAACF,WAAW;UACtBG,aAAa,EAAEH,WAAW,IAAI,CAACA,WAAW,CAACC;QAC/C,CAAC,CAAC;QACF7I,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACpC;MACJ;MAEAE,QAAQ,2BAAA2B,MAAA,CAA2BgH,aAAa,WAAQ,CAAC;IAC7D,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACZ9B,OAAO,CAAC8B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxClE,gBAAgB,CAAC,KAAK,CAAC;MACvBxD,QAAQ,CAAC3B,eAAe,CAAC,oCAAoC,CAAC,CAAC;MAC/DkD,eAAe,CAACG,OAAO,GAAG,KAAK,CAAC,CAAC;;MAEjC;MACA+F,UAAU,CAAC,MAAM;QACb,IAAI,CAAClE,aAAa,IAAIvC,UAAU,CAACU,OAAO,EAAE;UACtCkE,OAAO,CAAC0B,GAAG,CAAC,4BAA4B,CAAC;UACzCoB,gBAAgB,CAAC,CAAC;QACtB;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAGhL,WAAW,CAAE6G,UAAU,IAAK;IACnD/B,mBAAmB,CAAC+B,UAAU,CAAC;;IAE/B;IACA,IAAIrD,cAAc,CAACC,OAAO,IAAID,cAAc,CAACC,OAAO,CAACwH,oBAAoB,CAAC,CAAC,EAAE;MACzE;MACAzH,cAAc,CAACC,OAAO,CAACyH,gBAAgB,CAACrE,UAAU,CAAC;IACvD,CAAC,MAAM;MACH;MACA;MACA2C,UAAU,CAAC,MAAM;QACb;QACA,MAAM2B,OAAO,GAAG3E,QAAQ,CAAC4E,aAAa,wBAAAzH,MAAA,CAAuBkD,UAAU,QAAI,CAAC;QAE5E,IAAIsE,OAAO,EAAE;UACT,MAAME,MAAM,GAAG,EAAE,CAAC,CAAC;UACnB,MAAMC,CAAC,GAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;UACvEI,MAAM,CAACE,QAAQ,CAAC;YAAEH,GAAG,EAAEF,CAAC;YAAEM,QAAQ,EAAE;UAAS,CAAC,CAAC;QACnD,CAAC,MAAM;UACH;UACA,MAAMC,UAAU,GAAGnJ,YAAY,CAACe,OAAO,CAACoD,UAAU,CAAC;UAEnD,IAAIgF,UAAU,EAAE;YACZ,MAAMR,MAAM,GAAG,EAAE,CAAC,CAAC;YACnB,MAAMC,CAAC,GAAGO,UAAU,CAACN,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;YAC1EI,MAAM,CAACE,QAAQ,CAAC;cAAEH,GAAG,EAAEF,CAAC;cAAEM,QAAQ,EAAE;YAAS,CAAC,CAAC;UACnD;QACJ;MACJ,CAAC,EAAE,CAAC,CAAC;IACT;EACJ,CAAC,EAAE,CAAClJ,YAAY,EAAEc,cAAc,CAAC,CAAC;;EAElC;EACA,MAAMsI,gBAAgB,GAAGd,kBAAkB;EAE3C,MAAMe,oBAAoB,GAAGA,CAAClF,UAAU,EAAEmF,WAAW,EAAEC,IAAI,KAAK;IAC5D;IACA,IAAIvG,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAM8L,SAAS,GAAG;MACdrF,UAAU;MACVsF,aAAa,EAAEH,WAAW;MAC1BI,cAAc,EAAEH;IACpB,CAAC;IACDlK,QAAQ,CAACvB,UAAU,CAAC0L,SAAS,CAAC,CAAC;;IAE/B;IACAnK,QAAQ,CAACR,uBAAuB,CAAC;MAC7BsF,UAAU;MACVsF,aAAa,EAAEH,WAAW;MAC1BC,IAAI;MACJpJ;IACJ,CAAC,CAAC,CAAC,CAACsH,IAAI,CAAEC,MAAM,IAAK;MACjB,IAAIA,MAAM,CAAC6B,IAAI,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC;QACA1E,OAAO,CAAC0B,GAAG,CAAC,+BAA+B,CAAC;MAChD,CAAC,MAAM;QACH;QACA1B,OAAO,CAAC8B,KAAK,CAAC,0BAA0B,EAAEW,MAAM,CAACX,KAAK,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EAED,MAAM6C,oBAAoB,GAAGA,CAACzF,UAAU,EAAEmF,WAAW,EAAEO,cAAc,KAAK;IACtE;IACA,IAAI7G,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAMoM,cAAc,GAAGlG,SAAS,CAACO,UAAU,CAAC,IAAI,EAAE;IAElD,MAAM4F,QAAQ,GAAGD,cAAc,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACX,WAAW,KAAKA,WAAW,CAAC;;IAE5E;IACA,IAAIS,QAAQ,IAAIA,QAAQ,CAACG,MAAM,KAAKL,cAAc,EAAE;MAChD;IACJ;IAEA,MAAMM,cAAc,GAAGL,cAAc,CAACM,GAAG,CAACH,GAAG,IACzCA,GAAG,CAACX,WAAW,KAAKA,WAAW,GACzB;MAAE,GAAGW,GAAG;MAAEC,MAAM,EAAEL;IAAe,CAAC,GAClCI,GACV,CAAC;;IAED;IACA,IAAI,CAACF,QAAQ,EAAE;MACXI,cAAc,CAACE,IAAI,CAAC;QAAEf,WAAW;QAAEY,MAAM,EAAEL;MAAe,CAAC,CAAC;IAChE;;IAEA;IACAS,MAAM,CAACC,IAAI,CAAC,eAAe,EAAE;MACzBpG,UAAU;MACVsF,aAAa,EAAEU,cAAc;MAC7BK,SAAS,EAAEtJ,IAAI,CAACuJ,EAAE;MAClBtK,SAAS;MACToJ,IAAI,EAAE,IAAI;MACVnK,MAAM;MACNsL,IAAI,EAAExJ,IAAI,CAACyJ,QAAQ,GAAG,GAAG,GAAGzJ,IAAI,CAAC0J;IACrC,CAAC,CAAC;IACFvL,QAAQ,CAACvB,UAAU,CAAC;MAAEqG,UAAU;MAAEsF,aAAa,EAAE/G,IAAI,CAACmI,SAAS,CAACV,cAAc,CAAC;MAAET,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC;EAC7G,CAAC;EAGD,MAAMoB,qBAAqB,GAAGA,CAAC3G,UAAU,EAAEsF,aAAa,EAAEF,IAAI,KAAK;IAC/D;IACA,IAAIvG,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,IAAI,CAAC+L,aAAa,IAAIA,aAAa,CAACsB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/C;IACJ;IAEA,MAAMnD,OAAO,GAAG;MACZzH,SAAS;MACTgE,UAAU;MACVsF,aAAa,EAAEA,aAAa,CAACsB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MACrDR,SAAS,EAAEtJ,IAAI,CAACuJ,EAAE;MAClBlB,IAAI;MACJnK,MAAM;MACNsL,IAAI,EAAExJ,IAAI,CAACyJ,QAAQ,GAAG,GAAG,GAAGzJ,IAAI,CAAC0J;IACrC,CAAC;IAEDvL,QAAQ,CAACvB,UAAU,CAAC;MAAEqG,UAAU;MAAEsF,aAAa;MAAEC,cAAc,EAAEH;IAAK,CAAC,CAAC,CAAC;IAEzEe,MAAM,CAACC,IAAI,CAAC,eAAe,EAAE3C,OAAO,CAAC;EACzC,CAAC;;EAED;EACA,MAAMqD,sBAAsB,GAAG5N,MAAM,CAAC,IAAIqE,GAAG,CAAC,CAAC,CAAC;;EAEhD;EACAtE,SAAS,CAAC,MAAM;IACZ,IAAI6N,sBAAsB,CAAClK,OAAO,CAACmK,IAAI,GAAG,CAAC,EAAE;MACzCD,sBAAsB,CAAClK,OAAO,CAACoK,OAAO,CAAChH,UAAU,IAAI;QACjD,IAAI,CAAC9C,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;UAChCD,WAAW,CAACC,UAAU,CAAC;QAC3B;MACJ,CAAC,CAAC;MACF8G,sBAAsB,CAAClK,OAAO,CAACqK,KAAK,CAAC,CAAC;IAC1C;EACJ,CAAC,EAAE,CAAC/J,aAAa,EAAE6C,WAAW,CAAC,CAAC;;EAEhC;EACA9G,SAAS,CAAC,MAAM;IACZ;IACA,MAAMiO,OAAO,GAAGC,qBAAqB,CAAC,MAAM;MACxC,IAAIL,sBAAsB,CAAClK,OAAO,CAACmK,IAAI,GAAG,CAAC,EAAE;QACzC,MAAMK,WAAW,GAAG,CAAC,GAAGN,sBAAsB,CAAClK,OAAO,CAAC;QACvDkK,sBAAsB,CAAClK,OAAO,CAACqK,KAAK,CAAC,CAAC;;QAEtC;QACAG,WAAW,CAACJ,OAAO,CAAChH,UAAU,IAAI;UAC9B,IAAI,CAAC9C,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;YAChCD,WAAW,CAACC,UAAU,CAAC;UAC3B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IAEF,OAAO,MAAMqH,oBAAoB,CAACH,OAAO,CAAC;EAC9C,CAAC,CAAC;EAEF,MAAMI,YAAY,GAAGnO,WAAW,CAAC,CAAC6G,UAAU,EAAEmF,WAAW,KAAK;IAC1D,MAAMoC,UAAU,GAAGlI,QAAQ,CAACmI,IAAI,CAC3B1B,GAAG,IACAA,GAAG,CAAC9F,UAAU,KAAKA,UAAU,IAC7B8F,GAAG,CAACR,aAAa,IACjB9D,MAAM,CAACsE,GAAG,CAACR,aAAa,CAAC,KAAK9D,MAAM,CAAC2D,WAAW,CACxD,CAAC;;IAED;IACA,IAAIoC,UAAU,IAAI,CAACrK,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;MAC9C8G,sBAAsB,CAAClK,OAAO,CAACsD,GAAG,CAACF,UAAU,CAAC;IAClD;IAEA,OAAOuH,UAAU;EACrB,CAAC,EAAE,CAAClI,QAAQ,EAAEnC,aAAa,CAAC,CAAC;EAE7B,MAAMuK,WAAW,GAAGtO,WAAW,CAAC,CAAC6G,UAAU,EAAEmF,WAAW,EAAEuC,IAAI,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC/D,MAAML,UAAU,GAAG,EAAAI,qBAAA,GAAAlI,SAAS,CAACO,UAAU,CAAC,cAAA2H,qBAAA,uBAArBA,qBAAA,CAAuBH,IAAI,CACzCK,CAAC,IAAKA,CAAC,CAAC1C,WAAW,KAAKA,WAAW,IAAI0C,CAAC,CAAC9B,MAAM,KAAK2B,IACzD,CAAC,KAAI,KAAK;;IAEV;IACA,IAAIH,UAAU,IAAI,CAACrK,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,IAAI,EAAA4H,sBAAA,GAAAnI,SAAS,CAACO,UAAU,CAAC,cAAA4H,sBAAA,uBAArBA,sBAAA,CAAuBE,MAAM,MAAK,CAAC,EAAE;MACrFhB,sBAAsB,CAAClK,OAAO,CAACsD,GAAG,CAACF,UAAU,CAAC;IAClD;IAEA,OAAOuH,UAAU;EACrB,CAAC,EAAE,CAAC9H,SAAS,EAAEvC,aAAa,CAAC,CAAC;EAE9B,MAAM6K,kBAAkB,GAAG5O,WAAW,CAAE6G,UAAU,IAAK;IAAA,IAAAgI,qBAAA;IACnD,MAAMC,OAAO,GAAG1I,SAAS,CAACsG,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAAC9F,UAAU,KAAKA,UAAU,CAAC;IACtE,MAAMkI,OAAO,GAAG,CAAAD,OAAO,aAAPA,OAAO,wBAAAD,qBAAA,GAAPC,OAAO,CAAE3C,aAAa,cAAA0C,qBAAA,uBAAtBA,qBAAA,CAAwBnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAI,EAAE;;IAEnE;IACA,IAAIqB,OAAO,IAAI,CAAChL,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;MAC3C8G,sBAAsB,CAAClK,OAAO,CAACsD,GAAG,CAACF,UAAU,CAAC;IAClD;IAEA,OAAOkI,OAAO;EAClB,CAAC,EAAE,CAAC3I,SAAS,EAAErC,aAAa,CAAC,CAAC;;EAE9B;EACA;EACA;EACA;EACA;;EAEAjE,SAAS,CAAC,MAAM;IACZ,IAAIsC,SAAS,EAAE;MACXyD,aAAa,CAACzD,SAAS,CAAC4M,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAAC7C,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/ErG,aAAa,CAAC3D,SAAS,CAAC4M,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAAC7C,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/EnG,cAAc,CAAC7D,SAAS,CAAC4M,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAAC7C,cAAc,KAAK,KAAK,CAAC,CAAC;IACrF;EACJ,CAAC,EAAE,CAAChK,SAAS,CAAC,CAAC;EAEftC,SAAS,CAAC,MAAM;IACZ;IACA,IAAI,CAACkH,KAAK,CAACkI,OAAO,CAAC7M,OAAO,CAAC,IAAIA,OAAO,CAACsM,MAAM,KAAK,CAAC,EAAE;IAErD,MAAMQ,EAAE,GAAG,EAAE;IACb,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,KAAK,MAAMzC,MAAM,IAAIvK,OAAO,EAAE;MAC1B,IAAIuK,MAAM,CAACR,cAAc,KAAK,IAAI,EAAE;QAChC+C,EAAE,CAACpC,IAAI,CAACH,MAAM,CAAC;MACnB,CAAC,MAAM,IAAIA,MAAM,CAACR,cAAc,KAAK,KAAK,EAAE;QACxCgD,GAAG,CAACrC,IAAI,CAACH,MAAM,CAAC;MACpB,CAAC,MAAM,IAAIA,MAAM,CAACR,cAAc,KAAK,IAAI,IAAIQ,MAAM,CAACT,aAAa,EAAE;QAC/D,IAAI;UACA,MAAMmD,MAAM,GAAGlK,IAAI,CAACC,KAAK,CAACuH,MAAM,CAACT,aAAa,CAAC;UAC/CkD,KAAK,CAACzC,MAAM,CAAC/F,UAAU,CAAC,GAAGyI,MAAM;QACrC,CAAC,CAAC,OAAO5H,GAAG,EAAE;UACVC,OAAO,CAAC8B,KAAK,CAAC,6BAA6B,EAAE/B,GAAG,CAAC;QACrD;MACJ;IACJ;IAEAvB,WAAW,CAACgJ,EAAE,CAAC;IACf9I,YAAY,CAAC+I,GAAG,CAAC;IACjB7I,YAAY,CAAC8I,KAAK,CAAC;IACnB,IAAI,CAACrC,MAAM,IAAI,CAACA,MAAM,CAACuC,SAAS,IAAI,CAAC1M,SAAS,IAAI,CAACf,MAAM,EAAE;IAC3DkL,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAE;MAC3BpK,SAAS;MACTR,OAAO;MACPP,MAAM;MACN0N,OAAO,EAAE5L;IACb,CAAC,CAAC;EACN,CAAC,EAAE,CAACvB,OAAO,CAAC,CAAC;EAGbvC,SAAS,CAAC,MAAM;IACZ,IAAI+C,SAAS,EAAE;MACXd,QAAQ,CAACxB,qBAAqB,CAACsC,SAAS,CAAC,CAAC;IAC9C;EACJ,CAAC,EAAE,CAACd,QAAQ,EAAEc,SAAS,CAAC,CAAC;EAEzB/C,SAAS,CAAC,MAAM;IACZ,IAAI,EAACmC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0H,YAAY,KAAI7F,aAAa,KAAK,IAAI,IAAI,CAACnB,OAAO,EAAE;;IAE/D;IACA,MAAM8M,iBAAiB,GAAIC,IAAI,IAAK;MAChC;MACA,IAAIA,IAAI,KAAK,GAAG,IAAI,CAACrL,gBAAgB,CAACE,WAAW,EAAE;QAC/CD,mBAAmB,CAACiD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEhD,WAAW,EAAE;QAAK,CAAC,CAAC,CAAC;QAC7DG,iBAAiB,CAAC,IAAI,CAAC;QACvB3C,QAAQ,CAAC3B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;QACAoJ,UAAU,CAAC,MAAM;UACb9E,iBAAiB,CAAC,KAAK,CAAC;QAC5B,CAAC,EAAE,KAAK,CAAC;MACb;;MAEA;MACA,IAAIgL,IAAI,KAAK,EAAE,IAAI,CAACrL,gBAAgB,CAACG,SAAS,EAAE;QAC5CF,mBAAmB,CAACiD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE/C,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC;QAC3DE,iBAAiB,CAAC,IAAI,CAAC;QACvB3C,QAAQ,CAAC3B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;MACJ;IACJ,CAAC;;IAED;IACA,MAAMuP,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACvC,IAAI5C,MAAM,CAACuC,SAAS,IAAI1M,SAAS,EAAE;QAC/BmK,MAAM,CAACC,IAAI,CAAC,cAAc,EAAE;UAAEnL,MAAM;UAAEe;QAAU,CAAC,CAAC;MACtD;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,MAAMgN,QAAQ,GAAGD,WAAW,CAAC,MAAM;MAC/B7N,QAAQ,CAACd,gBAAgB,CAAEsG,IAAI,IAAK;QAChC,IAAIA,IAAI,IAAI,CAAC,EAAE;UAAE;UACbuI,aAAa,CAACD,QAAQ,CAAC;UACvBC,aAAa,CAACH,gBAAgB,CAAC;UAC/B;UACAhK,WAAW,CAAC,IAAI,CAAC;UACjBjB,iBAAiB,CAAC,KAAK,CAAC;UACxB;UACA+F,gBAAgB,CAAC,CAAC;UAClB,OAAO,CAAC;QACZ;;QAEA;QACAgF,iBAAiB,CAAClI,IAAI,CAAC;QAEvB,OAAOA,IAAI,GAAG,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACTuI,aAAa,CAACD,QAAQ,CAAC;MACvBC,aAAa,CAACH,gBAAgB,CAAC;IACnC,CAAC;EACL,CAAC,EAAE,CAAC1N,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0H,YAAY,EAAEhH,OAAO,EAAEmB,aAAa,EAAEO,gBAAgB,EAAEtC,QAAQ,EAAEiL,MAAM,EAAEnK,SAAS,EAAEf,MAAM,CAAC,CAAC,CAAC;;EAExGhC,SAAS,CAAC,MAAM;IACZ,IAAI6C,OAAO,IAAI,CAACqK,MAAM,CAACuC,SAAS,EAAE;MAC9BvC,MAAM,CAAC+C,OAAO,CAAC,CAAC;IACpB;IACA,OAAO,MAAM;MACT/C,MAAM,CAACgD,UAAU,CAAC,CAAC;IACvB,CAAC;EACL,CAAC,EAAE,CAACrN,OAAO,CAAC,CAAC;;EAEb;EACA7C,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC+C,SAAS,IAAI,EAACe,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuJ,EAAE,KAAI,CAACrL,MAAM,IAAIe,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK0H,SAAS,EAAE;IACzF,IAAI,EAACtI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqH,sBAAsB,GAAE;IACnC3B,OAAO,CAAC0B,GAAG,CAAC,kCAAkC,CAAC;IAG/C,MAAM4G,UAAU,GAAG,IAAI7L,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM8L,OAAO,GAAGA,CAACC,GAAG,EAAE7F,OAAO,KAAK;MAE9B,IAAI,EAACrI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqH,sBAAsB,KAAI2G,UAAU,CAACzI,GAAG,CAAC2I,GAAG,CAAC,EAAE;MAE1DF,UAAU,CAAClJ,GAAG,CAACoJ,GAAG,CAAC;MACnBnD,MAAM,CAACC,IAAI,CAAC,UAAU,EAAE;QAAE,GAAG3C,OAAO;QAAE8C,IAAI,EAAExJ,IAAI,CAACyJ,QAAQ,GAAG,GAAG,GAAGzJ,IAAI,CAAC0J;MAAU,CAAC,CAAC;MAEnF9D,UAAU,CAAC,MAAMyG,UAAU,CAAC5I,MAAM,CAAC8I,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC;;IAED;IACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAAC5J,QAAQ,CAAC6J,iBAAiB,IAC3B,CAAC7J,QAAQ,CAAC8J,uBAAuB,IACjC,CAAC9J,QAAQ,CAAC+J,oBAAoB,IAC9B,CAAC/J,QAAQ,CAACgK,mBAAmB,EAAE;QAC/BN,OAAO,CAAC,iBAAiB,EAAE;UACvBhD,SAAS,EAAEtJ,IAAI,CAACuJ,EAAE;UAClBtK,SAAS;UACTf,MAAM;UACN2O,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,iBAAiB;UACzBC,MAAM,EAAEvL,IAAI,CAACmI,SAAS,CAAC;YAAEqD,MAAM,EAAE;UAA8B,CAAC;QACpE,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAIrK,QAAQ,CAACsK,eAAe,KAAK,QAAQ,EAAE;QACvCZ,OAAO,CAAC,UAAU,EAAE;UAChBhD,SAAS,EAAEtJ,IAAI,CAACuJ,EAAE;UAClBtK,SAAS;UACTf,MAAM;UACN2O,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,UAAU;UAClBC,MAAM,EAAEvL,IAAI,CAACmI,SAAS,CAAC;YAAEpE,OAAO,EAAE;UAAwC,CAAC;QAC/E,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAM4H,UAAU,GAAGA,CAAA,KAAM;MACrBb,OAAO,CAAC,eAAe,EAAE;QACrBhD,SAAS,EAAEtJ,IAAI,CAACuJ,EAAE;QAClBtK,SAAS;QACTf,MAAM;QACN2O,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAEvL,IAAI,CAACmI,SAAS,CAAC;UAAEpE,OAAO,EAAE;QAAsB,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC;;IAED;IACA,MAAM6H,mBAAmB,GAAItK,CAAC,IAAK;MAC/B,MAAMuK,cAAc,GAAG,CACnB,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CACjE;MACD,MAAMC,KAAK,MAAAvN,MAAA,CAAM+C,CAAC,CAACyK,OAAO,GAAG,OAAO,GAAG,EAAE,EAAAxN,MAAA,CAAG+C,CAAC,CAAC0K,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAAzN,MAAA,CAAG+C,CAAC,CAAC2K,MAAM,GAAG,MAAM,GAAG,EAAE,EAAA1N,MAAA,CAAG+C,CAAC,CAAC4K,OAAO,GAAG,OAAO,GAAG,EAAE,EAAA3N,MAAA,CAAG+C,CAAC,CAACyJ,GAAG,CAAE;MAEpI,IACIc,cAAc,CAACM,QAAQ,CAAC7K,CAAC,CAACyJ,GAAG,CAAC,IAC9Be,KAAK,KAAK,cAAc,IACxBA,KAAK,KAAK,cAAc,EAC1B;QACEhB,OAAO,QAAAvM,MAAA,CAAQuN,KAAK,GAAI;UACpBhE,SAAS,EAAEtJ,IAAI,CAACuJ,EAAE;UAClBtK,SAAS;UACTf,MAAM;UACN2O,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,gBAAgB;UACxBC,MAAM,EAAEvL,IAAI,CAACmI,SAAS,CAAC;YAAE4C,GAAG,EAAEzJ,CAAC,CAACyJ,GAAG;YAAEM,IAAI,EAAE/J,CAAC,CAAC+J,IAAI;YAAES;UAAM,CAAC;QAC9D,CAAC,CAAC;MACN;IACJ,CAAC;IAED1K,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE2J,sBAAsB,CAAC;IACrE5J,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEoK,sBAAsB,CAAC;IACrErK,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEsK,UAAU,CAAC;IAC7CvK,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEuK,mBAAmB,CAAC;IAEzD,OAAO,MAAM;MACTxK,QAAQ,CAACgL,mBAAmB,CAAC,kBAAkB,EAAEpB,sBAAsB,CAAC;MACxE5J,QAAQ,CAACgL,mBAAmB,CAAC,kBAAkB,EAAEX,sBAAsB,CAAC;MACxErK,QAAQ,CAACgL,mBAAmB,CAAC,MAAM,EAAET,UAAU,CAAC;MAChDvK,QAAQ,CAACgL,mBAAmB,CAAC,SAAS,EAAER,mBAAmB,CAAC;IAChE,CAAC;EACL,CAAC,EAAE,CAAChE,MAAM,EAAEpJ,IAAI,CAACuJ,EAAE,EAAErL,MAAM,EAAEe,SAAS,CAAC,CAAC;EAGxC/C,SAAS,CAAC,MAAM;IACZ;IACA,MAAM2R,iBAAiB,GAAGC,IAAA,IAAoB;MAAA,IAAnB;QAAE7K;MAAW,CAAC,GAAA6K,IAAA;MACrC9K,WAAW,CAACC,UAAU,CAAC;MACvBK,mBAAmB,CAACL,UAAU,CAAC;IACnC,CAAC;IAED,MAAM8K,iBAAiB,GAAGC,KAAA,IAA6B;MAAA,IAA5B;QAAE/K,UAAU;QAAEsC;MAAQ,CAAC,GAAAyI,KAAA;MAC9C7P,QAAQ,CAAC3B,eAAe,CAAC+I,OAAO,CAAC,CAAC;MAClC/B,cAAc,CAACP,UAAU,CAAC;MAC1BM,gBAAgB,CAACN,UAAU,CAAC;IAChC,CAAC;;IAED;IACA,MAAMgL,eAAe,GAAGC,KAAA,IAA4C;MAAA,IAA3C;QAAEhO,aAAa,EAAEiO;MAAoB,CAAC,GAAAD,KAAA;MAC3DnK,OAAO,CAAC0B,GAAG,CAAC,2BAA2B,EAAE0I,mBAAmB,CAAC;MAC7DhQ,QAAQ,CAACd,gBAAgB,CAAC8Q,mBAAmB,CAAC,CAAC;IACnD,CAAC;;IAED;IACA,MAAMC,uBAAuB,GAAGC,KAAA,IAAwD;MAAA,IAAvD;QAAE9I,OAAO;QAAEtG,SAAS,EAAEqP,mBAAmB;QAAEC;MAAM,CAAC,GAAAF,KAAA;MAC/EtK,OAAO,CAAC0B,GAAG,CAAC,yBAAyB,EAAE;QAAEF,OAAO;QAAE+I,mBAAmB;QAAEC;MAAM,CAAC,CAAC;MAC/EpQ,QAAQ,CAAC1B,iBAAiB,CAAC8I,OAAO,CAAC,CAAC;MACpCxD,WAAW,CAAC,IAAI,CAAC;;MAEjB;MACA,IAAI;QACA5E,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,OAAO2G,GAAG,EAAE;QACVC,OAAO,CAACC,IAAI,CAAC,qDAAqD,EAAEF,GAAG,CAAC;MAC5E;;MAEA;MACA,IAAIzF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4I,gBAAgB,EAAE;QACxB7I,QAAQ,2BAAA2B,MAAA,CAA2BuO,mBAAmB,WAAQ,CAAC;MACnE,CAAC,MAAM;QACHlQ,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;MACxC;IACJ,CAAC;;IAED;IACA,MAAMsQ,sBAAsB,GAAGC,KAAA,IAAiB;MAAA,IAAhB;QAAElJ;MAAQ,CAAC,GAAAkJ,KAAA;MACvC1K,OAAO,CAAC0B,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;MACvCpH,QAAQ,CAAC1B,iBAAiB,CAAC8I,OAAO,CAAC,CAAC;IACxC,CAAC;;IAED;IACA6D,MAAM,CAACsF,EAAE,CAAC,cAAc,EAAEb,iBAAiB,CAAC;IAC5CzE,MAAM,CAACsF,EAAE,CAAC,cAAc,EAAEX,iBAAiB,CAAC;IAC5C3E,MAAM,CAACsF,EAAE,CAAC,YAAY,EAAET,eAAe,CAAC;IACxC7E,MAAM,CAACsF,EAAE,CAAC,qBAAqB,EAAEN,uBAAuB,CAAC;IACzDhF,MAAM,CAACsF,EAAE,CAAC,mBAAmB,EAAEF,sBAAsB,CAAC;IAEtD,OAAO,MAAM;MACT;MACApF,MAAM,CAACuF,GAAG,CAAC,cAAc,EAAEd,iBAAiB,CAAC;MAC7CzE,MAAM,CAACuF,GAAG,CAAC,cAAc,EAAEZ,iBAAiB,CAAC;MAC7C3E,MAAM,CAACuF,GAAG,CAAC,YAAY,EAAEV,eAAe,CAAC;MACzC7E,MAAM,CAACuF,GAAG,CAAC,qBAAqB,EAAEP,uBAAuB,CAAC;MAC1DhF,MAAM,CAACuF,GAAG,CAAC,mBAAmB,EAAEH,sBAAsB,CAAC;IAC3D,CAAC;EACL,CAAC,EAAE,CAACnQ,IAAI,EAAEH,MAAM,EAAEE,QAAQ,EAAED,QAAQ,EAAE6E,WAAW,EAAEQ,cAAc,EAAEF,mBAAmB,EAAEC,gBAAgB,CAAC,CAAC;EAE1GrH,SAAS,CAAC,MAAM;IACZoF,YAAY,CAACsN,OAAO,CAAC,YAAY,EAAEpN,IAAI,CAACmI,SAAS,CAACxI,UAAU,CAAC,CAAC;EAClE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM0N,aAAa,GAAGzS,WAAW,CAAE0G,CAAC,IAAK;IACrC;IACA,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC6K,QAAQ,CAAC7K,CAAC,CAACyJ,GAAG,CAAC,EAAE;MACrE;MACAzJ,CAAC,CAACC,cAAc,CAAC,CAAC;;MAElB;MACA,IAAI,CAACvE,SAAS,IAAIA,SAAS,CAACuM,MAAM,KAAK,CAAC,EAAE;MAE1C,MAAM+D,YAAY,GAAG,CAAC,GAAG9M,UAAU,EAAE,GAAGE,UAAU,EAAE,GAAGE,WAAW,CAAC;MACnE,MAAM2M,YAAY,GAAGD,YAAY,CAACE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC1F,EAAE,KAAKtI,gBAAgB,CAAC;MAE3E,IAAI,CAAC6B,CAAC,CAACyJ,GAAG,KAAK,SAAS,IAAIzJ,CAAC,CAACyJ,GAAG,KAAK,WAAW,KAAKwC,YAAY,GAAG,CAAC,EAAE;QACpE,MAAMG,cAAc,GAAGJ,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACxF,EAAE;QACxDxF,OAAO,CAAC0B,GAAG,CAAC,+CAA+C,EAAEyJ,cAAc,CAAC;QAC5E9H,kBAAkB,CAAC8H,cAAc,CAAC;MACtC,CAAC,MAAM,IAAI,CAACpM,CAAC,CAACyJ,GAAG,KAAK,WAAW,IAAIzJ,CAAC,CAACyJ,GAAG,KAAK,YAAY,KAAKwC,YAAY,GAAGD,YAAY,CAAC/D,MAAM,GAAG,CAAC,EAAE;QACpG,MAAMoE,cAAc,GAAGL,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACxF,EAAE;QACxDxF,OAAO,CAAC0B,GAAG,CAAC,kDAAkD,EAAE0J,cAAc,CAAC;QAC/E/H,kBAAkB,CAAC+H,cAAc,CAAC;MACtC;IACJ;EACJ,CAAC,EAAE,CAAC3Q,SAAS,EAAEwD,UAAU,EAAEE,UAAU,EAAEE,WAAW,EAAEnB,gBAAgB,EAAEmG,kBAAkB,CAAC,CAAC;EAC1F;EACAlL,SAAS,CAAC,MAAM;IACZ0G,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEgM,aAAa,CAAC;IACnD,OAAO,MAAM;MACTjM,QAAQ,CAACgL,mBAAmB,CAAC,SAAS,EAAEiB,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACI/Q,OAAA;IAAKsR,SAAS,0BAAArP,MAAA,CAA0BoB,UAAU,GAAG,yBAAyB,GAAG,uBAAuB,CAAG;IAAAkO,QAAA,gBACvGvR,OAAA,CAAChC,gBAAgB;MAACwT,QAAQ,EAAEjR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmL,IAAK;MAAC+F,gBAAgB,EAAE1L,oBAAqB;MAAC1C,UAAU,EAAE,CAACA;IAAW;MAAAqO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1G5Q,OAAO,gBACJjB,OAAA;MAAKsR,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAEhFvR,OAAA,CAACb,WAAW;QACR2S,GAAG,EAAEhQ,cAAe;QACpBiQ,QAAQ,EAAEjO,eAAgB;QAC1BT,UAAU,EAAEA,UAAW;QACvBa,UAAU,EAAEA,UAAW;QACvBE,UAAU,EAAEA,UAAW;QACvBE,WAAW,EAAEA,WAAY;QACzB0N,QAAQ,EAAE;UACN3H,oBAAoB;UACpBO,oBAAoB;UACpBkB,qBAAqB;UACrBW,YAAY;UACZG,WAAW;UACXM,kBAAkB;UAClB+E,cAAc,EAAEA,CAACxG,EAAE,EAAEyG,EAAE,KAAMlR,YAAY,CAACe,OAAO,CAAC0J,EAAE,CAAC,GAAGyG,EAAG;UAC3D9O,mBAAmB,EAAGqI,EAAE,IAAKrI,mBAAmB,CAACqI,EAAE;QACvD,CAAE;QACF0G,QAAQ,EAAE;UACNhP,gBAAgB;UAChBE,UAAU;UACVzC,QAAQ;UACRE,SAAS;UACTmC,iBAAiB;UACjBC,iBAAiB;UACjBc,QAAQ;UACRxB,eAAe;UACfoD;QACJ,CAAE;QACF5B,QAAQ,EAAEA;QACV;QAAA;QACAoO,iBAAiB,EAAEvJ,SAAU;QAC7BE,gBAAgB,EAAEA,gBAAiB;QACnCnF,aAAa,EAAEA;MAAc;QAAA8N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAIF7R,OAAA;QAAKsR,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDvR,OAAA;UACIsR,SAAS,gCAAArP,MAAA,CAAgCoB,UAAU,GAAG,wBAAwB,GAAG,qBAAqB,CAAG;UACzGgP,OAAO,EAAEA,CAAA,KAAM9Q,gBAAgB,CAACsE,IAAI,IAAI,CAACA,IAAI,CAAE;UAAA0L,QAAA,eAE/CvR,OAAA,CAACf,IAAI;YAAAyS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGN7R,OAAA,CAAChB,eAAe;QAAAuS,QAAA,EACX,CAACjQ,aAAa,IAAIyI,MAAM,CAACuI,UAAU,GAAG,IAAI,kBACvCtS,OAAA,CAACd,WAAW;UACRmE,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7B1C,QAAQ,EAAEA,QAAS;UACnBuF,oBAAoB,EAAEA,oBAAqB;UAC3CrF,SAAS,EAAEA,SAAU;UACrByF,qBAAqB,EAAEA,qBAAsB;UAC7CrC,UAAU,EAAEA,UAAW;UACvBE,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzB8F,gBAAgB,EAAEd,kBAAmB;UACrCnG,gBAAgB,EAAEA,gBAAiB;UACnCX,eAAe,EAAEA,eAAgB;UACjCoD,kBAAkB,EAAEA,kBAAmB;UACvCmD,gBAAgB,EAAEA,gBAAiB;UACnCnF,aAAa,EAAEA,aAAc;UAC7BE,eAAe,EAAEA,eAAgB;UACjCvD,IAAI,EAAEA,IAAK;UACX6B,aAAa,EAAEA,aAAc;UAC7BoE,UAAU,EAAEA,UAAW;UACvB9F,SAAS,EAAEA,SAAU;UACrB6R,kBAAkB,EAAE,EAAApS,qBAAA,GAAA2B,cAAc,CAACC,OAAO,cAAA5B,qBAAA,uBAAtBA,qBAAA,CAAwBoJ,oBAAoB,CAAC,CAAC,KAAI,KAAM;UAC5EiJ,qBAAqB,EAAGlM,KAAK,IAAK;YAC9B,IAAIxE,cAAc,CAACC,OAAO,EAAE;cACxB;cACAD,cAAc,CAACC,OAAO,CAACyQ,qBAAqB,CAAClM,KAAK,CAAC;YACvD;UACJ;QAAE;UAAAoL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CAAC,gBAEN7R,OAAA;MAAKsR,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC7CvR,OAAA,CAACjB,mBAAmB;QAChB0T,OAAO,EAAEA,CAAA,KAAM;UACX,IAAInH,MAAM,CAACuC,SAAS,EAAE;YAClBvC,MAAM,CAACC,IAAI,CAAC,YAAY,EAAE;cAAEC,SAAS,EAAEtJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuJ,EAAE;cAAErL;YAAO,CAAC,CAAC;YAC1DkL,MAAM,CAACgD,UAAU,CAAC,CAAC;UACvB;UACAhD,MAAM,CAACoH,kBAAkB,CAAC,CAAC,CAAC,CAAC;UAC7BpS,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACxC,CAAE;QACFuS,MAAM,EAAE,CAAC1R,OAAQ;QACjB2R,WAAW,EAAE5L;MAAiB;QAAA0K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA,CAAAtR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0H,YAAY,KAAIhH,OAAO,iBAC1BjB,OAAA;MAAKsR,SAAS,qEAAArP,MAAA,CACRc,cAAc,GACV,0BAA0B,GAC1B,4BAA4B,yEACc;MAAAwO,QAAA,eAChDvR,OAAA;QAAKsR,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCvR,OAAA;UAAKsR,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAAE/K,UAAU,CAACpE,aAAa,CAAC,EAAC,UAAK;QAAA;UAAAsP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEA,CAAC;AAEd,CAAC;AAAC3R,EAAA,CAn7BID,UAAU;EAAA,QACOxB,SAAS,EACXR,WAAW,EACXW,WAAW,EACXV,WAAW,EACNA,WAAW,EACbA,WAAW,EA4BdA,WAAW,EAC6BA,WAAW;AAAA;AAAA2U,EAAA,GAnClE5S,UAAU;AAq7BhB,eAAeA,UAAU;AAAC,IAAA4S,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}