import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as doExamApi from "../../services/doExamApi";
import { apiHandler } from "../../utils/apiHandler";

export const joinExam = createAsyncThunk(
    "doExam/joinExam",
    async (examId, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.joinExamApi, examId, null, false, false, false, false);
    }
);

export const submitAnswer = createAsyncThunk(
    "doExam/submitAnswer",
    async ({ questionId, answerContent, type }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.submitAnswerApi, { questionId, answerContent, type }, null, false, false, false, false);
    }
);

export const calculateScore = createAsyncThunk(
    "doExam/calculateScore",
    async ({ attemptId, answers }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.calculateScoreApi, { attemptId, answers }, null, false, false, false, false);
    }
);

export const summitExam = createAsyncThunk(
    "doExam/summitExam",
    async (attemptId, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.summitExamAPI, { attemptId }, () => { }, true, false);
    }
);

export const getRemainingTime = createAsyncThunk(
    "doExam/getRemainingTime",
    async ({ examId, attemptId }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.getRemainingTimeApi, { examId, attemptId }, null, false, false, false, false);
    }
);

export const logUserActivity = createAsyncThunk(
    "doExam/logUserActivity",
    async ({ examId, attemptId, activityType, details }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.logUserActivityApi, { examId, attemptId, activityType, details }, null, false, false, false, false);
    }
);

export const submitAnswerWithAttempt = createAsyncThunk(
    "doExam/submitAnswerWithAttempt",
    async ({ questionId, answerContent, type, attemptId }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.submitAnswerWithAttemptApi, { questionId, answerContent, type, attemptId }, null, false, false, false, false);
    }
);

export const leaveExam = createAsyncThunk(
    "doExam/leaveExam",
    async ({ examId, attemptId }, { dispatch }) => {
        return await apiHandler(dispatch, doExamApi.leaveExamApi, { examId, attemptId }, null, false, false, false, false);
    }
);

const doExamSlice = createSlice({
    name: "doExam",
    initialState: {
        attemptId: null,
        loadingJoin: false,
        loadingSubmitAnswer: false,
        loadingCalculate: false,
        loadingSubmit: false,
        loadingTime: false,
        loadingActivity: false,
        loadingLeave: false,
        isSubmit: false,
        startTime: null,
        saveQuestions: [], // Changed from Set to Array
        errorQuestions: [], // Changed from Set to Array
        remainingTime: null,
    },
    reducers: {
        setAttemptId: (state, action) => {
            state.attemptId = action.payload;
        },
        setStartTime: (state, action) => {
            state.startTime = action.payload;
        },
        setSaveQuestions: (state, action) => {
            // Ensure unique values when setting
            state.saveQuestions = Array.isArray(action.payload)
                ? [...new Set(action.payload)]
                : [];
            
            console.log("setSaveQuestions", state.saveQuestions)
        },
        setErrorQuestions: (state, action) => {
            // Ensure unique values when setting
            // console.log(action.payload)
            state.errorQuestions = Array.isArray(action.payload)
                ? [...new Set(action.payload)]
                : [];

            console.log("setErrorQuestions", state.errorQuestions)
        },
        setRemainingTime: (state, action) => {
            state.remainingTime = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(joinExam.pending, (state) => {
                state.loadingJoin = true;
            })
            .addCase(joinExam.fulfilled, (state, action) => {
                state.attemptId = action.payload.attemptId;
                state.loadingJoin = false;
            })
            .addCase(joinExam.rejected, (state) => {
                state.loadingJoin = false;
            })
            .addCase(submitAnswer.pending, (state) => {
                state.loadingSubmitAnswer = true;
            })
            .addCase(submitAnswer.fulfilled, (state, action) => {
                state.loadingSubmitAnswer = false;
            })
            .addCase(submitAnswer.rejected, (state) => {
                state.loadingSubmitAnswer = false;
            })
            .addCase(calculateScore.pending, (state) => {
                state.loadingCalculate = true;
            })
            .addCase(calculateScore.fulfilled, (state, action) => {
                // Handle score calculation result
                state.loadingCalculate = false;
            })
            .addCase(calculateScore.rejected, (state) => {
                state.loadingCalculate = false;
            })
            .addCase(summitExam.pending, (state) => {
                state.loadingSubmit = true;
            })
            .addCase(summitExam.fulfilled, (state, action) => {
                state.loadingSubmit = false;
                state.isSubmit = true;
            })
            .addCase(summitExam.rejected, (state) => {
                state.loadingSubmit = false;
                state.isSubmit = false;
            })
            // getRemainingTime
            .addCase(getRemainingTime.pending, (state) => {
                state.loadingTime = true;
            })
            .addCase(getRemainingTime.fulfilled, (state, action) => {
                state.loadingTime = false;
                if (action.payload?.data?.remainingTime !== undefined) {
                    state.remainingTime = action.payload.data.remainingTime;
                }
            })
            .addCase(getRemainingTime.rejected, (state) => {
                state.loadingTime = false;
            })
            // logUserActivity
            .addCase(logUserActivity.pending, (state) => {
                state.loadingActivity = true;
            })
            .addCase(logUserActivity.fulfilled, (state) => {
                state.loadingActivity = false;
            })
            .addCase(logUserActivity.rejected, (state) => {
                state.loadingActivity = false;
            })
            // submitAnswerWithAttempt
            .addCase(submitAnswerWithAttempt.pending, (state) => {
                state.loadingSubmitAnswer = true;
            })
            .addCase(submitAnswerWithAttempt.fulfilled, (state, action) => {
                state.loadingSubmitAnswer = false;
                // Add questionId to saveQuestions if successful
                if (action.payload?.data?.questionId) {
                    const questionId = action.payload.data.questionId;

                    // Add to saveQuestions if not already there
                    if (!state.saveQuestions.includes(questionId)) {
                        state.saveQuestions.push(questionId);
                        console.log(`✅ API Success: Added questionId ${questionId} to saveQuestions`);
                    }

                    // Remove from errorQuestions if it was there
                    const wasInError = state.errorQuestions.includes(questionId);
                    state.errorQuestions = state.errorQuestions.filter(id => id !== questionId);
                    if (wasInError) {
                        console.log(`✅ API Success: Removed questionId ${questionId} from errorQuestions`);
                    }
                }
            })
            .addCase(submitAnswerWithAttempt.rejected, (state, action) => {
                state.loadingSubmitAnswer = false;
                // Add questionId to errorQuestions if failed
                if (action.meta?.arg?.questionId) {
                    const questionId = action.meta.arg.questionId;

                    // Add to errorQuestions if not already there
                    if (!state.errorQuestions.includes(questionId)) {
                        state.errorQuestions.push(questionId);
                        console.log(`❌ API Failed: Added questionId ${questionId} to errorQuestions`);
                    }

                    // Remove from saveQuestions if it was there
                    const wasInSave = state.saveQuestions.includes(questionId);
                    state.saveQuestions = state.saveQuestions.filter(id => id !== questionId);
                    if (wasInSave) {
                        console.log(`❌ API Failed: Removed questionId ${questionId} from saveQuestions`);
                    }
                }
            })
            // leaveExam
            .addCase(leaveExam.pending, (state) => {
                state.loadingLeave = true;
            })
            .addCase(leaveExam.fulfilled, (state) => {
                state.loadingLeave = false;
            })
            .addCase(leaveExam.rejected, (state) => {
                state.loadingLeave = false;
            })
    },
});

export const {
    setAttemptId,
    setStartTime,
    setSaveQuestions,
    setErrorQuestions,
    setRemainingTime,

} = doExamSlice.actions;

export default doExamSlice.reducer;
