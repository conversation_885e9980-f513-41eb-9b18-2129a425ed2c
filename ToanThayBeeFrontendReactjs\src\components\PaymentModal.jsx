import React, { useState } from 'react';
import { X, Copy, Check, QrCode } from 'lucide-react';
// import QRCode from 'qrcode.react';

const PaymentModal = ({ isOpen, onClose, paymentInfo }) => {
  const [copiedAccount, setCopiedAccount] = useState(null);
  const [activeTab, setActiveTab] = useState('mbbank');

  if (!isOpen) return null;

  const bankAccounts = [
    {
      id: 'mbbank',
      name: 'MB Bank',
      accountName: 'Ong K<PERSON>ắ<PERSON> Ng<PERSON>',
      accountNumber: '**********',
      qrValue: `Ong Khac Ngoc - MB Bank - ********** - ${paymentInfo?.description || 'Thanh toan hoc phi'}`
    },
    {
      id: 'vietcombank',
      name: 'Vietcombank',
      accountName: '<PERSON>uyễ<PERSON>h<PERSON> Thu Hiền',
      accountNumber: '*************',
      qrValue: `<PERSON><PERSON><PERSON> - Vietcombank - ************* - ${paymentInfo?.description || 'Thanh toan hoc phi'}`
    }
  ];

  const handleCopy = (text, accountId) => {
    navigator.clipboard.writeText(text);
    setCopiedAccount(accountId);
    setTimeout(() => setCopiedAccount(null), 2000);
  };

  const activeBank = bankAccounts.find(bank => bank.id === activeTab);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-800">Thông tin thanh toán</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Payment Info */}
          {paymentInfo && (
            <div className="mb-6 bg-blue-50 p-4 rounded-lg space-y-4">
              <h3 className="font-medium text-gray-800 mb-2">Thông tin học phí</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Tháng:</p>
                  <p className="font-medium">{paymentInfo.month}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Số tiền cần thanh toán:</p>
                  <p className="font-medium text-red-600">{paymentInfo.amount}</p>
                </div>

              </div>
              <div>
                <p className="text-sm text-gray-500">Nội dung chuyển khoản:</p>
                <div className="flex items-center gap-2">
                  <p className="font-medium">{paymentInfo.description}</p>
                  <button
                    onClick={() => handleCopy(paymentInfo.description, 'description')}
                    className="text-blue-600 hover:text-blue-800"
                    title="Sao chép"
                  >
                    {copiedAccount === 'description' ? <Check size={16} /> : <Copy size={16} />}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Bank Tabs */}
          <div className="flex border-b border-gray-200 mb-4">
            {bankAccounts.map(bank => (
              <button
                key={bank.id}
                onClick={() => setActiveTab(bank.id)}
                className={`px-4 py-2 text-sm font-medium ${activeTab === bank.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
                  }`}
              >
                {bank.name}
              </button>
            ))}
          </div>

          {/* Bank Account Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-800 mb-4">Thông tin tài khoản</h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">Ngân hàng:</p>
                  <p className="font-medium">{activeBank.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Tên tài khoản:</p>
                  <p className="font-medium">{activeBank.accountName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Số tài khoản:</p>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{activeBank.accountNumber}</p>
                    <button
                      onClick={() => handleCopy(activeBank.accountNumber, activeBank.id)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Sao chép số tài khoản"
                    >
                      {copiedAccount === activeBank.id ? <Check size={16} /> : <Copy size={16} />}
                    </button>
                  </div>
                </div>

              </div>

            </div>


            <div className="flex flex-col items-center justify-center">
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                {/* <QRCode 
                  value={activeBank.qrValue}
                  size={180}
                  level="H"
                  includeMargin={true}
                  renderAs="svg"
                /> */}
              </div>
              <p className="mt-2 text-sm text-gray-500 text-center">Quét mã QR để thanh toán</p>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-sm text-gray-500">Nội dung chuyển khoản:</p>
            <p className="font-medium">{paymentInfo?.description || 'Thanh toán học phí'}</p>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Lưu ý:</strong> Sau khi chuyển khoản, vui lòng chụp màn hình biên lai và gửi cho giáo viên qua Zalo để xác nhận thanh toán. Số điện thoại: **********.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
