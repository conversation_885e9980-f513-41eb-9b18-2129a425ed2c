{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\sidebar\\\\MarkableQuestionButton.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Bookmark } from 'lucide-react';\nimport { useSelector } from 'react-redux';\n/**\n * Button component for navigating to a specific question with mark feature\n *\n * @param {Object} props - Component props\n * @param {string|number} props.questionId - ID of the question\n * @param {number} props.index - Index of the question (for display)\n * @param {Function} props.scrollToQuestion - Function to scroll to the question\n * @param {string|number|null} props.selectedQuestion - ID of the currently selected question\n * @param {Set} props.markedQuestions - Set of marked question IDs\n * @param {Function} props.toggleMarkQuestion - Function to toggle mark status\n * @param {boolean} props.isDarkMode - Whether dark mode is enabled\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MarkableQuestionButton = _ref => {\n  _s();\n  let {\n    questionId,\n    index,\n    scrollToQuestion,\n    selectedQuestion,\n    markedQuestions,\n    toggleMarkQuestion,\n    isDarkMode\n  } = _ref;\n  const {\n    saveQuestions,\n    errorQuestions\n  } = useSelector(state => state.doExam);\n\n  // Determine button style based on question state\n  const getButtonStyle = () => {\n    if (selectedQuestion === questionId) {\n      return isDarkMode ? \"bg-yellow-600 text-white\" : \"bg-yellow-400 text-black\";\n    } else if (errorQuestions.includes(questionId)) {\n      return isDarkMode ? \"bg-red-600 text-white\" : \"bg-red-500 text-white\";\n    } else if (saveQuestions.includes(questionId)) {\n      return isDarkMode ? \"bg-green-600 text-white\" : \"bg-green-500 text-white\";\n    } else {\n      return isDarkMode ? \"bg-gray-700 hover:bg-gray-600 text-white\" : \"bg-sky-100 hover:bg-sky-300 text-black\";\n    }\n  };\n  const isMarked = markedQuestions.has(questionId);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => scrollToQuestion(questionId),\n      className: \"w-8 h-8 rounded text-sm font-bold flex items-center justify-center transition-colors \".concat(getButtonStyle()),\n      \"aria-label\": \"Go to question \".concat(index + 1),\n      children: index + 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: e => {\n        e.stopPropagation();\n        toggleMarkQuestion(questionId);\n      },\n      className: \"absolute -top-2 -right-2 w-5 h-5 flex items-center justify-center rounded-full\\n          \".concat(isMarked ? isDarkMode ? 'text-sky-400' : 'text-sky-600' : isDarkMode ? 'text-gray-500' : 'text-gray-400'),\n      title: isMarked ? \"Bỏ đánh dấu\" : \"Đánh dấu để xem lại\",\n      children: /*#__PURE__*/_jsxDEV(Bookmark, {\n        size: 14,\n        fill: isMarked ? \"currentColor\" : \"none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(MarkableQuestionButton, \"nPBnIC9GeQAOAjGXVOZfnMmi1HA=\", false, function () {\n  return [useSelector];\n});\n_c = MarkableQuestionButton;\nexport default MarkableQuestionButton;\nvar _c;\n$RefreshReg$(_c, \"MarkableQuestionButton\");", "map": {"version": 3, "names": ["React", "Bookmark", "useSelector", "jsxDEV", "_jsxDEV", "MarkableQuestionButton", "_ref", "_s", "questionId", "index", "scrollToQuestion", "selectedQuestion", "markedQuestions", "toggleMarkQuestion", "isDarkMode", "saveQuestions", "errorQuestions", "state", "doExam", "getButtonStyle", "includes", "isMarked", "has", "className", "children", "onClick", "concat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "e", "stopPropagation", "title", "size", "fill", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/sidebar/MarkableQuestionButton.jsx"], "sourcesContent": ["import React from 'react';\nimport { Bookmark } from 'lucide-react';\nimport { useSelector } from 'react-redux';\n/**\n * Button component for navigating to a specific question with mark feature\n *\n * @param {Object} props - Component props\n * @param {string|number} props.questionId - ID of the question\n * @param {number} props.index - Index of the question (for display)\n * @param {Function} props.scrollToQuestion - Function to scroll to the question\n * @param {string|number|null} props.selectedQuestion - ID of the currently selected question\n * @param {Set} props.markedQuestions - Set of marked question IDs\n * @param {Function} props.toggleMarkQuestion - Function to toggle mark status\n * @param {boolean} props.isDarkMode - Whether dark mode is enabled\n */\nconst MarkableQuestionButton = ({\n  questionId,\n  index,\n  scrollToQuestion,\n  selectedQuestion,\n  markedQuestions,\n  toggleMarkQuestion,\n  isDarkMode\n}) => {\n  const { saveQuestions, errorQuestions } = useSelector(state => state.doExam);\n\n  // Determine button style based on question state\n  const getButtonStyle = () => {\n    if (selectedQuestion === questionId) {\n      return isDarkMode\n        ? \"bg-yellow-600 text-white\"\n        : \"bg-yellow-400 text-black\";\n    } else if (errorQuestions.includes(questionId)) {\n      return isDarkMode\n        ? \"bg-red-600 text-white\"\n        : \"bg-red-500 text-white\";\n    } else if (saveQuestions.includes(questionId)) {\n      return isDarkMode\n        ? \"bg-green-600 text-white\"\n        : \"bg-green-500 text-white\";\n    } else {\n      return isDarkMode\n        ? \"bg-gray-700 hover:bg-gray-600 text-white\"\n        : \"bg-sky-100 hover:bg-sky-300 text-black\";\n    }\n  };\n\n  const isMarked = markedQuestions.has(questionId);\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => scrollToQuestion(questionId)}\n        className={`w-8 h-8 rounded text-sm font-bold flex items-center justify-center transition-colors ${getButtonStyle()}`}\n        aria-label={`Go to question ${index + 1}`}\n      >\n        {index + 1}\n      </button>\n\n      {/* Bookmark icon for marking questions */}\n      <button\n        onClick={(e) => {\n          e.stopPropagation();\n          toggleMarkQuestion(questionId);\n        }}\n        className={`absolute -top-2 -right-2 w-5 h-5 flex items-center justify-center rounded-full\n          ${isMarked\n            ? (isDarkMode ? 'text-sky-400' : 'text-sky-600')\n            : (isDarkMode ? 'text-gray-500' : 'text-gray-400')}`}\n        title={isMarked ? \"Bỏ đánh dấu\" : \"Đánh dấu để xem lại\"}\n      >\n        <Bookmark size={14} fill={isMarked ? \"currentColor\" : \"none\"} />\n      </button>\n    </div>\n  );\n};\n\nexport default MarkableQuestionButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,WAAW,QAAQ,aAAa;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAAC,MAAA,IAAAC,OAAA;AAYA,MAAMC,sBAAsB,GAAGC,IAAA,IAQzB;EAAAC,EAAA;EAAA,IAR0B;IAC9BC,UAAU;IACVC,KAAK;IACLC,gBAAgB;IAChBC,gBAAgB;IAChBC,eAAe;IACfC,kBAAkB;IAClBC;EACF,CAAC,GAAAR,IAAA;EACC,MAAM;IAAES,aAAa;IAAEC;EAAe,CAAC,GAAGd,WAAW,CAACe,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;;EAE5E;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIR,gBAAgB,KAAKH,UAAU,EAAE;MACnC,OAAOM,UAAU,GACb,0BAA0B,GAC1B,0BAA0B;IAChC,CAAC,MAAM,IAAIE,cAAc,CAACI,QAAQ,CAACZ,UAAU,CAAC,EAAE;MAC9C,OAAOM,UAAU,GACb,uBAAuB,GACvB,uBAAuB;IAC7B,CAAC,MAAM,IAAIC,aAAa,CAACK,QAAQ,CAACZ,UAAU,CAAC,EAAE;MAC7C,OAAOM,UAAU,GACb,yBAAyB,GACzB,yBAAyB;IAC/B,CAAC,MAAM;MACL,OAAOA,UAAU,GACb,0CAA0C,GAC1C,wCAAwC;IAC9C;EACF,CAAC;EAED,MAAMO,QAAQ,GAAGT,eAAe,CAACU,GAAG,CAACd,UAAU,CAAC;EAEhD,oBACEJ,OAAA;IAAKmB,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACvBpB,OAAA;MACEqB,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAACF,UAAU,CAAE;MAC5Ce,SAAS,0FAAAG,MAAA,CAA0FP,cAAc,CAAC,CAAC,CAAG;MACtH,gCAAAO,MAAA,CAA8BjB,KAAK,GAAG,CAAC,CAAG;MAAAe,QAAA,EAEzCf,KAAK,GAAG;IAAC;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGT1B,OAAA;MACEqB,OAAO,EAAGM,CAAC,IAAK;QACdA,CAAC,CAACC,eAAe,CAAC,CAAC;QACnBnB,kBAAkB,CAACL,UAAU,CAAC;MAChC,CAAE;MACFe,SAAS,+FAAAG,MAAA,CACLL,QAAQ,GACLP,UAAU,GAAG,cAAc,GAAG,cAAc,GAC5CA,UAAU,GAAG,eAAe,GAAG,eAAgB,CAAG;MACzDmB,KAAK,EAAEZ,QAAQ,GAAG,aAAa,GAAG,qBAAsB;MAAAG,QAAA,eAExDpB,OAAA,CAACH,QAAQ;QAACiC,IAAI,EAAE,EAAG;QAACC,IAAI,EAAEd,QAAQ,GAAG,cAAc,GAAG;MAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvB,EAAA,CA5DIF,sBAAsB;EAAA,QASgBH,WAAW;AAAA;AAAAkC,EAAA,GATjD/B,sBAAsB;AA8D5B,eAAeA,sBAAsB;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}