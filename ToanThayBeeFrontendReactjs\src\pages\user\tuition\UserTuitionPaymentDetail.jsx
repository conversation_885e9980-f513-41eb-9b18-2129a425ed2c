import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import {
  fetchUserTuitionPaymentById,
  clearTuitionPayment,
  fetchStudentClassTuitionsByMonth
} from "src/features/tuition/tuitionSlice";
import { formatCurrency } from "src/utils/formatters";
import UserLayout from "src/layouts/UserLayout";
import PaymentModal from "src/components/PaymentModal";
import {
  CreditCard,
  ArrowLeft,
  FileText,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  Receipt,
  Loader,
  ChevronRight
} from "lucide-react";

const UserTuitionPaymentDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { tuitionPayment, loading, studentClassTuitions } = useSelector((state) => state.tuition);
  const [paymentProgress, setPaymentProgress] = useState(0);
  const [classTuitionsLoading, setClassTuitionsLoading] = useState(false);

  // State cho modal thanh toán
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState(null);

  useEffect(() => {
    dispatch(fetchUserTuitionPaymentById(id));

    return () => {
      dispatch(clearTuitionPayment());
    };
  }, [dispatch, id]);

  useEffect(() => {
    if (tuitionPayment) {
      setClassTuitionsLoading(true);
      dispatch(fetchStudentClassTuitionsByMonth(tuitionPayment.month))
        .unwrap()
        .then(() => {
          setClassTuitionsLoading(false);
        })
        .catch((error) => {
          console.error("Error fetching class tuitions:", error);
          setClassTuitionsLoading(false);
        });
    }
  }, [dispatch, tuitionPayment]);

  useEffect(() => {
    if (tuitionPayment) {
      const progress = tuitionPayment.expectedAmount > 0
        ? (tuitionPayment.paidAmount / tuitionPayment.expectedAmount) * 100
        : 0;
      setPaymentProgress(progress);
    }
  }, [tuitionPayment]);

  const handleOpenPaymentModal = () => {
    if (!tuitionPayment) return;

    setPaymentInfo({
      id: tuitionPayment.id,
      month: tuitionPayment.monthFormatted,
      amount: formatCurrency(tuitionPayment.expectedAmount - tuitionPayment.paidAmount),
      description: `${user.firstName} ${user.lastName} ${user.highSchool} HP_${tuitionPayment.monthFormatted.replace(' ', '_')}_${tuitionPayment.id}`
    });
    setIsPaymentModalOpen(true);
  };

  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setPaymentInfo(null);
  };

  const getStatusBadge = (status, isOverdue) => {
    if (status === "PAID") {
      return (
        <span className="px-3 py-1 rounded-full text-sm bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle size={16} />
          Đã thanh toán
        </span>
      );
    } else if (isOverdue) {
      return (
        <span className="px-3 py-1 rounded-full text-sm bg-red-100 text-red-800 flex items-center gap-1">
          <AlertCircle size={16} />
          Quá hạn
        </span>
      );
    } else if (status === "PARTIAL") {
      return (
        <span className="px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 flex items-center gap-1">
          <DollarSign size={16} />
          Thanh toán một phần
        </span>
      );
    } else {
      return (
        <span className="px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800 flex items-center gap-1">
          <CreditCard size={16} />
          Chưa thanh toán
        </span>
      );
    }
  };

  const getPaymentStatusIcon = (status, isOverdue) => {
    if (status === "PAID") {
      return (
        <div className="p-4 bg-green-100 rounded-full">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
      );
    } else if (isOverdue) {
      return (
        <div className="p-4 bg-red-100 rounded-full">
          <AlertCircle className="w-8 h-8 text-red-600" />
        </div>
      );
    } else if (status === "PARTIAL") {
      return (
        <div className="p-4 bg-blue-100 rounded-full">
          <DollarSign className="w-8 h-8 text-blue-600" />
        </div>
      );
    } else {
      return (
        <div className="p-4 bg-yellow-100 rounded-full">
          <CreditCard className="w-8 h-8 text-yellow-600" />
        </div>
      );
    }
  };

  if (loading) {
    return (
      <UserLayout>
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="p-8 text-center text-gray-500">
            <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
            <p>Đang tải thông tin học phí...</p>
          </div>
        </div>
      </UserLayout>
    );
  }

  if (!tuitionPayment) {
    return (
      <UserLayout>
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="p-8 text-center text-gray-500">
            <AlertCircle size={40} className="mx-auto mb-4 text-gray-300" />
            <p>Không tìm thấy thông tin học phí.</p>
            <button
              onClick={() => navigate("/tuition-payments")}
              className="mt-4 px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 transition-colors"
            >
              Quay lại danh sách học phí
            </button>
          </div>
        </div>
      </UserLayout>
    );
  }

  return (
    <UserLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Breadcrumb */}
        <div className="flex items-center mb-6 text-sm">
          <button
            onClick={() => navigate("/tuition-payments")}
            className="text-gray-500 hover:text-sky-600 flex items-center gap-1"
          >
            Danh sách học phí
          </button>
          <ChevronRight size={16} className="mx-2 text-gray-400" />
          <span className="text-gray-700">Chi tiết học phí</span>
        </div>

        {/* Tiêu đề */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">
            Chi tiết học phí {tuitionPayment.monthFormatted}
          </h1>
          {getStatusBadge(tuitionPayment.status, tuitionPayment.isOverdue)}
        </div>

        {/* Thông tin chính */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6 flex gap-6">
            {getPaymentStatusIcon(tuitionPayment.status, tuitionPayment.isOverdue)}
            <div className="flex-1">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Số tiền học phí</p>
                  <p className="text-xl font-semibold">{formatCurrency(tuitionPayment.expectedAmount)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Đã thanh toán</p>
                  <p className="text-xl font-semibold text-green-600">{formatCurrency(tuitionPayment.paidAmount)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Còn lại</p>
                  <p className="text-xl font-semibold text-red-600">{formatCurrency(tuitionPayment.remainingAmount)}</p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm text-gray-500 mb-2">Tiến độ thanh toán</p>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${tuitionPayment.status === "PAID" ? "bg-green-600" : tuitionPayment.isOverdue ? "bg-red-600" : "bg-blue-600"}`}
                    style={{ width: `${paymentProgress}%` }}
                  ></div>
                </div>
                <p className="text-right text-sm text-gray-500 mt-1">{paymentProgress.toFixed(0)}%</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Hạn thanh toán</p>
                  <p className="text-base flex items-center gap-1">
                    <Calendar size={16} className="text-gray-400" />
                    {tuitionPayment.dueDateFormatted || "Chưa có hạn thanh toán"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Ngày thanh toán</p>
                  <p className="text-base flex items-center gap-1">
                    <Calendar size={16} className="text-gray-400" />
                    {tuitionPayment.paymentDateFormatted || "Chưa thanh toán"}
                  </p>
                </div>
              </div>

              {tuitionPayment.note && (
                <div className="mt-6 p-4 bg-gray-50 rounded-md">
                  <p className="text-sm text-gray-500 mb-1">Ghi chú</p>
                  <p className="text-base">{tuitionPayment.note}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Danh sách học phí theo lớp */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <FileText size={20} className="text-sky-600" />
              Chi tiết học phí theo lớp
            </h2>

            {classTuitionsLoading ? (
              <div className="py-8 text-center">
                <Loader size={30} className="mx-auto mb-4 text-sky-500 animate-spin" />
                <p className="text-gray-500">Đang tải danh sách học phí...</p>
              </div>
            ) : !studentClassTuitions || !studentClassTuitions.classTuitions || studentClassTuitions.classTuitions.length === 0 ? (
              <div className="py-8 text-center">
                <AlertCircle size={30} className="mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">Không có dữ liệu học phí cho tháng này</p>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  {studentClassTuitions.classTuitions.map((tuition) => (
                    <div key={tuition.id} className="border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow">
                      <div className="flex justify-between items-center mb-3">
                        <h3 className="font-medium text-gray-800">{tuition.className}</h3>
                        <span className="bg-sky-100 text-sky-800 text-xs px-2 py-1 rounded-full">
                          Lớp {tuition.classGrade}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Học phí:</p>
                          <p className="font-medium text-gray-800">{formatCurrency(tuition.amount)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Ngày tham gia:</p>
                          <p className="font-medium text-gray-800 flex items-center gap-1">
                            <Calendar size={14} className="text-gray-400" />
                            {new Date(tuition.joinDate).toLocaleDateString('vi-VN')}
                          </p>
                        </div>
                        {tuition.note && (
                          <div>
                            <p className="text-gray-500">Ghi chú:</p>
                            <p className="font-medium text-gray-800">{tuition.note}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 p-4 bg-sky-50 rounded-lg">
                  <div className="flex flex-col md:flex-row justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Tổng học phí các lớp:</p>
                      <p className="text-lg font-semibold text-sky-700">{formatCurrency(studentClassTuitions.totalAmount || 0)}</p>
                    </div>
                    <div className="mt-3 md:mt-0">
                      <p className="text-sm text-gray-500">Tháng:</p>
                      <p className="text-lg font-semibold text-sky-700">{studentClassTuitions.monthFormatted}</p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Các tùy chọn */}
        <div className="flex justify-end gap-3">
          <button
            onClick={() => navigate("/tuition-payments")}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            Quay lại
          </button>

          {tuitionPayment.status !== "PAID" && (
            <button
              onClick={handleOpenPaymentModal}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center gap-2"
            >
              <Receipt size={16} />
              Thanh toán
            </button>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handleClosePaymentModal}
        paymentInfo={paymentInfo}
      />
    </UserLayout>
  );
};

export default UserTuitionPaymentDetail;
