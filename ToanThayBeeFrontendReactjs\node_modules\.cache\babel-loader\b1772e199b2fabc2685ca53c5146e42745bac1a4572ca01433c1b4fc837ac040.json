{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\sidebar\\\\ExamSidebar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport SettingsButton from './SettingsButton';\nimport ThemeToggleButton from './ThemeToggleButton';\nimport SizeSlider from './SizeSlider';\nimport QuestionSection from './QuestionSection';\nimport QuestionCounter from './QuestionCounter';\nimport SubmitButton from './SubmitButton';\nimport TimeDisplay from './TimeDisplay';\nimport ViewModeToggle from './ViewModeToggle';\nimport ProgressBar from './ProgressBar';\nimport NetworkSpeedTest from '../NetworkSpeedTest';\nimport { BookmarkCheck, List, RefreshCw } from 'lucide-react';\nimport { fetchPublicQuestionsByExamId } from 'src/features/question/questionSlice';\nimport { useDispatch, useSelector } from 'react-redux';\n\n/**\r\n * Main sidebar component for exam interface\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ExamSidebar = _ref => {\n  _s();\n  let {\n    isDarkMode,\n    setIsDarkMode,\n    fontSize,\n    handleFontSizeChange,\n    imageSize,\n    handleImageSizeChange,\n    questionTN,\n    questionDS,\n    questionTLN,\n    scrollToQuestion,\n    selectedQuestion,\n    markedQuestions = new Set(),\n    toggleMarkQuestion = () => {},\n    handleAutoSubmit,\n    loadingSubmit,\n    loadingLoadExam,\n    exam,\n    remainingTime,\n    formatTime,\n    questions,\n    savingQuestions = new Set(),\n    singleQuestionMode = false,\n    setSingleQuestionMode = () => {}\n  } = _ref;\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMarkedOnly, setShowMarkedOnly] = useState(false);\n  const dispatch = useDispatch();\n  const {\n    saveQuestions,\n    errorQuestions\n  } = useSelector(state => state.doExam);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      x: \"100%\"\n    },\n    animate: {\n      x: 0\n    },\n    exit: {\n      x: \"100%\"\n    },\n    transition: {\n      type: \"spring\",\n      stiffness: 300,\n      damping: 30\n    },\n    className: \"fixed top-0 right-0 w-11/12 sm:w-2/3 md:w-1/3 h-full \".concat(isDarkMode ? 'bg-gray-800' : 'bg-white', \" p-4 z-30 shadow-lg overflow-y-auto lg:sticky lg:shadow-none lg:h-[90vh] lg:top-20 lg:right-0\"),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between w-full\",\n      children: [/*#__PURE__*/_jsxDEV(SettingsButton, {\n        showSettings: showSettings,\n        setShowSettings: setShowSettings,\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NetworkSpeedTest, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transition-all duration-500 overflow-hidden \".concat(showSettings ? \"max-h-[500px] opacity-100\" : \"max-h-0 opacity-0\"),\n      children: [/*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \"my-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold\",\n          children: \"Ch\\u1EBF \\u0111\\u1ED9:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ThemeToggleButton, {\n          isDarkMode: isDarkMode,\n          setIsDarkMode: setIsDarkMode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \"my-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(SizeSlider, {\n          label: \"Ch\\u1EC9nh c\\u1EE1 ch\\u1EEF\",\n          value: fontSize,\n          onChange: handleFontSizeChange,\n          min: 12,\n          max: 24,\n          isDarkMode: isDarkMode,\n          unit: \"px\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SizeSlider, {\n          label: \"Ch\\u1EC9nh c\\u1EE1 \\u1EA3nh\",\n          value: imageSize,\n          onChange: handleImageSizeChange,\n          min: 6,\n          max: 20,\n          isDarkMode: isDarkMode,\n          unit: \"rem\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n        className: \"my-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ViewModeToggle, {\n        singleQuestionMode: singleQuestionMode,\n        setSingleQuestionMode: setSingleQuestionMode,\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold\",\n          children: \"Load l\\u1EA1i: \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => dispatch(fetchPublicQuestionsByExamId(exam.id)),\n          className: \"flex items-center gap-2 px-3 py-1 rounded-md transition-colors \".concat(isDarkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-sky-600 text-white hover:bg-sky-700'),\n          title: \"T\\u1EA3i l\\u1EA1i danh s\\xE1ch c\\xE2u h\\u1ECFi\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"\",\n            children: \"T\\u1EA3i l\\u1EA1i\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-semibold\",\n          children: \"Hi\\u1EC3n th\\u1ECB:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowMarkedOnly(!showMarkedOnly),\n          className: \"flex items-center gap-2 px-3 py-1 rounded-md transition-colors \".concat(isDarkMode ? showMarkedOnly ? 'bg-sky-600 text-white' : 'bg-gray-700 text-white hover:bg-gray-600' : showMarkedOnly ? 'bg-sky-500 text-white' : 'bg-sky-100 text-black hover:bg-sky-200'),\n          title: showMarkedOnly ? \"Hiển thị tất cả câu hỏi\" : \"Chỉ hiển thị câu hỏi đã đánh dấu\",\n          children: showMarkedOnly ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(BookmarkCheck, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0110\\xE3 \\u0111\\xE1nh d\\u1EA5u\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(List, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"T\\u1EA5t c\\u1EA3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"my-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n      completed: saveQuestions.length,\n      total: questions.length,\n      isDarkMode: isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"my-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TimeDisplay, {\n      isLoading: loadingLoadExam,\n      exam: exam,\n      remainingTime: remainingTime,\n      formatTime: formatTime,\n      isDarkMode: isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"my-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuestionSection, {\n      title: \"Ph\\u1EA7n I: Tr\\u1EAFc nghi\\u1EC7m\",\n      questions: questionTN,\n      scrollToQuestion: scrollToQuestion,\n      selectedQuestion: selectedQuestion,\n      markedQuestions: markedQuestions,\n      toggleMarkQuestion: toggleMarkQuestion,\n      isDarkMode: isDarkMode,\n      showMarkedOnly: showMarkedOnly,\n      savingQuestions: savingQuestions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"my-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuestionSection, {\n      title: \"Ph\\u1EA7n II: \\u0110\\xFAng sai\",\n      questions: questionDS,\n      scrollToQuestion: scrollToQuestion,\n      selectedQuestion: selectedQuestion,\n      markedQuestions: markedQuestions,\n      toggleMarkQuestion: toggleMarkQuestion,\n      isDarkMode: isDarkMode,\n      showMarkedOnly: showMarkedOnly\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"my-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuestionSection, {\n      title: \"Ph\\u1EA7n III: Tr\\u1EA3 l\\u1EDDi ng\\u1EAFn\",\n      questions: questionTLN,\n      scrollToQuestion: scrollToQuestion,\n      selectedQuestion: selectedQuestion,\n      markedQuestions: markedQuestions,\n      toggleMarkQuestion: toggleMarkQuestion,\n      isDarkMode: isDarkMode,\n      showMarkedOnly: showMarkedOnly\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"my-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(QuestionCounter, {\n        count: saveQuestions.length,\n        label: \"S\\u1ED1 c\\xE2u \\u0111\\xE3 l\\xE0m\",\n        bgColor: \"bg-green-600\",\n        bgColorLight: \"bg-green-500\",\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuestionCounter, {\n        count: savingQuestions.size,\n        label: \"S\\u1ED1 c\\xE2u \\u0111ang l\\u01B0u\",\n        bgColor: \"bg-blue-600\",\n        bgColorLight: \"bg-blue-400\",\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuestionCounter, {\n        count: selectedQuestion !== null ? 1 : 0,\n        label: \"S\\u1ED1 c\\xE2u \\u0111ang l\\xE0m\",\n        bgColor: \"bg-yellow-600\",\n        bgColorLight: \"bg-yellow-400\",\n        textColorLight: \"text-black\",\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuestionCounter, {\n        count: questions.length - saveQuestions.length,\n        label: \"S\\u1ED1 c\\xE2u ch\\u01B0a l\\xE0m\",\n        bgColor: \"bg-gray-700\",\n        bgColorLight: \"bg-gray-300\",\n        textColorLight: \"text-black\",\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuestionCounter, {\n        count: errorQuestions.length,\n        label: \"S\\u1ED1 c\\xE2u ch\\u01B0a l\\u01B0u\",\n        bgColor: \"bg-red-600\",\n        bgColorLight: \"bg-red-400\",\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuestionCounter, {\n        count: markedQuestions.size,\n        label: \"S\\u1ED1 c\\xE2u \\u0111\\xE3 \\u0111\\xE1nh d\\u1EA5u\",\n        bgColor: \"bg-sky-600\",\n        bgColorLight: \"bg-sky-500\",\n        textColorLight: \"text-white\",\n        isDarkMode: isDarkMode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {\n      className: \"my-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {\n      handleSubmit: handleAutoSubmit,\n      isLoading: loadingSubmit || loadingLoadExam\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(ExamSidebar, \"h7CN+Vijb4+zvQsl04tY/seuhiw=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = ExamSidebar;\nexport default ExamSidebar;\nvar _c;\n$RefreshReg$(_c, \"ExamSidebar\");", "map": {"version": 3, "names": ["React", "useState", "motion", "SettingsButton", "ThemeToggleButton", "SizeSlider", "QuestionSection", "QuestionCounter", "SubmitButton", "TimeDisplay", "ViewModeToggle", "ProgressBar", "NetworkSpeedTest", "BookmarkCheck", "List", "RefreshCw", "fetchPublicQuestionsByExamId", "useDispatch", "useSelector", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ExamSidebar", "_ref", "_s", "isDarkMode", "setIsDarkMode", "fontSize", "handleFontSizeChange", "imageSize", "handleImageSizeChange", "questionTN", "questionDS", "questionTLN", "scrollToQuestion", "selectedQuestion", "markedQuestions", "Set", "toggleMarkQuestion", "handleAutoSubmit", "loadingSubmit", "loadingLoadExam", "exam", "remainingTime", "formatTime", "questions", "savingQuestions", "singleQuestionMode", "setSingleQuestionMode", "showSettings", "setShowSettings", "showMarkedOnly", "setShowMarkedOnly", "dispatch", "saveQuestions", "errorQuestions", "state", "doExam", "div", "initial", "x", "animate", "exit", "transition", "type", "stiffness", "damping", "className", "concat", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "value", "onChange", "min", "max", "unit", "onClick", "id", "title", "size", "completed", "length", "total", "isLoading", "count", "bgColor", "bgColorLight", "textColorLight", "handleSubmit", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/sidebar/ExamSidebar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport SettingsButton from './SettingsButton';\r\nimport ThemeToggleButton from './ThemeToggleButton';\r\nimport SizeSlider from './SizeSlider';\r\nimport QuestionSection from './QuestionSection';\r\nimport QuestionCounter from './QuestionCounter';\r\nimport SubmitButton from './SubmitButton';\r\nimport TimeDisplay from './TimeDisplay';\r\nimport ViewModeToggle from './ViewModeToggle';\r\nimport ProgressBar from './ProgressBar';\r\nimport NetworkSpeedTest from '../NetworkSpeedTest';\r\nimport { BookmarkCheck, List, RefreshCw } from 'lucide-react';\r\nimport { fetchPublicQuestionsByExamId } from 'src/features/question/questionSlice';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\n\r\n/**\r\n * Main sidebar component for exam interface\r\n */\r\nconst ExamSidebar = ({\r\n  isDarkMode,\r\n  setIsDarkMode,\r\n  fontSize,\r\n  handleFontSizeChange,\r\n  imageSize,\r\n  handleImageSizeChange,\r\n  questionTN,\r\n  questionDS,\r\n  questionTLN,\r\n  scrollToQuestion,\r\n  selectedQuestion,\r\n  markedQuestions = new Set(),\r\n  toggleMarkQuestion = () => { },\r\n  handleAutoSubmit,\r\n  loadingSubmit,\r\n  loadingLoadExam,\r\n  exam,\r\n  remainingTime,\r\n  formatTime,\r\n  questions,\r\n  savingQuestions = new Set(),\r\n  singleQuestionMode = false,\r\n  setSingleQuestionMode = () => { }\r\n}) => {\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showMarkedOnly, setShowMarkedOnly] = useState(false);\r\n  const dispatch = useDispatch();\r\n  const { saveQuestions, errorQuestions } = useSelector(state => state.doExam);\r\n\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ x: \"100%\" }}\r\n      animate={{ x: 0 }}\r\n      exit={{ x: \"100%\" }}\r\n      transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n      className={`fixed top-0 right-0 w-11/12 sm:w-2/3 md:w-1/3 h-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'\r\n        } p-4 z-30 shadow-lg overflow-y-auto lg:sticky lg:shadow-none lg:h-[90vh] lg:top-20 lg:right-0`}\r\n    >\r\n      {/* Header with settings button and network speed */}\r\n      <div className=\"flex items-center justify-between w-full\">\r\n        <SettingsButton\r\n          showSettings={showSettings}\r\n          setShowSettings={setShowSettings}\r\n          isDarkMode={isDarkMode}\r\n        />\r\n        <NetworkSpeedTest />\r\n      </div>\r\n\r\n      {/* Settings panel */}\r\n      <div className={`transition-all duration-500 overflow-hidden ${showSettings ? \"max-h-[500px] opacity-100\" : \"max-h-0 opacity-0\"\r\n        }`}>\r\n        <hr className=\"my-4\" />\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <span className=\"font-semibold\">Chế độ:</span>\r\n          <ThemeToggleButton\r\n            isDarkMode={isDarkMode}\r\n            setIsDarkMode={setIsDarkMode}\r\n          />\r\n        </div>\r\n\r\n        <hr className=\"my-4\" />\r\n        <div className=\"flex flex-col gap-2\">\r\n          <SizeSlider\r\n            label=\"Chỉnh cỡ chữ\"\r\n            value={fontSize}\r\n            onChange={handleFontSizeChange}\r\n            min={12}\r\n            max={24}\r\n            isDarkMode={isDarkMode}\r\n            unit=\"px\"\r\n          />\r\n\r\n          <SizeSlider\r\n            label=\"Chỉnh cỡ ảnh\"\r\n            value={imageSize}\r\n            onChange={handleImageSizeChange}\r\n            min={6}\r\n            max={20}\r\n            isDarkMode={isDarkMode}\r\n            unit=\"rem\"\r\n          />\r\n        </div>\r\n\r\n        <hr className=\"my-4\" />\r\n        <ViewModeToggle\r\n          singleQuestionMode={singleQuestionMode}\r\n          setSingleQuestionMode={setSingleQuestionMode}\r\n          isDarkMode={isDarkMode}\r\n        />\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <span className=\"font-semibold\">Load lại: </span>\r\n          <button\r\n            onClick={() => dispatch(fetchPublicQuestionsByExamId(exam.id))}\r\n            className={`flex items-center gap-2 px-3 py-1 rounded-md transition-colors ${isDarkMode\r\n              ? 'bg-gray-700 text-white hover:bg-gray-600'\r\n              : 'bg-sky-600 text-white hover:bg-sky-700'\r\n              }`}\r\n            title=\"Tải lại danh sách câu hỏi\"\r\n          >\r\n            <RefreshCw size={18} />\r\n            <span className=\"\">Tải lại</span>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Filter toggle */}\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <span className=\"font-semibold\">Hiển thị:</span>\r\n          <button\r\n            onClick={() => setShowMarkedOnly(!showMarkedOnly)}\r\n            className={`flex items-center gap-2 px-3 py-1 rounded-md transition-colors ${isDarkMode\r\n              ? (showMarkedOnly ? 'bg-sky-600 text-white' : 'bg-gray-700 text-white hover:bg-gray-600')\r\n              : (showMarkedOnly ? 'bg-sky-500 text-white' : 'bg-sky-100 text-black hover:bg-sky-200')\r\n              }`}\r\n            title={showMarkedOnly ? \"Hiển thị tất cả câu hỏi\" : \"Chỉ hiển thị câu hỏi đã đánh dấu\"}\r\n          >\r\n            {showMarkedOnly ? (\r\n              <>\r\n                <BookmarkCheck size={18} />\r\n                <span>Đã đánh dấu</span>\r\n              </>\r\n            ) : (\r\n              <>\r\n                <List size={18} />\r\n                <span>Tất cả</span>\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n\r\n      </div>\r\n\r\n\r\n      <hr className=\"my-4\" />\r\n\r\n      {/* Progress bar */}\r\n      <ProgressBar\r\n        completed={saveQuestions.length}\r\n        total={questions.length}\r\n        isDarkMode={isDarkMode}\r\n      />\r\n\r\n      <hr className=\"my-4\" />\r\n\r\n      {/* Time display */}\r\n      <TimeDisplay\r\n        isLoading={loadingLoadExam}\r\n        exam={exam}\r\n        remainingTime={remainingTime}\r\n        formatTime={formatTime}\r\n        isDarkMode={isDarkMode}\r\n      />\r\n\r\n      <hr className=\"my-4\" />\r\n\r\n      {/* Multiple choice questions section */}\r\n      <QuestionSection\r\n        title=\"Phần I: Trắc nghiệm\"\r\n        questions={questionTN}\r\n        scrollToQuestion={scrollToQuestion}\r\n        selectedQuestion={selectedQuestion}\r\n        markedQuestions={markedQuestions}\r\n        toggleMarkQuestion={toggleMarkQuestion}\r\n        isDarkMode={isDarkMode}\r\n        showMarkedOnly={showMarkedOnly}\r\n        savingQuestions={savingQuestions}\r\n      />\r\n\r\n      <hr className=\"my-4\" />\r\n\r\n      {/* True/False questions section */}\r\n      <QuestionSection\r\n        title=\"Phần II: Đúng sai\"\r\n        questions={questionDS}\r\n        scrollToQuestion={scrollToQuestion}\r\n        selectedQuestion={selectedQuestion}\r\n\r\n        markedQuestions={markedQuestions}\r\n        toggleMarkQuestion={toggleMarkQuestion}\r\n        isDarkMode={isDarkMode}\r\n        showMarkedOnly={showMarkedOnly}\r\n      />\r\n\r\n      <hr className=\"my-4\" />\r\n\r\n      {/* Short answer questions section */}\r\n      <QuestionSection\r\n        title=\"Phần III: Trả lời ngắn\"\r\n        questions={questionTLN}\r\n        scrollToQuestion={scrollToQuestion}\r\n        selectedQuestion={selectedQuestion}\r\n\r\n        markedQuestions={markedQuestions}\r\n        toggleMarkQuestion={toggleMarkQuestion}\r\n        isDarkMode={isDarkMode}\r\n        showMarkedOnly={showMarkedOnly}\r\n      />\r\n\r\n      <hr className=\"my-4\" />\r\n\r\n      {/* Question statistics */}\r\n      <div className=\"flex flex-col gap-2\">\r\n        <QuestionCounter\r\n          count={saveQuestions.length}\r\n          label=\"Số câu đã làm\"\r\n          bgColor=\"bg-green-600\"\r\n          bgColorLight=\"bg-green-500\"\r\n          isDarkMode={isDarkMode}\r\n        />\r\n\r\n        <QuestionCounter\r\n          count={savingQuestions.size}\r\n          label=\"Số câu đang lưu\"\r\n          bgColor=\"bg-blue-600\"\r\n          bgColorLight=\"bg-blue-400\"\r\n          isDarkMode={isDarkMode}\r\n        />\r\n\r\n        <QuestionCounter\r\n          count={selectedQuestion !== null ? 1 : 0}\r\n          label=\"Số câu đang làm\"\r\n          bgColor=\"bg-yellow-600\"\r\n          bgColorLight=\"bg-yellow-400\"\r\n          textColorLight=\"text-black\"\r\n          isDarkMode={isDarkMode}\r\n        />\r\n\r\n        <QuestionCounter\r\n          count={questions.length - saveQuestions.length}\r\n          label=\"Số câu chưa làm\"\r\n          bgColor=\"bg-gray-700\"\r\n          bgColorLight=\"bg-gray-300\"\r\n          textColorLight=\"text-black\"\r\n          isDarkMode={isDarkMode}\r\n        />\r\n\r\n        <QuestionCounter\r\n          count={errorQuestions.length}\r\n          label=\"Số câu chưa lưu\"\r\n          bgColor=\"bg-red-600\"\r\n          bgColorLight=\"bg-red-400\"\r\n          isDarkMode={isDarkMode}\r\n        />\r\n\r\n        <QuestionCounter\r\n          count={markedQuestions.size}\r\n          label=\"Số câu đã đánh dấu\"\r\n          bgColor=\"bg-sky-600\"\r\n          bgColorLight=\"bg-sky-500\"\r\n          textColorLight=\"text-white\"\r\n          isDarkMode={isDarkMode}\r\n        />\r\n      </div>\r\n\r\n      <hr className=\"my-4\" />\r\n\r\n      {/* Submit button */}\r\n      <SubmitButton\r\n        handleSubmit={handleAutoSubmit}\r\n        isLoading={loadingSubmit || loadingLoadExam}\r\n      />\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default ExamSidebar;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,SAASC,aAAa,EAAEC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AAC7D,SAASC,4BAA4B,QAAQ,qCAAqC;AAClF,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;;AAEtD;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,WAAW,GAAGC,IAAA,IAwBd;EAAAC,EAAA;EAAA,IAxBe;IACnBC,UAAU;IACVC,aAAa;IACbC,QAAQ;IACRC,oBAAoB;IACpBC,SAAS;IACTC,qBAAqB;IACrBC,UAAU;IACVC,UAAU;IACVC,WAAW;IACXC,gBAAgB;IAChBC,gBAAgB;IAChBC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3BC,kBAAkB,GAAGA,CAAA,KAAM,CAAE,CAAC;IAC9BC,gBAAgB;IAChBC,aAAa;IACbC,eAAe;IACfC,IAAI;IACJC,aAAa;IACbC,UAAU;IACVC,SAAS;IACTC,eAAe,GAAG,IAAIT,GAAG,CAAC,CAAC;IAC3BU,kBAAkB,GAAG,KAAK;IAC1BC,qBAAqB,GAAGA,CAAA,KAAM,CAAE;EAClC,CAAC,GAAAzB,IAAA;EACC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMqD,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsC,aAAa;IAAEC;EAAe,CAAC,GAAGtC,WAAW,CAACuC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EAG5E,oBACEtC,OAAA,CAAClB,MAAM,CAACyD,GAAG;IACTC,OAAO,EAAE;MAAEC,CAAC,EAAE;IAAO,CAAE;IACvBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,IAAI,EAAE;MAAEF,CAAC,EAAE;IAAO,CAAE;IACpBG,UAAU,EAAE;MAAEC,IAAI,EAAE,QAAQ;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE;IAAG,CAAE;IAC5DC,SAAS,0DAAAC,MAAA,CAA0D3C,UAAU,GAAG,aAAa,GAAG,UAAU,kGACR;IAAA4C,QAAA,gBAGlGlD,OAAA;MAAKgD,SAAS,EAAC,0CAA0C;MAAAE,QAAA,gBACvDlD,OAAA,CAACjB,cAAc;QACb+C,YAAY,EAAEA,YAAa;QAC3BC,eAAe,EAAEA,eAAgB;QACjCzB,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFtD,OAAA,CAACR,gBAAgB;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNtD,OAAA;MAAKgD,SAAS,iDAAAC,MAAA,CAAiDnB,YAAY,GAAG,2BAA2B,GAAG,mBAAmB,CAC1H;MAAAoB,QAAA,gBACHlD,OAAA;QAAIgD,SAAS,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBtD,OAAA;QAAKgD,SAAS,EAAC,wCAAwC;QAAAE,QAAA,gBACrDlD,OAAA;UAAMgD,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9CtD,OAAA,CAAChB,iBAAiB;UAChBsB,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA;QAAc;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtD,OAAA;QAAIgD,SAAS,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBtD,OAAA;QAAKgD,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAClClD,OAAA,CAACf,UAAU;UACTsE,KAAK,EAAC,6BAAc;UACpBC,KAAK,EAAEhD,QAAS;UAChBiD,QAAQ,EAAEhD,oBAAqB;UAC/BiD,GAAG,EAAE,EAAG;UACRC,GAAG,EAAE,EAAG;UACRrD,UAAU,EAAEA,UAAW;UACvBsD,IAAI,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEFtD,OAAA,CAACf,UAAU;UACTsE,KAAK,EAAC,6BAAc;UACpBC,KAAK,EAAE9C,SAAU;UACjB+C,QAAQ,EAAE9C,qBAAsB;UAChC+C,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,EAAG;UACRrD,UAAU,EAAEA,UAAW;UACvBsD,IAAI,EAAC;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtD,OAAA;QAAIgD,SAAS,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBtD,OAAA,CAACV,cAAc;QACbsC,kBAAkB,EAAEA,kBAAmB;QACvCC,qBAAqB,EAAEA,qBAAsB;QAC7CvB,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFtD,OAAA;QAAKgD,SAAS,EAAC,wCAAwC;QAAAE,QAAA,gBACrDlD,OAAA;UAAMgD,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjDtD,OAAA;UACE6D,OAAO,EAAEA,CAAA,KAAM3B,QAAQ,CAACtC,4BAA4B,CAAC2B,IAAI,CAACuC,EAAE,CAAC,CAAE;UAC/Dd,SAAS,oEAAAC,MAAA,CAAoE3C,UAAU,GACnF,0CAA0C,GAC1C,wCAAwC,CACvC;UACLyD,KAAK,EAAC,gDAA2B;UAAAb,QAAA,gBAEjClD,OAAA,CAACL,SAAS;YAACqE,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvBtD,OAAA;YAAMgD,SAAS,EAAC,EAAE;YAAAE,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNtD,OAAA;QAAKgD,SAAS,EAAC,wCAAwC;QAAAE,QAAA,gBACrDlD,OAAA;UAAMgD,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChDtD,OAAA;UACE6D,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC,CAACD,cAAc,CAAE;UAClDgB,SAAS,oEAAAC,MAAA,CAAoE3C,UAAU,GAClF0B,cAAc,GAAG,uBAAuB,GAAG,0CAA0C,GACrFA,cAAc,GAAG,uBAAuB,GAAG,wCAAyC,CACpF;UACL+B,KAAK,EAAE/B,cAAc,GAAG,yBAAyB,GAAG,kCAAmC;UAAAkB,QAAA,EAEtFlB,cAAc,gBACbhC,OAAA,CAAAE,SAAA;YAAAgD,QAAA,gBACElD,OAAA,CAACP,aAAa;cAACuE,IAAI,EAAE;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3BtD,OAAA;cAAAkD,QAAA,EAAM;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACxB,CAAC,gBAEHtD,OAAA,CAAAE,SAAA;YAAAgD,QAAA,gBACElD,OAAA,CAACN,IAAI;cAACsE,IAAI,EAAE;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClBtD,OAAA;cAAAkD,QAAA,EAAM;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eACnB;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAGNtD,OAAA;MAAIgD,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBtD,OAAA,CAACT,WAAW;MACV0E,SAAS,EAAE9B,aAAa,CAAC+B,MAAO;MAChCC,KAAK,EAAEzC,SAAS,CAACwC,MAAO;MACxB5D,UAAU,EAAEA;IAAW;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEFtD,OAAA;MAAIgD,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBtD,OAAA,CAACX,WAAW;MACV+E,SAAS,EAAE9C,eAAgB;MAC3BC,IAAI,EAAEA,IAAK;MACXC,aAAa,EAAEA,aAAc;MAC7BC,UAAU,EAAEA,UAAW;MACvBnB,UAAU,EAAEA;IAAW;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEFtD,OAAA;MAAIgD,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBtD,OAAA,CAACd,eAAe;MACd6E,KAAK,EAAC,oCAAqB;MAC3BrC,SAAS,EAAEd,UAAW;MACtBG,gBAAgB,EAAEA,gBAAiB;MACnCC,gBAAgB,EAAEA,gBAAiB;MACnCC,eAAe,EAAEA,eAAgB;MACjCE,kBAAkB,EAAEA,kBAAmB;MACvCb,UAAU,EAAEA,UAAW;MACvB0B,cAAc,EAAEA,cAAe;MAC/BL,eAAe,EAAEA;IAAgB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,eAEFtD,OAAA;MAAIgD,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBtD,OAAA,CAACd,eAAe;MACd6E,KAAK,EAAC,gCAAmB;MACzBrC,SAAS,EAAEb,UAAW;MACtBE,gBAAgB,EAAEA,gBAAiB;MACnCC,gBAAgB,EAAEA,gBAAiB;MAEnCC,eAAe,EAAEA,eAAgB;MACjCE,kBAAkB,EAAEA,kBAAmB;MACvCb,UAAU,EAAEA,UAAW;MACvB0B,cAAc,EAAEA;IAAe;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAEFtD,OAAA;MAAIgD,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBtD,OAAA,CAACd,eAAe;MACd6E,KAAK,EAAC,4CAAwB;MAC9BrC,SAAS,EAAEZ,WAAY;MACvBC,gBAAgB,EAAEA,gBAAiB;MACnCC,gBAAgB,EAAEA,gBAAiB;MAEnCC,eAAe,EAAEA,eAAgB;MACjCE,kBAAkB,EAAEA,kBAAmB;MACvCb,UAAU,EAAEA,UAAW;MACvB0B,cAAc,EAAEA;IAAe;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAEFtD,OAAA;MAAIgD,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBtD,OAAA;MAAKgD,SAAS,EAAC,qBAAqB;MAAAE,QAAA,gBAClClD,OAAA,CAACb,eAAe;QACdkF,KAAK,EAAElC,aAAa,CAAC+B,MAAO;QAC5BX,KAAK,EAAC,kCAAe;QACrBe,OAAO,EAAC,cAAc;QACtBC,YAAY,EAAC,cAAc;QAC3BjE,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEFtD,OAAA,CAACb,eAAe;QACdkF,KAAK,EAAE1C,eAAe,CAACqC,IAAK;QAC5BT,KAAK,EAAC,mCAAiB;QACvBe,OAAO,EAAC,aAAa;QACrBC,YAAY,EAAC,aAAa;QAC1BjE,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEFtD,OAAA,CAACb,eAAe;QACdkF,KAAK,EAAErD,gBAAgB,KAAK,IAAI,GAAG,CAAC,GAAG,CAAE;QACzCuC,KAAK,EAAC,iCAAiB;QACvBe,OAAO,EAAC,eAAe;QACvBC,YAAY,EAAC,eAAe;QAC5BC,cAAc,EAAC,YAAY;QAC3BlE,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEFtD,OAAA,CAACb,eAAe;QACdkF,KAAK,EAAE3C,SAAS,CAACwC,MAAM,GAAG/B,aAAa,CAAC+B,MAAO;QAC/CX,KAAK,EAAC,iCAAiB;QACvBe,OAAO,EAAC,aAAa;QACrBC,YAAY,EAAC,aAAa;QAC1BC,cAAc,EAAC,YAAY;QAC3BlE,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEFtD,OAAA,CAACb,eAAe;QACdkF,KAAK,EAAEjC,cAAc,CAAC8B,MAAO;QAC7BX,KAAK,EAAC,mCAAiB;QACvBe,OAAO,EAAC,YAAY;QACpBC,YAAY,EAAC,YAAY;QACzBjE,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEFtD,OAAA,CAACb,eAAe;QACdkF,KAAK,EAAEpD,eAAe,CAAC+C,IAAK;QAC5BT,KAAK,EAAC,iDAAoB;QAC1Be,OAAO,EAAC,YAAY;QACpBC,YAAY,EAAC,YAAY;QACzBC,cAAc,EAAC,YAAY;QAC3BlE,UAAU,EAAEA;MAAW;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENtD,OAAA;MAAIgD,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBtD,OAAA,CAACZ,YAAY;MACXqF,YAAY,EAAErD,gBAAiB;MAC/BgD,SAAS,EAAE/C,aAAa,IAAIC;IAAgB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEjB,CAAC;AAACjD,EAAA,CAxQIF,WAAW;EAAA,QA2BEN,WAAW,EACcC,WAAW;AAAA;AAAA4E,EAAA,GA5BjDvE,WAAW;AA0QjB,eAAeA,WAAW;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}