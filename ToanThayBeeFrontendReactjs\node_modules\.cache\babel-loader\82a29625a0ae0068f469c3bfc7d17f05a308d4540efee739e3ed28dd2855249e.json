{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\DoExamPage.jsx\",\n  _s = $RefreshSig$();\nimport HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useState, useEffect, useRef, useCallback } from \"react\";\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\nimport { useParams } from \"react-router-dom\";\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\nimport { AnimatePresence } from \"framer-motion\";\nimport { Menu } from \"lucide-react\";\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\nimport ExamContent from \"../../../components/questions/ExamContent\";\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\nimport { setRemainingTime, summitExam, setSaveQuestions, setErrorQuestions, getRemainingTime, logUserActivity, submitAnswerWithAttempt, leaveExam } from \"../../../features/doExam/doExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoExamPage = () => {\n  _s();\n  var _examContentRef$curre;\n  const {\n    examId\n  } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    exam\n  } = useSelector(state => state.exams);\n  const {\n    questions\n  } = useSelector(state => state.questions);\n  const {\n    answers\n  } = useSelector(state => state.answers);\n  const [fontSize, setFontSize] = useState(14); // 14px mặc định\n  const [imageSize, setImageSize] = useState(12); // đơn vị: rem\n  const questionRefs = useRef([]);\n  const [isAgree, setIsAgree] = useState(false);\n  const [attemptId, setAttemptId] = useState(null);\n  const attemptRef = useRef(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [flag, setFlag] = useState(false);\n  const [startTime1, setStartTime1] = useState(null);\n  const hasSubmittedRef = useRef(false);\n  const examRef = useRef(null);\n  const examContentRef = useRef(null);\n  useEffect(() => {\n    examRef.current = exam;\n    if ((exam === null || exam === void 0 ? void 0 : exam.acceptDoExam) === false) {\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  }, [exam]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchPublicExamById(examId));\n    }\n  }, [dispatch, examId]);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    remainingTime,\n    saveQuestions,\n    errorQuestions\n  } = useSelector(state => state.doExam);\n  const [markedQuestions, setMarkedQuestions] = useState(new Set());\n  const [timeWarningShown, setTimeWarningShown] = useState({\n    fiveMinutes: false,\n    oneMinute: false\n  });\n  const [isTimeBlinking, setIsTimeBlinking] = useState(false);\n  const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    const saved = localStorage.getItem(\"isDarkMode\");\n    return saved ? JSON.parse(saved) : false;\n  });\n  const [loadingSubmit, setLoadingSubmit] = useState(false);\n  const [loadingLoadExam, setLoadingLoadExam] = useState(false);\n  const [isTimeUp, setIsTimeUp] = useState(false);\n  const [questionTN, setQuestionTN] = useState([]);\n  const [questionDS, setQuestionDS] = useState([]);\n  const [questionTLN, setQuestionTLN] = useState([]);\n  const [answerTN, setAnswerTN] = useState([]);\n  const [answerTLN, setAnswerTLN] = useState([]);\n  const [dsAnswers, setDsAnswers] = useState({});\n  document.addEventListener(\"copy\", e => {\n    e.preventDefault();\n  });\n  const addQuestion = questionId => {\n    if (!saveQuestions.includes(questionId)) {\n      dispatch(setSaveQuestions([...saveQuestions, questionId]));\n    }\n    removeErrorQuestion(questionId);\n  };\n  const addErrorQuestion = questionId => {\n    if (!errorQuestions.includes(questionId)) {\n      dispatch(setErrorQuestions([...errorQuestions, questionId]));\n    }\n    removeQuestion(questionId);\n  };\n  const removeQuestion = questionId => {\n    dispatch(setSaveQuestions(saveQuestions.filter(id => id !== questionId)));\n  };\n  const removeErrorQuestion = questionId => {\n    dispatch(setErrorQuestions(errorQuestions.filter(id => id !== questionId)));\n  };\n\n  // Hàm đánh dấu câu hỏi để xem lại sau\n  const toggleMarkQuestion = questionId => {\n    setMarkedQuestions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(questionId)) {\n        newSet.delete(questionId);\n      } else {\n        newSet.add(questionId);\n      }\n      return newSet;\n    });\n  };\n  const handleExitFullscreen = () => {\n    try {\n      exitFullscreen();\n    } catch (err) {\n      // Chỉ ghi log lỗi, không bắt lỗi\n      console.warn(\"Không thể thoát fullscreen:\", err);\n    }\n  };\n  const handleFontSizeChange = e => {\n    setFontSize(Number(e.target.value));\n  };\n  const handleImageSizeChange = e => {\n    setImageSize(Number(e.target.value));\n  };\n  const formatTime = seconds => {\n    const min = String(Math.floor(seconds / 60)).padStart(2, '0');\n    const sec = String(seconds % 60).padStart(2, '0');\n    return \"\".concat(min, \":\").concat(sec);\n  };\n  const handleFullScreen = async () => {\n    setLoadingLoadExam(true);\n    try {\n      // Gọi API join exam thay vì socket\n      const response = await fetch(\"/api/v1/user/join-exam/\".concat(examId), {\n        method: 'GET',\n        headers: {\n          'Authorization': \"Bearer \".concat(localStorage.getItem('token')),\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.message || 'Lỗi khi tham gia bài thi');\n      }\n\n      // Xử lý khi join exam thành công\n      const {\n        attemptId,\n        startTime\n      } = data;\n      console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\n      setIsAgree(true);\n      attemptRef.current = attemptId;\n      setAttemptId(attemptId);\n      if (examId) {\n        dispatch(fetchPublicQuestionsByExamId(examId));\n      }\n      setStartTime1(startTime);\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) {\n        setLoadingLoadExam(false);\n        return;\n      }\n      try {\n        const success = await requestFullscreen();\n        if (success) {\n          setTimeout(() => {\n            setLoadingLoadExam(false);\n          }, 800);\n        } else {\n          console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\n          setLoadingLoadExam(false);\n        }\n      } catch (err) {\n        console.error(\"❌ Lỗi khi bật fullscreen:\", err);\n        alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\n        setLoadingLoadExam(false);\n      }\n    } catch (error) {\n      console.error(\"Lỗi khi tham gia bài thi:\", error);\n      dispatch(setErrorMessage(\"Lỗi: \" + error.message));\n      setLoadingLoadExam(false);\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  };\n\n  // Removed socket-based exam_started listener - now handled in handleFullScreen\n\n  useEffect(() => {\n    if (exam !== null && exam !== void 0 && exam.testDuration && startTime1) {\n      const start = new Date(startTime1);\n      const now = new Date();\n      const elapsedSeconds = Math.floor((now - start) / 1000);\n      const totalSeconds = exam.testDuration * 60;\n      const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\n      dispatch(setRemainingTime(remaining));\n\n      // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\n      if (attemptId) {\n        dispatch(getRemainingTime({\n          examId,\n          attemptId\n        })).then(result => {\n          var _result$payload;\n          if (((_result$payload = result.payload) === null || _result$payload === void 0 ? void 0 : _result$payload.remainingTime) !== undefined) {\n            dispatch(setRemainingTime(result.payload.remainingTime));\n          }\n        }).catch(error => {\n          console.error(\"Lỗi khi lấy thời gian từ server:\", error);\n        });\n      }\n    }\n  }, [startTime1, exam, attemptId, examId, dispatch]);\n  useEffect(() => {\n    if (flag) return;\n    if (!remainingTime) setFlag(true);\n  }, [remainingTime]);\n  const handleAutoSubmit = async () => {\n    if (hasSubmittedRef.current) {\n      console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\n      return;\n    }\n    hasSubmittedRef.current = true; // Đánh dấu đã submit\n    console.log(\"Kiểm tra attemptId:\", attemptId);\n    if (!attemptId) {\n      console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\n      return;\n    }\n    console.log(\"Đang nộp bài với attemptId:\", attemptId);\n    dispatch(setSaveQuestions([]));\n    setLoadingSubmit(true);\n    try {\n      // Sử dụng API thay vì socket để nộp bài\n      const result = await dispatch(summitExam(attemptId)).unwrap();\n      console.log(\"Nộp bài thành công:\", result);\n\n      // Xử lý khi nộp bài thành công\n      dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\n\n      // Thoát fullscreen mà không bắt lỗi\n      try {\n        exitFullscreen();\n      } catch (err) {\n        // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\n        console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\n      }\n      const safeAttemptId = attemptRef.current;\n      const currentExam = examRef.current;\n      if (!safeAttemptId) {\n        console.error(\"Không có attemptId khi navigate!\");\n        return;\n      }\n\n      // Log để debug\n      console.log(\"Current exam state:\", currentExam);\n      console.log(\"Attempt ID:\", safeAttemptId);\n      if (!currentExam || !currentExam.seeCorrectAnswer) {\n        console.log(\"Chuyển về trang danh sách do:\", {\n          examNull: !currentExam,\n          cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\n        });\n        navigate(\"/practice/exam/\".concat(examId));\n        return;\n      }\n      navigate(\"/practice/exam/attempt/\".concat(safeAttemptId, \"/score\"));\n    } catch (error) {\n      console.error(\"Lỗi khi nộp bài:\", error);\n      setLoadingSubmit(false);\n      dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\n      hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\n\n      // Thử nộp lại sau 3 giây nếu lỗi xảy ra\n      setTimeout(() => {\n        if (!loadingSubmit && attemptRef.current) {\n          console.log(\"Thử nộp bài lại sau lỗi...\");\n          handleAutoSubmit();\n        }\n      }, 5000);\n    }\n  };\n\n  // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\n  const navigateToQuestion = useCallback(questionId => {\n    setSelectedQuestion(questionId);\n\n    // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\n    if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\n      // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\n      examContentRef.current.goToQuestionById(questionId);\n    } else {\n      // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\n      // Tìm phần tử câu hỏi bằng querySelector\n      setTimeout(() => {\n        // Thử tìm phần tử bằng data-question-id\n        const element = document.querySelector(\"[data-question-id=\\\"\".concat(questionId, \"\\\"]\"));\n        if (element) {\n          const offset = 80; // chiều cao của header sticky\n          const y = element.getBoundingClientRect().top + window.scrollY - offset;\n          window.scrollTo({\n            top: y,\n            behavior: \"smooth\"\n          });\n        } else {\n          // Fallback: Sử dụng refs\n          const refElement = questionRefs.current[questionId];\n          if (refElement) {\n            const offset = 80; // chiều cao của header sticky\n            const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\n            window.scrollTo({\n              top: y,\n              behavior: \"smooth\"\n            });\n          }\n        }\n      }, 0);\n    }\n  }, [questionRefs, examContentRef]);\n\n  // Alias cho navigateToQuestion để tương thích với các component khác\n  const scrollToQuestion = navigateToQuestion;\n  const handleSelectAnswerTN = (questionId, statementId, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const newAnswer = {\n      questionId,\n      answerContent: statementId,\n      typeOfQuestion: type\n    };\n    dispatch(setAnswers(newAnswer));\n\n    // Sử dụng API thay vì socket\n    dispatch(submitAnswerWithAttempt({\n      questionId,\n      answerContent: statementId,\n      type,\n      attemptId\n    })).then(result => {\n      if (result.type.endsWith('/fulfilled')) {\n        // Answer submitted successfully\n        console.log(\"Đã lưu câu trả lời thành công\");\n      } else {\n        // Handle error\n        console.error(\"Lỗi khi lưu câu trả lời:\", result.error);\n      }\n    });\n  };\n  const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const currentAnswers = dsAnswers[questionId] || [];\n    const existing = currentAnswers.find(ans => ans.statementId === statementId);\n\n    // 🔁 Nếu đáp án đã giống thì không gửi lại\n    if (existing && existing.answer === selectedAnswer) {\n      return;\n    }\n    const updatedAnswers = currentAnswers.map(ans => ans.statementId === statementId ? {\n      ...ans,\n      answer: selectedAnswer\n    } : ans);\n\n    // Nếu chưa có statement này\n    if (!existing) {\n      updatedAnswers.push({\n        statementId,\n        answer: selectedAnswer\n      });\n    }\n    dispatch(setAnswers({\n      questionId,\n      answerContent: JSON.stringify(updatedAnswers),\n      typeOfQuestion: \"DS\"\n    }));\n\n    // Sử dụng API thay vì socket\n    dispatch(submitAnswerWithAttempt({\n      questionId,\n      answerContent: updatedAnswers,\n      type: \"DS\",\n      attemptId\n    })).then(result => {\n      if (result.type.endsWith('/fulfilled')) {\n        console.log(\"Đã lưu câu trả lời DS thành công\");\n      } else {\n        console.error(\"Lỗi khi lưu câu trả lời DS:\", result.error);\n      }\n    });\n  };\n  const handleSelectAnswerTLN = (questionId, answerContent, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    if (!answerContent || answerContent.trim() === \"\") {\n      return;\n    }\n    const formattedAnswer = answerContent.trim().replace(\",\", \".\");\n    dispatch(setAnswers({\n      questionId,\n      answerContent,\n      typeOfQuestion: type\n    }));\n\n    // Sử dụng API thay vì socket\n    dispatch(submitAnswerWithAttempt({\n      questionId,\n      answerContent: formattedAnswer,\n      type,\n      attemptId\n    })).then(result => {\n      if (result.type.endsWith('/fulfilled')) {\n        console.log(\"Đã lưu câu trả lời TLN thành công\");\n      } else {\n        console.error(\"Lỗi khi lưu câu trả lời TLN:\", result.error);\n      }\n    });\n  };\n\n  // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\n  const questionsToMarkAsSaved = useRef(new Set());\n\n  // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\n  useEffect(() => {\n    if (questionsToMarkAsSaved.current.size > 0) {\n      questionsToMarkAsSaved.current.forEach(questionId => {\n        if (!saveQuestions.includes(questionId)) {\n          addQuestion(questionId);\n        }\n      });\n      questionsToMarkAsSaved.current.clear();\n    }\n  }, [saveQuestions, addQuestion]);\n\n  // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\n  useEffect(() => {\n    // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\n    const frameId = requestAnimationFrame(() => {\n      if (questionsToMarkAsSaved.current.size > 0) {\n        const questionIds = [...questionsToMarkAsSaved.current];\n        questionsToMarkAsSaved.current.clear();\n\n        // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\n        questionIds.forEach(questionId => {\n          if (!saveQuestions.has(questionId)) {\n            addQuestion(questionId);\n          }\n        });\n      }\n    });\n    return () => cancelAnimationFrame(frameId);\n  });\n  const isTNSelected = useCallback((questionId, statementId) => {\n    const isSelected = answerTN.some(ans => ans.questionId === questionId && ans.answerContent && String(ans.answerContent) === String(statementId));\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.has(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [answerTN, saveQuestions]);\n  const isDSChecked = useCallback((questionId, statementId, bool) => {\n    var _dsAnswers$questionId, _dsAnswers$questionId2;\n    const isSelected = ((_dsAnswers$questionId = dsAnswers[questionId]) === null || _dsAnswers$questionId === void 0 ? void 0 : _dsAnswers$questionId.some(a => a.statementId === statementId && a.answer === bool)) || false;\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.has(questionId) && ((_dsAnswers$questionId2 = dsAnswers[questionId]) === null || _dsAnswers$questionId2 === void 0 ? void 0 : _dsAnswers$questionId2.length) === 4) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [dsAnswers, saveQuestions]);\n  const getTLNDefaultValue = useCallback(questionId => {\n    var _matched$answerConten;\n    const matched = answerTLN.find(ans => ans.questionId === questionId);\n    const content = (matched === null || matched === void 0 ? void 0 : (_matched$answerConten = matched.answerContent) === null || _matched$answerConten === void 0 ? void 0 : _matched$answerConten.replace(/^\"|\"$/g, \"\")) || \"\";\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (content && !saveQuestions.has(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return content;\n  }, [answerTLN, saveQuestions]);\n\n  // useEffect(() => {\n  //     if (examId) {\n  //         dispatch(fetchPublicQuestionsByExamId(examId));\n  //     }\n  // }, [dispatch, examId]);\n\n  useEffect(() => {\n    if (questions) {\n      setQuestionTN(questions.filter(question => question.typeOfQuestion === \"TN\"));\n      setQuestionDS(questions.filter(question => question.typeOfQuestion === \"DS\"));\n      setQuestionTLN(questions.filter(question => question.typeOfQuestion === \"TLN\"));\n    }\n  }, [questions]);\n  useEffect(() => {\n    // Kiểm tra answers có phải là mảng không\n    if (!Array.isArray(answers) || answers.length === 0) return;\n    const tn = [];\n    const tln = [];\n    const dsMap = {};\n\n    // Sử dụng for...of thay vì forEach để tránh lỗi\n    for (const answer of answers) {\n      if (answer.typeOfQuestion === \"TN\") {\n        tn.push(answer);\n      } else if (answer.typeOfQuestion === \"TLN\") {\n        tln.push(answer);\n      } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\n        try {\n          const parsed = JSON.parse(answer.answerContent);\n          dsMap[answer.questionId] = parsed;\n        } catch (err) {\n          console.error(\"Lỗi parse DS answerContent:\", err);\n        }\n      }\n    }\n    setAnswerTN(tn);\n    setAnswerTLN(tln);\n    setDsAnswers(dsMap);\n\n    // Note: Score calculation is now handled when submitting exam\n    // No need to calculate score in real-time\n  }, [answers]);\n  useEffect(() => {\n    if (attemptId) {\n      dispatch(fetchAnswersByAttempt(attemptId));\n    }\n  }, [dispatch, attemptId]);\n  useEffect(() => {\n    if (!(exam !== null && exam !== void 0 && exam.testDuration) || remainingTime === null || !isAgree) return;\n\n    // Kiểm tra và hiển thị cảnh báo thời gian\n    const checkTimeWarnings = time => {\n      // Cảnh báo khi còn 5 phút\n      if (time === 300 && !timeWarningShown.fiveMinutes) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          fiveMinutes: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\n\n        // Tắt hiệu ứng nhấp nháy sau 10 giây\n        setTimeout(() => {\n          setIsTimeBlinking(false);\n        }, 10000);\n      }\n\n      // Cảnh báo khi còn 1 phút\n      if (time === 60 && !timeWarningShown.oneMinute) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          oneMinute: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\n\n        // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\n      }\n    };\n\n    // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API\n    const syncTimeInterval = setInterval(() => {\n      if (attemptId) {\n        dispatch(getRemainingTime({\n          examId,\n          attemptId\n        })).then(result => {\n          var _result$payload2;\n          if (((_result$payload2 = result.payload) === null || _result$payload2 === void 0 ? void 0 : _result$payload2.remainingTime) !== undefined) {\n            dispatch(setRemainingTime(result.payload.remainingTime));\n          }\n        }).catch(error => {\n          console.error(\"Lỗi khi đồng bộ thời gian:\", error);\n        });\n      }\n    }, 30000); // Đồng bộ thời gian mỗi 30 giây\n\n    const interval = setInterval(() => {\n      dispatch(setRemainingTime(prev => {\n        if (prev <= 1) {\n          // dùng <=1 để đảm bảo không bị âm\n          clearInterval(interval);\n          clearInterval(syncTimeInterval);\n          // Đánh dấu là đã hết thời gian\n          setIsTimeUp(true);\n          setIsTimeBlinking(false);\n          // Thử nộp bài\n          handleAutoSubmit();\n          return 0;\n        }\n\n        // Kiểm tra cảnh báo thời gian\n        checkTimeWarnings(prev);\n        return prev - 1;\n      }));\n    }, 1000);\n    return () => {\n      clearInterval(interval);\n      clearInterval(syncTimeInterval);\n    };\n  }, [exam === null || exam === void 0 ? void 0 : exam.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId]); // Chỉ phụ thuộc vào các giá trị cần thiết\n\n  // Removed socket connection management - using API only\n\n  // frontend\n  useEffect(() => {\n    if (!attemptId || !(user !== null && user !== void 0 && user.id) || !examId || attemptId === null || attemptId === undefined) return;\n    if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) return;\n    console.log(\"Đã bật theo dõi hành vi gian lận\");\n    const recentLogs = new Set(); // chống log lặp\n    const logOnce = (key, payload) => {\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled) || recentLogs.has(key)) return;\n      recentLogs.add(key);\n\n      // Sử dụng API thay vì socket\n      dispatch(logUserActivity({\n        examId,\n        attemptId,\n        activityType: payload.type || 'user_activity',\n        details: {\n          ...payload,\n          name: user.lastName + \" \" + user.firstName\n        }\n      }));\n      setTimeout(() => recentLogs.delete(key), 5000);\n    };\n\n    // 📌 Thoát fullscreen\n    const handleFullscreenChange = () => {\n      if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {\n        logOnce(\"exit_fullscreen\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"EF\",\n          action: \"exit_fullscreen\",\n          detail: JSON.stringify({\n            reason: \"User exited fullscreen mode\"\n          })\n        });\n      }\n    };\n\n    // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === \"hidden\") {\n        logOnce(\"tab_blur\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"TB\",\n          action: \"tab_blur\",\n          detail: JSON.stringify({\n            message: \"User switched tab or minimized window\"\n          })\n        });\n      }\n    };\n\n    // 📌 Copy nội dung\n    const handleCopy = () => {\n      logOnce(\"copy_detected\", {\n        studentId: user.id,\n        attemptId,\n        examId,\n        code: \"COP\",\n        action: \"copy_detected\",\n        detail: JSON.stringify({\n          message: \"User copied content\"\n        })\n      });\n    };\n\n    // 📌 Phím đáng ngờ\n    const handleSuspiciousKey = e => {\n      const suspiciousKeys = [\"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"];\n      const combo = \"\".concat(e.ctrlKey ? \"Ctrl+\" : \"\").concat(e.shiftKey ? \"Shift+\" : \"\").concat(e.altKey ? \"Alt+\" : \"\").concat(e.metaKey ? \"Meta+\" : \"\").concat(e.key);\n      if (suspiciousKeys.includes(e.key) || combo === \"Ctrl+Shift+I\" || combo === \"Ctrl+Shift+C\") {\n        logOnce(\"key_\".concat(combo), {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"SK\",\n          action: \"suspicious_key\",\n          detail: JSON.stringify({\n            key: e.key,\n            code: e.code,\n            combo\n          })\n        });\n      }\n    };\n    document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n    document.addEventListener(\"copy\", handleCopy);\n    document.addEventListener(\"keydown\", handleSuspiciousKey);\n    return () => {\n      document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      document.removeEventListener(\"copy\", handleCopy);\n      document.removeEventListener(\"keydown\", handleSuspiciousKey);\n    };\n  }, [user.id, examId, attemptId]);\n  useEffect(() => {\n    // Removed all socket event listeners - using API responses instead\n    // Answer save/error status is now handled in submitAnswerWithAttempt action responses\n    // Timer updates are handled via getRemainingTime API calls\n    // Auto-submit is handled via client-side timer logic\n  }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\n  useEffect(() => {\n    localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  // Hàm xử lý chuyển đổi câu hỏi\n  const handleKeyDown = useCallback(e => {\n    // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\n    if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\n      // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\n      e.preventDefault();\n\n      // Nếu không có câu hỏi, thoát khỏi hàm\n      if (!questions || questions.length === 0) return;\n      const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\n      const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\n      if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\n        const prevQuestionId = allQuestions[currentIndex - 1].id;\n        console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\n        navigateToQuestion(prevQuestionId);\n      } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\n        const nextQuestionId = allQuestions[currentIndex + 1].id;\n        console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\n        navigateToQuestion(nextQuestionId);\n      }\n    }\n  }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\n  // Lắng nghe sự kiện bàn phím\n  useEffect(() => {\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [handleKeyDown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full \".concat(isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'),\n    children: [/*#__PURE__*/_jsxDEV(HeaderDoExamPage, {\n      nameExam: exam === null || exam === void 0 ? void 0 : exam.name,\n      onExitFullscreen: handleExitFullscreen,\n      isDarkMode: !isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 13\n    }, this), isAgree ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\",\n      children: [/*#__PURE__*/_jsxDEV(ExamContent, {\n        ref: examContentRef,\n        loading1: loadingLoadExam,\n        isDarkMode: isDarkMode,\n        questionTN: questionTN,\n        questionDS: questionDS,\n        questionTLN: questionTLN,\n        handlers: {\n          handleSelectAnswerTN,\n          handleSelectAnswerDS,\n          handleSelectAnswerTLN,\n          isTNSelected,\n          isDSChecked,\n          getTLNDefaultValue,\n          setQuestionRef: (id, el) => questionRefs.current[id] = el,\n          setSelectedQuestion: id => setSelectedQuestion(id)\n        },\n        settings: {\n          selectedQuestion,\n          isDarkMode,\n          fontSize,\n          imageSize,\n          prefixStatementTN,\n          prefixStatementDS,\n          isTimeUp,\n          markedQuestions,\n          toggleMarkQuestion\n        },\n        isTimeUp: isTimeUp\n        // Để undefined để component tự quyết định dựa trên thiết bị\n        ,\n        initialSingleMode: undefined,\n        handleAutoSubmit: handleAutoSubmit,\n        loadingSubmit: loadingSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 814,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-full shadow-md \".concat(isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"),\n          onClick: () => setIsSidebarOpen(prev => !prev),\n          children: /*#__PURE__*/_jsxDEV(Menu, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: (isSidebarOpen || window.innerWidth > 1024) && /*#__PURE__*/_jsxDEV(ExamSidebar, {\n          isDarkMode: isDarkMode,\n          setIsDarkMode: setIsDarkMode,\n          fontSize: fontSize,\n          handleFontSizeChange: handleFontSizeChange,\n          imageSize: imageSize,\n          handleImageSizeChange: handleImageSizeChange,\n          questionTN: questionTN,\n          questionDS: questionDS,\n          questionTLN: questionTLN,\n          scrollToQuestion: navigateToQuestion,\n          selectedQuestion: selectedQuestion,\n          markedQuestions: markedQuestions,\n          toggleMarkQuestion: toggleMarkQuestion,\n          handleAutoSubmit: handleAutoSubmit,\n          loadingSubmit: loadingSubmit,\n          loadingLoadExam: loadingLoadExam,\n          exam: exam,\n          remainingTime: remainingTime,\n          formatTime: formatTime,\n          questions: questions,\n          singleQuestionMode: ((_examContentRef$curre = examContentRef.current) === null || _examContentRef$curre === void 0 ? void 0 : _examContentRef$curre.isSingleQuestionMode()) || false,\n          setSingleQuestionMode: value => {\n            if (examContentRef.current) {\n              // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\n              examContentRef.current.setSingleQuestionMode(value);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 812,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(ExamRegulationModal, {\n        onClose: () => {\n          // Sử dụng API để leave exam nếu có attemptId\n          if (attemptId) {\n            dispatch(leaveExam({\n              examId,\n              attemptId\n            }));\n          }\n          navigate(\"/practice/exam/\".concat(examId));\n        },\n        isOpen: !isAgree,\n        onStartExam: handleFullScreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 897,\n      columnNumber: 17\n    }, this), (exam === null || exam === void 0 ? void 0 : exam.testDuration) && isAgree && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-2 rounded-md left-2 px-4 py-2\\n                    \".concat(isTimeBlinking ? 'bg-red-600 animate-pulse' : 'bg-slate-700 bg-opacity-80', \"\\n                    text-white z-50 transition-colors duration-300\"),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-bold\",\n          children: [formatTime(remainingTime), \" ph\\xFAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 913,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 809,\n    columnNumber: 9\n  }, this);\n};\n_s(DoExamPage, \"RSknvD3yxJJfmTYKPqRha5FBvds=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DoExamPage;\nexport default DoExamPage;\nvar _c;\n$RefreshReg$(_c, \"DoExamPage\");", "map": {"version": 3, "names": ["HeaderDoExamPage", "useDispatch", "useSelector", "useState", "useEffect", "useRef", "useCallback", "fetchPublicQuestionsByExamId", "fetchPublicExamById", "useParams", "setErrorMessage", "setSuccessMessage", "useNavigate", "fetchAnswersByAttempt", "setAnswers", "ExamRegulationModal", "AnimatePresence", "<PERSON><PERSON>", "ExamSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestFullscreen", "exitFullscreen", "isFullscreen", "setRemainingTime", "summitExam", "setSaveQuestions", "setErrorQuestions", "getRemainingTime", "logUserActivity", "submitAnswerWithAttempt", "leaveExam", "jsxDEV", "_jsxDEV", "DoExamPage", "_s", "_examContentRef$curre", "examId", "dispatch", "navigate", "exam", "state", "exams", "questions", "answers", "fontSize", "setFontSize", "imageSize", "setImageSize", "questionRefs", "isAgree", "setIsAgree", "attemptId", "setAttemptId", "attemptRef", "isSidebarOpen", "setIsSidebarOpen", "flag", "setFlag", "startTime1", "setStartTime1", "hasSubmittedRef", "examRef", "examContentRef", "current", "acceptDoExam", "concat", "user", "auth", "remainingTime", "saveQuestions", "errorQuestions", "doExam", "markedQuestions", "setMarkedQuestions", "Set", "timeWarningShown", "setTimeWarningShown", "fiveMinutes", "oneMinute", "isTimeBlinking", "setIsTimeBlinking", "prefixStatementTN", "prefixStatementDS", "selectedQuestion", "setSelectedQuestion", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "loadingSubmit", "setLoadingSubmit", "loadingLoadExam", "setLoadingLoadExam", "isTimeUp", "setIsTimeUp", "questionTN", "setQuestionTN", "questionDS", "setQuestionDS", "questionTLN", "setQuestionTLN", "answerTN", "setAnswerTN", "answerTLN", "setAnswerTLN", "dsAnswers", "setDsAnswers", "document", "addEventListener", "e", "preventDefault", "addQuestion", "questionId", "includes", "removeErrorQuestion", "addErrorQuestion", "removeQuestion", "filter", "id", "toggleMarkQuestion", "prev", "newSet", "has", "delete", "add", "handleExitFullscreen", "err", "console", "warn", "handleFontSizeChange", "Number", "target", "value", "handleImageSizeChange", "formatTime", "seconds", "min", "String", "Math", "floor", "padStart", "sec", "handleFullScreen", "response", "fetch", "method", "headers", "data", "json", "ok", "Error", "message", "startTime", "log", "isCheatingCheckEnabled", "success", "setTimeout", "error", "alert", "testDuration", "start", "Date", "now", "elapsedSeconds", "totalSeconds", "remaining", "max", "then", "result", "_result$payload", "payload", "undefined", "catch", "handleAutoSubmit", "unwrap", "safeAttemptId", "currentExam", "seeCorrectAnswer", "examNull", "cantSeeAnswer", "navigateToQuestion", "isSingleQuestionMode", "goToQuestionById", "element", "querySelector", "offset", "y", "getBoundingClientRect", "top", "window", "scrollY", "scrollTo", "behavior", "refElement", "scrollToQuestion", "handleSelectAnswerTN", "statementId", "type", "newAnswer", "answerContent", "typeOfQuestion", "endsWith", "handleSelectAnswerDS", "<PERSON><PERSON><PERSON><PERSON>", "currentAnswers", "existing", "find", "ans", "answer", "updatedAnswers", "map", "push", "stringify", "handleSelectAnswerTLN", "trim", "formattedAnswer", "replace", "questionsToMarkAsSaved", "size", "for<PERSON>ach", "clear", "frameId", "requestAnimationFrame", "questionIds", "cancelAnimationFrame", "isTNSelected", "isSelected", "some", "isDSChecked", "bool", "_dsAnswers$questionId", "_dsAnswers$questionId2", "a", "length", "getTLNDefaultValue", "_matched$answerConten", "matched", "content", "question", "Array", "isArray", "tn", "tln", "dsMap", "parsed", "checkTimeWarnings", "time", "syncTimeInterval", "setInterval", "_result$payload2", "interval", "clearInterval", "recentLogs", "logOnce", "key", "activityType", "details", "name", "lastName", "firstName", "handleFullscreenChange", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "studentId", "code", "action", "detail", "reason", "handleVisibilityChange", "visibilityState", "handleCopy", "handleSuspiciousKey", "<PERSON><PERSON><PERSON><PERSON>", "combo", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "removeEventListener", "setItem", "handleKeyDown", "allQuestions", "currentIndex", "findIndex", "q", "prevQuestionId", "nextQuestionId", "className", "children", "nameExam", "onExitFullscreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "loading1", "handlers", "setQuestionRef", "el", "settings", "initialSingleMode", "onClick", "innerWidth", "singleQuestionMode", "setSingleQuestionMode", "onClose", "isOpen", "onStartExam", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/DoExamPage.jsx"], "sourcesContent": ["import HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\r\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\r\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\r\nimport { AnimatePresence } from \"framer-motion\";\r\nimport { Menu } from \"lucide-react\";\r\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\r\nimport ExamContent from \"../../../components/questions/ExamContent\";\r\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\r\nimport {\r\n    setRemainingTime,\r\n    summitExam,\r\n    setSaveQuestions,\r\n    setErrorQuestions,\r\n    getRemainingTime,\r\n    logUserActivity,\r\n    submitAnswerWithAttempt,\r\n    leaveExam,\r\n} from \"../../../features/doExam/doExamSlice\";\r\n\r\nconst DoExamPage = () => {\r\n    const { examId } = useParams();\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { exam } = useSelector(state => state.exams);\r\n    const { questions } = useSelector(state => state.questions);\r\n    const { answers } = useSelector(state => state.answers);\r\n    const [fontSize, setFontSize] = useState(14); // 14px mặc định\r\n    const [imageSize, setImageSize] = useState(12); // đơn vị: rem\r\n    const questionRefs = useRef([]);\r\n    const [isAgree, setIsAgree] = useState(false);\r\n    const [attemptId, setAttemptId] = useState(null);\r\n    const attemptRef = useRef(null);\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const [flag, setFlag] = useState(false);\r\n    const [startTime1, setStartTime1] = useState(null);\r\n    const hasSubmittedRef = useRef(false);\r\n    const examRef = useRef(null);\r\n    const examContentRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        examRef.current = exam;\r\n        if (exam?.acceptDoExam === false) {\r\n            navigate(`/practice/exam/${examId}`)\r\n        }\r\n    }, [exam]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchPublicExamById(examId));\r\n        }\r\n    }, [dispatch, examId]);\r\n\r\n\r\n    const { user } = useSelector((state) => state.auth);\r\n    const { remainingTime, saveQuestions, errorQuestions } = useSelector((state) => state.doExam);\r\n\r\n\r\n    const [markedQuestions, setMarkedQuestions] = useState(new Set());\r\n    const [timeWarningShown, setTimeWarningShown] = useState({\r\n        fiveMinutes: false,\r\n        oneMinute: false\r\n    });\r\n    const [isTimeBlinking, setIsTimeBlinking] = useState(false);\r\n\r\n    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [isDarkMode, setIsDarkMode] = useState(() => {\r\n        const saved = localStorage.getItem(\"isDarkMode\");\r\n        return saved ? JSON.parse(saved) : false;\r\n    });\r\n\r\n    const [loadingSubmit, setLoadingSubmit] = useState(false);\r\n    const [loadingLoadExam, setLoadingLoadExam] = useState(false);\r\n    const [isTimeUp, setIsTimeUp] = useState(false);\r\n\r\n    const [questionTN, setQuestionTN] = useState([]);\r\n    const [questionDS, setQuestionDS] = useState([]);\r\n    const [questionTLN, setQuestionTLN] = useState([]);\r\n\r\n    const [answerTN, setAnswerTN] = useState([]);\r\n    const [answerTLN, setAnswerTLN] = useState([]);\r\n    const [dsAnswers, setDsAnswers] = useState({});\r\n\r\n    document.addEventListener(\"copy\", (e) => {\r\n        e.preventDefault();\r\n    });\r\n\r\n    const addQuestion = (questionId) => {\r\n        if (!saveQuestions.includes(questionId)) {\r\n            dispatch(setSaveQuestions([...saveQuestions, questionId]));\r\n        }\r\n        removeErrorQuestion(questionId);\r\n    };\r\n\r\n    const addErrorQuestion = (questionId) => {\r\n        if (!errorQuestions.includes(questionId)) {\r\n            dispatch(setErrorQuestions([...errorQuestions, questionId]));\r\n        }\r\n        removeQuestion(questionId);\r\n    };\r\n\r\n    const removeQuestion = (questionId) => {\r\n        dispatch(setSaveQuestions(saveQuestions.filter(id => id !== questionId)));\r\n    };\r\n\r\n    const removeErrorQuestion = (questionId) => {\r\n        dispatch(setErrorQuestions(errorQuestions.filter(id => id !== questionId)));\r\n    };\r\n\r\n    // Hàm đánh dấu câu hỏi để xem lại sau\r\n    const toggleMarkQuestion = (questionId) => {\r\n        setMarkedQuestions(prev => {\r\n            const newSet = new Set(prev);\r\n            if (newSet.has(questionId)) {\r\n                newSet.delete(questionId);\r\n            } else {\r\n                newSet.add(questionId);\r\n            }\r\n            return newSet;\r\n        });\r\n    };\r\n\r\n\r\n    const handleExitFullscreen = () => {\r\n        try {\r\n            exitFullscreen();\r\n        } catch (err) {\r\n            // Chỉ ghi log lỗi, không bắt lỗi\r\n            console.warn(\"Không thể thoát fullscreen:\", err);\r\n        }\r\n    };\r\n\r\n    const handleFontSizeChange = (e) => {\r\n        setFontSize(Number(e.target.value));\r\n    };\r\n\r\n    const handleImageSizeChange = (e) => {\r\n        setImageSize(Number(e.target.value));\r\n    };\r\n\r\n    const formatTime = (seconds) => {\r\n        const min = String(Math.floor(seconds / 60)).padStart(2, '0');\r\n        const sec = String(seconds % 60).padStart(2, '0');\r\n        return `${min}:${sec}`;\r\n    };\r\n\r\n    const handleFullScreen = async () => {\r\n        setLoadingLoadExam(true);\r\n\r\n        try {\r\n            // Gọi API join exam thay vì socket\r\n            const response = await fetch(`/api/v1/user/join-exam/${examId}`, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n                    'Content-Type': 'application/json'\r\n                }\r\n            });\r\n\r\n            const data = await response.json();\r\n\r\n            if (!response.ok) {\r\n                throw new Error(data.message || 'Lỗi khi tham gia bài thi');\r\n            }\r\n\r\n            // Xử lý khi join exam thành công\r\n            const { attemptId, startTime } = data;\r\n            console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\r\n\r\n            setIsAgree(true);\r\n            attemptRef.current = attemptId;\r\n            setAttemptId(attemptId);\r\n\r\n            if (examId) {\r\n                dispatch(fetchPublicQuestionsByExamId(examId));\r\n            }\r\n            setStartTime1(startTime);\r\n\r\n            if (!exam?.isCheatingCheckEnabled) {\r\n                setLoadingLoadExam(false);\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const success = await requestFullscreen();\r\n                if (success) {\r\n                    setTimeout(() => {\r\n                        setLoadingLoadExam(false);\r\n                    }, 800);\r\n                } else {\r\n                    console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\r\n                    setLoadingLoadExam(false);\r\n                }\r\n            } catch (err) {\r\n                console.error(\"❌ Lỗi khi bật fullscreen:\", err);\r\n                alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\r\n                setLoadingLoadExam(false);\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi tham gia bài thi:\", error);\r\n            dispatch(setErrorMessage(\"Lỗi: \" + error.message));\r\n            setLoadingLoadExam(false);\r\n            navigate(`/practice/exam/${examId}`);\r\n        }\r\n    };\r\n\r\n    // Removed socket-based exam_started listener - now handled in handleFullScreen\r\n\r\n    useEffect(() => {\r\n        if (exam?.testDuration && startTime1) {\r\n            const start = new Date(startTime1);\r\n            const now = new Date();\r\n            const elapsedSeconds = Math.floor((now - start) / 1000);\r\n            const totalSeconds = exam.testDuration * 60;\r\n            const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\r\n            dispatch(setRemainingTime(remaining));\r\n\r\n            // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket\r\n            if (attemptId) {\r\n                dispatch(getRemainingTime({ examId, attemptId }))\r\n                    .then((result) => {\r\n                        if (result.payload?.remainingTime !== undefined) {\r\n                            dispatch(setRemainingTime(result.payload.remainingTime));\r\n                        }\r\n                    })\r\n                    .catch((error) => {\r\n                        console.error(\"Lỗi khi lấy thời gian từ server:\", error);\r\n                    });\r\n            }\r\n        }\r\n    }, [startTime1, exam, attemptId, examId, dispatch]);\r\n\r\n    useEffect(() => {\r\n        if (flag) return\r\n        if (!remainingTime) setFlag(true)\r\n    }, [remainingTime])\r\n\r\n    const handleAutoSubmit = async () => {\r\n        if (hasSubmittedRef.current) {\r\n            console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\r\n            return;\r\n        }\r\n        hasSubmittedRef.current = true; // Đánh dấu đã submit\r\n        console.log(\"Kiểm tra attemptId:\", attemptId);\r\n        if (!attemptId) {\r\n            console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\r\n            return;\r\n        }\r\n\r\n        console.log(\"Đang nộp bài với attemptId:\", attemptId);\r\n        dispatch(setSaveQuestions([]));\r\n        setLoadingSubmit(true);\r\n\r\n        try {\r\n            // Sử dụng API thay vì socket để nộp bài\r\n            const result = await dispatch(summitExam(attemptId)).unwrap();\r\n            console.log(\"Nộp bài thành công:\", result);\r\n\r\n            // Xử lý khi nộp bài thành công\r\n            dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\r\n\r\n            // Thoát fullscreen mà không bắt lỗi\r\n            try {\r\n                exitFullscreen();\r\n            } catch (err) {\r\n                // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\r\n                console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\r\n            }\r\n\r\n            const safeAttemptId = attemptRef.current;\r\n            const currentExam = examRef.current;\r\n\r\n            if (!safeAttemptId) {\r\n                console.error(\"Không có attemptId khi navigate!\");\r\n                return;\r\n            }\r\n\r\n            // Log để debug\r\n            console.log(\"Current exam state:\", currentExam);\r\n            console.log(\"Attempt ID:\", safeAttemptId);\r\n\r\n            if (!currentExam || !currentExam.seeCorrectAnswer) {\r\n                console.log(\"Chuyển về trang danh sách do:\", {\r\n                    examNull: !currentExam,\r\n                    cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\r\n                });\r\n                navigate(`/practice/exam/${examId}`);\r\n                return;\r\n            }\r\n\r\n            navigate(`/practice/exam/attempt/${safeAttemptId}/score`);\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi nộp bài:\", error);\r\n            setLoadingSubmit(false);\r\n            dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\r\n            hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\r\n\r\n            // Thử nộp lại sau 3 giây nếu lỗi xảy ra\r\n            setTimeout(() => {\r\n                if (!loadingSubmit && attemptRef.current) {\r\n                    console.log(\"Thử nộp bài lại sau lỗi...\");\r\n                    handleAutoSubmit();\r\n                }\r\n            }, 5000);\r\n        }\r\n    };\r\n\r\n    // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\r\n    const navigateToQuestion = useCallback((questionId) => {\r\n        setSelectedQuestion(questionId);\r\n\r\n        // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\r\n        if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\r\n            // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\r\n            examContentRef.current.goToQuestionById(questionId);\r\n        } else {\r\n            // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\r\n            // Tìm phần tử câu hỏi bằng querySelector\r\n            setTimeout(() => {\r\n                // Thử tìm phần tử bằng data-question-id\r\n                const element = document.querySelector(`[data-question-id=\"${questionId}\"]`);\r\n\r\n                if (element) {\r\n                    const offset = 80; // chiều cao của header sticky\r\n                    const y = element.getBoundingClientRect().top + window.scrollY - offset;\r\n                    window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                } else {\r\n                    // Fallback: Sử dụng refs\r\n                    const refElement = questionRefs.current[questionId];\r\n\r\n                    if (refElement) {\r\n                        const offset = 80; // chiều cao của header sticky\r\n                        const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\r\n                        window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                    }\r\n                }\r\n            }, 0);\r\n        }\r\n    }, [questionRefs, examContentRef]);\r\n\r\n    // Alias cho navigateToQuestion để tương thích với các component khác\r\n    const scrollToQuestion = navigateToQuestion;\r\n\r\n    const handleSelectAnswerTN = (questionId, statementId, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const newAnswer = {\r\n            questionId,\r\n            answerContent: statementId,\r\n            typeOfQuestion: type,\r\n        };\r\n        dispatch(setAnswers(newAnswer));\r\n\r\n        // Sử dụng API thay vì socket\r\n        dispatch(submitAnswerWithAttempt({\r\n            questionId,\r\n            answerContent: statementId,\r\n            type,\r\n            attemptId\r\n        })).then((result) => {\r\n            if (result.type.endsWith('/fulfilled')) {\r\n                // Answer submitted successfully\r\n                console.log(\"Đã lưu câu trả lời thành công\");\r\n            } else {\r\n                // Handle error\r\n                console.error(\"Lỗi khi lưu câu trả lời:\", result.error);\r\n            }\r\n        });\r\n    };\r\n\r\n    const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const currentAnswers = dsAnswers[questionId] || [];\r\n\r\n        const existing = currentAnswers.find(ans => ans.statementId === statementId);\r\n\r\n        // 🔁 Nếu đáp án đã giống thì không gửi lại\r\n        if (existing && existing.answer === selectedAnswer) {\r\n            return\r\n        }\r\n\r\n        const updatedAnswers = currentAnswers.map(ans =>\r\n            ans.statementId === statementId\r\n                ? { ...ans, answer: selectedAnswer }\r\n                : ans\r\n        );\r\n\r\n        // Nếu chưa có statement này\r\n        if (!existing) {\r\n            updatedAnswers.push({ statementId, answer: selectedAnswer });\r\n        }\r\n\r\n        dispatch(setAnswers({ questionId, answerContent: JSON.stringify(updatedAnswers), typeOfQuestion: \"DS\" }));\r\n\r\n        // Sử dụng API thay vì socket\r\n        dispatch(submitAnswerWithAttempt({\r\n            questionId,\r\n            answerContent: updatedAnswers,\r\n            type: \"DS\",\r\n            attemptId\r\n        })).then((result) => {\r\n            if (result.type.endsWith('/fulfilled')) {\r\n                console.log(\"Đã lưu câu trả lời DS thành công\");\r\n            } else {\r\n                console.error(\"Lỗi khi lưu câu trả lời DS:\", result.error);\r\n            }\r\n        });\r\n    };\r\n\r\n\r\n    const handleSelectAnswerTLN = (questionId, answerContent, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        if (!answerContent || answerContent.trim() === \"\") {\r\n            return;\r\n        }\r\n\r\n        const formattedAnswer = answerContent.trim().replace(\",\", \".\");\r\n        dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));\r\n\r\n        // Sử dụng API thay vì socket\r\n        dispatch(submitAnswerWithAttempt({\r\n            questionId,\r\n            answerContent: formattedAnswer,\r\n            type,\r\n            attemptId\r\n        })).then((result) => {\r\n            if (result.type.endsWith('/fulfilled')) {\r\n                console.log(\"Đã lưu câu trả lời TLN thành công\");\r\n            } else {\r\n                console.error(\"Lỗi khi lưu câu trả lời TLN:\", result.error);\r\n            }\r\n        });\r\n    }\r\n\r\n    // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\r\n    const questionsToMarkAsSaved = useRef(new Set());\r\n\r\n    // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\r\n    useEffect(() => {\r\n        if (questionsToMarkAsSaved.current.size > 0) {\r\n            questionsToMarkAsSaved.current.forEach(questionId => {\r\n                if (!saveQuestions.includes(questionId)) {\r\n                    addQuestion(questionId);\r\n                }\r\n            });\r\n            questionsToMarkAsSaved.current.clear();\r\n        }\r\n    }, [saveQuestions, addQuestion]);\r\n\r\n    // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\r\n    useEffect(() => {\r\n        // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\r\n        const frameId = requestAnimationFrame(() => {\r\n            if (questionsToMarkAsSaved.current.size > 0) {\r\n                const questionIds = [...questionsToMarkAsSaved.current];\r\n                questionsToMarkAsSaved.current.clear();\r\n\r\n                // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\r\n                questionIds.forEach(questionId => {\r\n                    if (!saveQuestions.has(questionId)) {\r\n                        addQuestion(questionId);\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        return () => cancelAnimationFrame(frameId);\r\n    });\r\n\r\n    const isTNSelected = useCallback((questionId, statementId) => {\r\n        const isSelected = answerTN.some(\r\n            (ans) =>\r\n                ans.questionId === questionId &&\r\n                ans.answerContent &&\r\n                String(ans.answerContent) === String(statementId)\r\n        );\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.has(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [answerTN, saveQuestions]);\r\n\r\n    const isDSChecked = useCallback((questionId, statementId, bool) => {\r\n        const isSelected = dsAnswers[questionId]?.some(\r\n            (a) => a.statementId === statementId && a.answer === bool\r\n        ) || false;\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.has(questionId) && dsAnswers[questionId]?.length === 4) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [dsAnswers, saveQuestions]);\r\n\r\n    const getTLNDefaultValue = useCallback((questionId) => {\r\n        const matched = answerTLN.find((ans) => ans.questionId === questionId);\r\n        const content = matched?.answerContent?.replace(/^\"|\"$/g, \"\") || \"\";\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (content && !saveQuestions.has(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return content;\r\n    }, [answerTLN, saveQuestions]);\r\n\r\n    // useEffect(() => {\r\n    //     if (examId) {\r\n    //         dispatch(fetchPublicQuestionsByExamId(examId));\r\n    //     }\r\n    // }, [dispatch, examId]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            setQuestionTN(questions.filter((question) => question.typeOfQuestion === \"TN\"));\r\n            setQuestionDS(questions.filter((question) => question.typeOfQuestion === \"DS\"));\r\n            setQuestionTLN(questions.filter((question) => question.typeOfQuestion === \"TLN\"));\r\n        }\r\n    }, [questions]);\r\n\r\n    useEffect(() => {\r\n        // Kiểm tra answers có phải là mảng không\r\n        if (!Array.isArray(answers) || answers.length === 0) return;\r\n\r\n        const tn = [];\r\n        const tln = [];\r\n        const dsMap = {};\r\n\r\n        // Sử dụng for...of thay vì forEach để tránh lỗi\r\n        for (const answer of answers) {\r\n            if (answer.typeOfQuestion === \"TN\") {\r\n                tn.push(answer);\r\n            } else if (answer.typeOfQuestion === \"TLN\") {\r\n                tln.push(answer);\r\n            } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\r\n                try {\r\n                    const parsed = JSON.parse(answer.answerContent);\r\n                    dsMap[answer.questionId] = parsed;\r\n                } catch (err) {\r\n                    console.error(\"Lỗi parse DS answerContent:\", err);\r\n                }\r\n            }\r\n        }\r\n\r\n        setAnswerTN(tn);\r\n        setAnswerTLN(tln);\r\n        setDsAnswers(dsMap);\r\n\r\n        // Note: Score calculation is now handled when submitting exam\r\n        // No need to calculate score in real-time\r\n    }, [answers]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (attemptId) {\r\n            dispatch(fetchAnswersByAttempt(attemptId));\r\n        }\r\n    }, [dispatch, attemptId]);\r\n\r\n    useEffect(() => {\r\n        if (!exam?.testDuration || remainingTime === null || !isAgree) return;\r\n\r\n        // Kiểm tra và hiển thị cảnh báo thời gian\r\n        const checkTimeWarnings = (time) => {\r\n            // Cảnh báo khi còn 5 phút\r\n            if (time === 300 && !timeWarningShown.fiveMinutes) {\r\n                setTimeWarningShown(prev => ({ ...prev, fiveMinutes: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Tắt hiệu ứng nhấp nháy sau 10 giây\r\n                setTimeout(() => {\r\n                    setIsTimeBlinking(false);\r\n                }, 10000);\r\n            }\r\n\r\n            // Cảnh báo khi còn 1 phút\r\n            if (time === 60 && !timeWarningShown.oneMinute) {\r\n                setTimeWarningShown(prev => ({ ...prev, oneMinute: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\r\n            }\r\n        };\r\n\r\n        // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API\r\n        const syncTimeInterval = setInterval(() => {\r\n            if (attemptId) {\r\n                dispatch(getRemainingTime({ examId, attemptId }))\r\n                    .then((result) => {\r\n                        if (result.payload?.remainingTime !== undefined) {\r\n                            dispatch(setRemainingTime(result.payload.remainingTime));\r\n                        }\r\n                    })\r\n                    .catch((error) => {\r\n                        console.error(\"Lỗi khi đồng bộ thời gian:\", error);\r\n                    });\r\n            }\r\n        }, 30000); // Đồng bộ thời gian mỗi 30 giây\r\n\r\n        const interval = setInterval(() => {\r\n            dispatch(setRemainingTime((prev) => {\r\n                if (prev <= 1) { // dùng <=1 để đảm bảo không bị âm\r\n                    clearInterval(interval);\r\n                    clearInterval(syncTimeInterval);\r\n                    // Đánh dấu là đã hết thời gian\r\n                    setIsTimeUp(true);\r\n                    setIsTimeBlinking(false);\r\n                    // Thử nộp bài\r\n                    handleAutoSubmit();\r\n                    return 0;\r\n                }\r\n\r\n                // Kiểm tra cảnh báo thời gian\r\n                checkTimeWarnings(prev);\r\n\r\n                return prev - 1;\r\n            }));\r\n        }, 1000);\r\n\r\n        return () => {\r\n            clearInterval(interval);\r\n            clearInterval(syncTimeInterval);\r\n        };\r\n    }, [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId]);// Chỉ phụ thuộc vào các giá trị cần thiết\r\n\r\n    // Removed socket connection management - using API only\r\n\r\n    // frontend\r\n    useEffect(() => {\r\n        if (!attemptId || !user?.id || !examId || attemptId === null || attemptId === undefined) return;\r\n        if (!exam?.isCheatingCheckEnabled) return;\r\n        console.log(\"Đã bật theo dõi hành vi gian lận\");\r\n\r\n\r\n        const recentLogs = new Set(); // chống log lặp\r\n        const logOnce = (key, payload) => {\r\n            if (!exam?.isCheatingCheckEnabled || recentLogs.has(key)) return;\r\n\r\n            recentLogs.add(key);\r\n\r\n            // Sử dụng API thay vì socket\r\n            dispatch(logUserActivity({\r\n                examId,\r\n                attemptId,\r\n                activityType: payload.type || 'user_activity',\r\n                details: {\r\n                    ...payload,\r\n                    name: user.lastName + \" \" + user.firstName\r\n                }\r\n            }));\r\n\r\n            setTimeout(() => recentLogs.delete(key), 5000);\r\n        };\r\n\r\n        // 📌 Thoát fullscreen\r\n        const handleFullscreenChange = () => {\r\n            if (!document.fullscreenElement &&\r\n                !document.webkitFullscreenElement &&\r\n                !document.mozFullScreenElement &&\r\n                !document.msFullscreenElement) {\r\n                logOnce(\"exit_fullscreen\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"EF\",\r\n                    action: \"exit_fullscreen\",\r\n                    detail: JSON.stringify({ reason: \"User exited fullscreen mode\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\r\n        const handleVisibilityChange = () => {\r\n            if (document.visibilityState === \"hidden\") {\r\n                logOnce(\"tab_blur\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"TB\",\r\n                    action: \"tab_blur\",\r\n                    detail: JSON.stringify({ message: \"User switched tab or minimized window\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Copy nội dung\r\n        const handleCopy = () => {\r\n            logOnce(\"copy_detected\", {\r\n                studentId: user.id,\r\n                attemptId,\r\n                examId,\r\n                code: \"COP\",\r\n                action: \"copy_detected\",\r\n                detail: JSON.stringify({ message: \"User copied content\" }),\r\n            });\r\n        };\r\n\r\n        // 📌 Phím đáng ngờ\r\n        const handleSuspiciousKey = (e) => {\r\n            const suspiciousKeys = [\r\n                \"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"\r\n            ];\r\n            const combo = `${e.ctrlKey ? \"Ctrl+\" : \"\"}${e.shiftKey ? \"Shift+\" : \"\"}${e.altKey ? \"Alt+\" : \"\"}${e.metaKey ? \"Meta+\" : \"\"}${e.key}`;\r\n\r\n            if (\r\n                suspiciousKeys.includes(e.key) ||\r\n                combo === \"Ctrl+Shift+I\" ||\r\n                combo === \"Ctrl+Shift+C\"\r\n            ) {\r\n                logOnce(`key_${combo}`, {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"SK\",\r\n                    action: \"suspicious_key\",\r\n                    detail: JSON.stringify({ key: e.key, code: e.code, combo }),\r\n                });\r\n            }\r\n        };\r\n\r\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n        document.addEventListener(\"copy\", handleCopy);\r\n        document.addEventListener(\"keydown\", handleSuspiciousKey);\r\n\r\n        return () => {\r\n            document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n            document.removeEventListener(\"copy\", handleCopy);\r\n            document.removeEventListener(\"keydown\", handleSuspiciousKey);\r\n        };\r\n    }, [user.id, examId, attemptId]);\r\n\r\n\r\n    useEffect(() => {\r\n        // Removed all socket event listeners - using API responses instead\r\n        // Answer save/error status is now handled in submitAnswerWithAttempt action responses\r\n        // Timer updates are handled via getRemainingTime API calls\r\n        // Auto-submit is handled via client-side timer logic\r\n    }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\r\n\r\n    useEffect(() => {\r\n        localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\r\n    }, [isDarkMode]);\r\n\r\n    // Hàm xử lý chuyển đổi câu hỏi\r\n    const handleKeyDown = useCallback((e) => {\r\n        // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\r\n        if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\r\n            // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\r\n            e.preventDefault();\r\n\r\n            // Nếu không có câu hỏi, thoát khỏi hàm\r\n            if (!questions || questions.length === 0) return;\r\n\r\n            const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\r\n            const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\r\n\r\n            if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\r\n                const prevQuestionId = allQuestions[currentIndex - 1].id;\r\n                console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\r\n                navigateToQuestion(prevQuestionId);\r\n            } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\r\n                const nextQuestionId = allQuestions[currentIndex + 1].id;\r\n                console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\r\n                navigateToQuestion(nextQuestionId);\r\n            }\r\n        }\r\n    }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\r\n    // Lắng nghe sự kiện bàn phím\r\n    useEffect(() => {\r\n        document.addEventListener(\"keydown\", handleKeyDown);\r\n        return () => {\r\n            document.removeEventListener(\"keydown\", handleKeyDown);\r\n        };\r\n    }, [handleKeyDown]);\r\n\r\n    return (\r\n        <div className={`flex flex-col h-full ${isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'}`}>\r\n            <HeaderDoExamPage nameExam={exam?.name} onExitFullscreen={handleExitFullscreen} isDarkMode={!isDarkMode} />\r\n            {isAgree ? (\r\n                <div className=\"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\">\r\n                    {/* Main Content */}\r\n                    <ExamContent\r\n                        ref={examContentRef}\r\n                        loading1={loadingLoadExam}\r\n                        isDarkMode={isDarkMode}\r\n                        questionTN={questionTN}\r\n                        questionDS={questionDS}\r\n                        questionTLN={questionTLN}\r\n                        handlers={{\r\n                            handleSelectAnswerTN,\r\n                            handleSelectAnswerDS,\r\n                            handleSelectAnswerTLN,\r\n                            isTNSelected,\r\n                            isDSChecked,\r\n                            getTLNDefaultValue,\r\n                            setQuestionRef: (id, el) => (questionRefs.current[id] = el),\r\n                            setSelectedQuestion: (id) => setSelectedQuestion(id)\r\n                        }}\r\n                        settings={{\r\n                            selectedQuestion,\r\n                            isDarkMode,\r\n                            fontSize,\r\n                            imageSize,\r\n                            prefixStatementTN,\r\n                            prefixStatementDS,\r\n                            isTimeUp,\r\n                            markedQuestions,\r\n                            toggleMarkQuestion\r\n                        }}\r\n                        isTimeUp={isTimeUp}\r\n                        // Để undefined để component tự quyết định dựa trên thiết bị\r\n                        initialSingleMode={undefined}\r\n                        handleAutoSubmit={handleAutoSubmit}\r\n                        loadingSubmit={loadingSubmit}\r\n                    />\r\n\r\n\r\n                    {/* Button toggle cho mobile */}\r\n                    <div className=\"fixed bottom-4 right-4 z-50 lg:hidden\">\r\n                        <button\r\n                            className={`p-2 rounded-full shadow-md ${isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"}`}\r\n                            onClick={() => setIsSidebarOpen(prev => !prev)}\r\n                        >\r\n                            <Menu />\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Sidebar chính */}\r\n                    <AnimatePresence>\r\n                        {(isSidebarOpen || window.innerWidth > 1024) && (\r\n                            <ExamSidebar\r\n                                isDarkMode={isDarkMode}\r\n                                setIsDarkMode={setIsDarkMode}\r\n                                fontSize={fontSize}\r\n                                handleFontSizeChange={handleFontSizeChange}\r\n                                imageSize={imageSize}\r\n                                handleImageSizeChange={handleImageSizeChange}\r\n                                questionTN={questionTN}\r\n                                questionDS={questionDS}\r\n                                questionTLN={questionTLN}\r\n                                scrollToQuestion={navigateToQuestion}\r\n                                selectedQuestion={selectedQuestion}\r\n                                markedQuestions={markedQuestions}\r\n                                toggleMarkQuestion={toggleMarkQuestion}\r\n                                handleAutoSubmit={handleAutoSubmit}\r\n                                loadingSubmit={loadingSubmit}\r\n                                loadingLoadExam={loadingLoadExam}\r\n                                exam={exam}\r\n                                remainingTime={remainingTime}\r\n                                formatTime={formatTime}\r\n                                questions={questions}\r\n                                singleQuestionMode={examContentRef.current?.isSingleQuestionMode() || false}\r\n                                setSingleQuestionMode={(value) => {\r\n                                    if (examContentRef.current) {\r\n                                        // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\r\n                                        examContentRef.current.setSingleQuestionMode(value);\r\n                                    }\r\n                                }}\r\n                            />\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                </div>\r\n            ) : (\r\n                <div className=\"flex items-center justify-center\">\r\n                    <ExamRegulationModal\r\n                        onClose={() => {\r\n                            // Sử dụng API để leave exam nếu có attemptId\r\n                            if (attemptId) {\r\n                                dispatch(leaveExam({ examId, attemptId }));\r\n                            }\r\n                            navigate(`/practice/exam/${examId}`);\r\n                        }}\r\n                        isOpen={!isAgree}\r\n                        onStartExam={handleFullScreen}\r\n                    />\r\n                </div>\r\n            )}\r\n\r\n            {exam?.testDuration && isAgree && (\r\n                <div className={`fixed bottom-2 rounded-md left-2 px-4 py-2\r\n                    ${isTimeBlinking\r\n                        ? 'bg-red-600 animate-pulse'\r\n                        : 'bg-slate-700 bg-opacity-80'}\r\n                    text-white z-50 transition-colors duration-300`}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <div className=\"text-sm font-bold\">{formatTime(remainingTime)} phút</div>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DoExamPage;\r\n"], "mappings": ";;AAAA,OAAOA,gBAAgB,MAAM,6CAA6C;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,uCAAuC;AAC1F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,sCAAsC;AACxF,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,WAAW,MAAM,2CAA2C;AACnE,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gCAAgC;AAChG,SACIC,gBAAgB,EAChBC,UAAU,EACVC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,uBAAuB,EACvBC,SAAS,QACN,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAG3B,SAAS,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAK,CAAC,GAAGrC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAClD,MAAM;IAAEC;EAAU,CAAC,GAAGxC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACE,SAAS,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAGzC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;EACvD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM6C,YAAY,GAAG3C,MAAM,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMkD,UAAU,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMyD,eAAe,GAAGvD,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMwD,OAAO,GAAGxD,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMyD,cAAc,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACZyD,OAAO,CAACE,OAAO,GAAGxB,IAAI;IACtB,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,YAAY,MAAK,KAAK,EAAE;MAC9B1B,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;EAEVnC,SAAS,CAAC,MAAM;IACZ,IAAIgC,MAAM,EAAE;MACRC,QAAQ,CAAC7B,mBAAmB,CAAC4B,MAAM,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,CAACC,QAAQ,EAAED,MAAM,CAAC,CAAC;EAGtB,MAAM;IAAE8B;EAAK,CAAC,GAAGhE,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAAC2B,IAAI,CAAC;EACnD,MAAM;IAAEC,aAAa;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAGpE,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAAC+B,MAAM,CAAC;EAG7F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,IAAIuE,GAAG,CAAC,CAAC,CAAC;EACjE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC;IACrD0E,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM8E,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtF,MAAMC,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEtF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,MAAM;IAC/C,MAAMoF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC5C,CAAC,CAAC;EAEF,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2F,eAAe,EAAEC,kBAAkB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM,CAAC+F,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmG,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuG,SAAS,EAAEC,YAAY,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyG,SAAS,EAAEC,YAAY,CAAC,GAAG1G,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C2G,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAGC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAIC,UAAU,IAAK;IAChC,IAAI,CAAC9C,aAAa,CAAC+C,QAAQ,CAACD,UAAU,CAAC,EAAE;MACrC9E,QAAQ,CAACZ,gBAAgB,CAAC,CAAC,GAAG4C,aAAa,EAAE8C,UAAU,CAAC,CAAC,CAAC;IAC9D;IACAE,mBAAmB,CAACF,UAAU,CAAC;EACnC,CAAC;EAED,MAAMG,gBAAgB,GAAIH,UAAU,IAAK;IACrC,IAAI,CAAC7C,cAAc,CAAC8C,QAAQ,CAACD,UAAU,CAAC,EAAE;MACtC9E,QAAQ,CAACX,iBAAiB,CAAC,CAAC,GAAG4C,cAAc,EAAE6C,UAAU,CAAC,CAAC,CAAC;IAChE;IACAI,cAAc,CAACJ,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMI,cAAc,GAAIJ,UAAU,IAAK;IACnC9E,QAAQ,CAACZ,gBAAgB,CAAC4C,aAAa,CAACmD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,UAAU,CAAC,CAAC,CAAC;EAC7E,CAAC;EAED,MAAME,mBAAmB,GAAIF,UAAU,IAAK;IACxC9E,QAAQ,CAACX,iBAAiB,CAAC4C,cAAc,CAACkD,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,UAAU,CAAC,CAAC,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIP,UAAU,IAAK;IACvC1C,kBAAkB,CAACkD,IAAI,IAAI;MACvB,MAAMC,MAAM,GAAG,IAAIlD,GAAG,CAACiD,IAAI,CAAC;MAC5B,IAAIC,MAAM,CAACC,GAAG,CAACV,UAAU,CAAC,EAAE;QACxBS,MAAM,CAACE,MAAM,CAACX,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHS,MAAM,CAACG,GAAG,CAACZ,UAAU,CAAC;MAC1B;MACA,OAAOS,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAGD,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACA3G,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO4G,GAAG,EAAE;MACV;MACAC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,GAAG,CAAC;IACpD;EACJ,CAAC;EAED,MAAMG,oBAAoB,GAAIpB,CAAC,IAAK;IAChCnE,WAAW,CAACwF,MAAM,CAACrB,CAAC,CAACsB,MAAM,CAACC,KAAK,CAAC,CAAC;EACvC,CAAC;EAED,MAAMC,qBAAqB,GAAIxB,CAAC,IAAK;IACjCjE,YAAY,CAACsF,MAAM,CAACrB,CAAC,CAACsB,MAAM,CAACC,KAAK,CAAC,CAAC;EACxC,CAAC;EAED,MAAME,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,MAAMC,GAAG,GAAGJ,MAAM,CAACF,OAAO,GAAG,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjD,UAAA9E,MAAA,CAAU0E,GAAG,OAAA1E,MAAA,CAAI+E,GAAG;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjClD,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACA;MACA,MAAMmD,QAAQ,GAAG,MAAMC,KAAK,2BAAAlF,MAAA,CAA2B7B,MAAM,GAAI;QAC7DgH,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACL,eAAe,YAAApF,MAAA,CAAYuB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAE;UAC1D,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,MAAM6D,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAI,CAACL,QAAQ,CAACM,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACI,OAAO,IAAI,0BAA0B,CAAC;MAC/D;;MAEA;MACA,MAAM;QAAEvG,SAAS;QAAEwG;MAAU,CAAC,GAAGL,IAAI;MACrCpB,OAAO,CAAC0B,GAAG,CAAC,+CAA+C,EAAEzG,SAAS,CAAC;MAEvED,UAAU,CAAC,IAAI,CAAC;MAChBG,UAAU,CAACU,OAAO,GAAGZ,SAAS;MAC9BC,YAAY,CAACD,SAAS,CAAC;MAEvB,IAAIf,MAAM,EAAE;QACRC,QAAQ,CAAC9B,4BAA4B,CAAC6B,MAAM,CAAC,CAAC;MAClD;MACAuB,aAAa,CAACgG,SAAS,CAAC;MAExB,IAAI,EAACpH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsH,sBAAsB,GAAE;QAC/B9D,kBAAkB,CAAC,KAAK,CAAC;QACzB;MACJ;MAEA,IAAI;QACA,MAAM+D,OAAO,GAAG,MAAM1I,iBAAiB,CAAC,CAAC;QACzC,IAAI0I,OAAO,EAAE;UACTC,UAAU,CAAC,MAAM;YACbhE,kBAAkB,CAAC,KAAK,CAAC;UAC7B,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,MAAM;UACHmC,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;UACpEpC,kBAAkB,CAAC,KAAK,CAAC;QAC7B;MACJ,CAAC,CAAC,OAAOkC,GAAG,EAAE;QACVC,OAAO,CAAC8B,KAAK,CAAC,2BAA2B,EAAE/B,GAAG,CAAC;QAC/CgC,KAAK,CAAC,yDAAyD,CAAC;QAChElE,kBAAkB,CAAC,KAAK,CAAC;MAC7B;IAEJ,CAAC,CAAC,OAAOiE,KAAK,EAAE;MACZ9B,OAAO,CAAC8B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD3H,QAAQ,CAAC3B,eAAe,CAAC,OAAO,GAAGsJ,KAAK,CAACN,OAAO,CAAC,CAAC;MAClD3D,kBAAkB,CAAC,KAAK,CAAC;MACzBzD,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC;;EAED;;EAEAhC,SAAS,CAAC,MAAM;IACZ,IAAImC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2H,YAAY,IAAIxG,UAAU,EAAE;MAClC,MAAMyG,KAAK,GAAG,IAAIC,IAAI,CAAC1G,UAAU,CAAC;MAClC,MAAM2G,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,cAAc,GAAGzB,IAAI,CAACC,KAAK,CAAC,CAACuB,GAAG,GAAGF,KAAK,IAAI,IAAI,CAAC;MACvD,MAAMI,YAAY,GAAGhI,IAAI,CAAC2H,YAAY,GAAG,EAAE;MAC3C,MAAMM,SAAS,GAAG3B,IAAI,CAAC4B,GAAG,CAACF,YAAY,GAAGD,cAAc,EAAE,CAAC,CAAC;MAC5DjI,QAAQ,CAACd,gBAAgB,CAACiJ,SAAS,CAAC,CAAC;;MAErC;MACA,IAAIrH,SAAS,EAAE;QACXd,QAAQ,CAACV,gBAAgB,CAAC;UAAES,MAAM;UAAEe;QAAU,CAAC,CAAC,CAAC,CAC5CuH,IAAI,CAAEC,MAAM,IAAK;UAAA,IAAAC,eAAA;UACd,IAAI,EAAAA,eAAA,GAAAD,MAAM,CAACE,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBxG,aAAa,MAAK0G,SAAS,EAAE;YAC7CzI,QAAQ,CAACd,gBAAgB,CAACoJ,MAAM,CAACE,OAAO,CAACzG,aAAa,CAAC,CAAC;UAC5D;QACJ,CAAC,CAAC,CACD2G,KAAK,CAAEf,KAAK,IAAK;UACd9B,OAAO,CAAC8B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC5D,CAAC,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAACtG,UAAU,EAAEnB,IAAI,EAAEY,SAAS,EAAEf,MAAM,EAAEC,QAAQ,CAAC,CAAC;EAEnDjC,SAAS,CAAC,MAAM;IACZ,IAAIoD,IAAI,EAAE;IACV,IAAI,CAACY,aAAa,EAAEX,OAAO,CAAC,IAAI,CAAC;EACrC,CAAC,EAAE,CAACW,aAAa,CAAC,CAAC;EAEnB,MAAM4G,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAIpH,eAAe,CAACG,OAAO,EAAE;MACzBmE,OAAO,CAACC,IAAI,CAAC,sCAAsC,CAAC;MACpD;IACJ;IACAvE,eAAe,CAACG,OAAO,GAAG,IAAI,CAAC,CAAC;IAChCmE,OAAO,CAAC0B,GAAG,CAAC,qBAAqB,EAAEzG,SAAS,CAAC;IAC7C,IAAI,CAACA,SAAS,EAAE;MACZ+E,OAAO,CAAC0B,GAAG,CAAC,8EAA8E,CAAC;MAC3F;IACJ;IAEA1B,OAAO,CAAC0B,GAAG,CAAC,6BAA6B,EAAEzG,SAAS,CAAC;IACrDd,QAAQ,CAACZ,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC9BoE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACA;MACA,MAAM8E,MAAM,GAAG,MAAMtI,QAAQ,CAACb,UAAU,CAAC2B,SAAS,CAAC,CAAC,CAAC8H,MAAM,CAAC,CAAC;MAC7D/C,OAAO,CAAC0B,GAAG,CAAC,qBAAqB,EAAEe,MAAM,CAAC;;MAE1C;MACAtI,QAAQ,CAAC1B,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;;MAElD;MACA,IAAI;QACAU,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,OAAO4G,GAAG,EAAE;QACV;QACAC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAChE;MAEA,MAAMiD,aAAa,GAAG7H,UAAU,CAACU,OAAO;MACxC,MAAMoH,WAAW,GAAGtH,OAAO,CAACE,OAAO;MAEnC,IAAI,CAACmH,aAAa,EAAE;QAChBhD,OAAO,CAAC8B,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACJ;;MAEA;MACA9B,OAAO,CAAC0B,GAAG,CAAC,qBAAqB,EAAEuB,WAAW,CAAC;MAC/CjD,OAAO,CAAC0B,GAAG,CAAC,aAAa,EAAEsB,aAAa,CAAC;MAEzC,IAAI,CAACC,WAAW,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QAC/ClD,OAAO,CAAC0B,GAAG,CAAC,+BAA+B,EAAE;UACzCyB,QAAQ,EAAE,CAACF,WAAW;UACtBG,aAAa,EAAEH,WAAW,IAAI,CAACA,WAAW,CAACC;QAC/C,CAAC,CAAC;QACF9I,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACpC;MACJ;MAEAE,QAAQ,2BAAA2B,MAAA,CAA2BiH,aAAa,WAAQ,CAAC;IAC7D,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACZ9B,OAAO,CAAC8B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCnE,gBAAgB,CAAC,KAAK,CAAC;MACvBxD,QAAQ,CAAC3B,eAAe,CAAC,oCAAoC,CAAC,CAAC;MAC/DkD,eAAe,CAACG,OAAO,GAAG,KAAK,CAAC,CAAC;;MAEjC;MACAgG,UAAU,CAAC,MAAM;QACb,IAAI,CAACnE,aAAa,IAAIvC,UAAU,CAACU,OAAO,EAAE;UACtCmE,OAAO,CAAC0B,GAAG,CAAC,4BAA4B,CAAC;UACzCoB,gBAAgB,CAAC,CAAC;QACtB;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAGjL,WAAW,CAAE6G,UAAU,IAAK;IACnD/B,mBAAmB,CAAC+B,UAAU,CAAC;;IAE/B;IACA,IAAIrD,cAAc,CAACC,OAAO,IAAID,cAAc,CAACC,OAAO,CAACyH,oBAAoB,CAAC,CAAC,EAAE;MACzE;MACA1H,cAAc,CAACC,OAAO,CAAC0H,gBAAgB,CAACtE,UAAU,CAAC;IACvD,CAAC,MAAM;MACH;MACA;MACA4C,UAAU,CAAC,MAAM;QACb;QACA,MAAM2B,OAAO,GAAG5E,QAAQ,CAAC6E,aAAa,wBAAA1H,MAAA,CAAuBkD,UAAU,QAAI,CAAC;QAE5E,IAAIuE,OAAO,EAAE;UACT,MAAME,MAAM,GAAG,EAAE,CAAC,CAAC;UACnB,MAAMC,CAAC,GAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;UACvEI,MAAM,CAACE,QAAQ,CAAC;YAAEH,GAAG,EAAEF,CAAC;YAAEM,QAAQ,EAAE;UAAS,CAAC,CAAC;QACnD,CAAC,MAAM;UACH;UACA,MAAMC,UAAU,GAAGpJ,YAAY,CAACe,OAAO,CAACoD,UAAU,CAAC;UAEnD,IAAIiF,UAAU,EAAE;YACZ,MAAMR,MAAM,GAAG,EAAE,CAAC,CAAC;YACnB,MAAMC,CAAC,GAAGO,UAAU,CAACN,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;YAC1EI,MAAM,CAACE,QAAQ,CAAC;cAAEH,GAAG,EAAEF,CAAC;cAAEM,QAAQ,EAAE;YAAS,CAAC,CAAC;UACnD;QACJ;MACJ,CAAC,EAAE,CAAC,CAAC;IACT;EACJ,CAAC,EAAE,CAACnJ,YAAY,EAAEc,cAAc,CAAC,CAAC;;EAElC;EACA,MAAMuI,gBAAgB,GAAGd,kBAAkB;EAE3C,MAAMe,oBAAoB,GAAGA,CAACnF,UAAU,EAAEoF,WAAW,EAAEC,IAAI,KAAK;IAC5D;IACA,IAAIxG,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAM+L,SAAS,GAAG;MACdtF,UAAU;MACVuF,aAAa,EAAEH,WAAW;MAC1BI,cAAc,EAAEH;IACpB,CAAC;IACDnK,QAAQ,CAACvB,UAAU,CAAC2L,SAAS,CAAC,CAAC;;IAE/B;IACApK,QAAQ,CAACR,uBAAuB,CAAC;MAC7BsF,UAAU;MACVuF,aAAa,EAAEH,WAAW;MAC1BC,IAAI;MACJrJ;IACJ,CAAC,CAAC,CAAC,CAACuH,IAAI,CAAEC,MAAM,IAAK;MACjB,IAAIA,MAAM,CAAC6B,IAAI,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC;QACA1E,OAAO,CAAC0B,GAAG,CAAC,+BAA+B,CAAC;MAChD,CAAC,MAAM;QACH;QACA1B,OAAO,CAAC8B,KAAK,CAAC,0BAA0B,EAAEW,MAAM,CAACX,KAAK,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN,CAAC;EAED,MAAM6C,oBAAoB,GAAGA,CAAC1F,UAAU,EAAEoF,WAAW,EAAEO,cAAc,KAAK;IACtE;IACA,IAAI9G,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAMqM,cAAc,GAAGnG,SAAS,CAACO,UAAU,CAAC,IAAI,EAAE;IAElD,MAAM6F,QAAQ,GAAGD,cAAc,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACX,WAAW,KAAKA,WAAW,CAAC;;IAE5E;IACA,IAAIS,QAAQ,IAAIA,QAAQ,CAACG,MAAM,KAAKL,cAAc,EAAE;MAChD;IACJ;IAEA,MAAMM,cAAc,GAAGL,cAAc,CAACM,GAAG,CAACH,GAAG,IACzCA,GAAG,CAACX,WAAW,KAAKA,WAAW,GACzB;MAAE,GAAGW,GAAG;MAAEC,MAAM,EAAEL;IAAe,CAAC,GAClCI,GACV,CAAC;;IAED;IACA,IAAI,CAACF,QAAQ,EAAE;MACXI,cAAc,CAACE,IAAI,CAAC;QAAEf,WAAW;QAAEY,MAAM,EAAEL;MAAe,CAAC,CAAC;IAChE;IAEAzK,QAAQ,CAACvB,UAAU,CAAC;MAAEqG,UAAU;MAAEuF,aAAa,EAAEhH,IAAI,CAAC6H,SAAS,CAACH,cAAc,CAAC;MAAET,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC;;IAEzG;IACAtK,QAAQ,CAACR,uBAAuB,CAAC;MAC7BsF,UAAU;MACVuF,aAAa,EAAEU,cAAc;MAC7BZ,IAAI,EAAE,IAAI;MACVrJ;IACJ,CAAC,CAAC,CAAC,CAACuH,IAAI,CAAEC,MAAM,IAAK;MACjB,IAAIA,MAAM,CAAC6B,IAAI,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC1E,OAAO,CAAC0B,GAAG,CAAC,kCAAkC,CAAC;MACnD,CAAC,MAAM;QACH1B,OAAO,CAAC8B,KAAK,CAAC,6BAA6B,EAAEW,MAAM,CAACX,KAAK,CAAC;MAC9D;IACJ,CAAC,CAAC;EACN,CAAC;EAGD,MAAMwD,qBAAqB,GAAGA,CAACrG,UAAU,EAAEuF,aAAa,EAAEF,IAAI,KAAK;IAC/D;IACA,IAAIxG,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,IAAI,CAACgM,aAAa,IAAIA,aAAa,CAACe,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/C;IACJ;IAEA,MAAMC,eAAe,GAAGhB,aAAa,CAACe,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC9DtL,QAAQ,CAACvB,UAAU,CAAC;MAAEqG,UAAU;MAAEuF,aAAa;MAAEC,cAAc,EAAEH;IAAK,CAAC,CAAC,CAAC;;IAEzE;IACAnK,QAAQ,CAACR,uBAAuB,CAAC;MAC7BsF,UAAU;MACVuF,aAAa,EAAEgB,eAAe;MAC9BlB,IAAI;MACJrJ;IACJ,CAAC,CAAC,CAAC,CAACuH,IAAI,CAAEC,MAAM,IAAK;MACjB,IAAIA,MAAM,CAAC6B,IAAI,CAACI,QAAQ,CAAC,YAAY,CAAC,EAAE;QACpC1E,OAAO,CAAC0B,GAAG,CAAC,mCAAmC,CAAC;MACpD,CAAC,MAAM;QACH1B,OAAO,CAAC8B,KAAK,CAAC,8BAA8B,EAAEW,MAAM,CAACX,KAAK,CAAC;MAC/D;IACJ,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAM4D,sBAAsB,GAAGvN,MAAM,CAAC,IAAIqE,GAAG,CAAC,CAAC,CAAC;;EAEhD;EACAtE,SAAS,CAAC,MAAM;IACZ,IAAIwN,sBAAsB,CAAC7J,OAAO,CAAC8J,IAAI,GAAG,CAAC,EAAE;MACzCD,sBAAsB,CAAC7J,OAAO,CAAC+J,OAAO,CAAC3G,UAAU,IAAI;QACjD,IAAI,CAAC9C,aAAa,CAAC+C,QAAQ,CAACD,UAAU,CAAC,EAAE;UACrCD,WAAW,CAACC,UAAU,CAAC;QAC3B;MACJ,CAAC,CAAC;MACFyG,sBAAsB,CAAC7J,OAAO,CAACgK,KAAK,CAAC,CAAC;IAC1C;EACJ,CAAC,EAAE,CAAC1J,aAAa,EAAE6C,WAAW,CAAC,CAAC;;EAEhC;EACA9G,SAAS,CAAC,MAAM;IACZ;IACA,MAAM4N,OAAO,GAAGC,qBAAqB,CAAC,MAAM;MACxC,IAAIL,sBAAsB,CAAC7J,OAAO,CAAC8J,IAAI,GAAG,CAAC,EAAE;QACzC,MAAMK,WAAW,GAAG,CAAC,GAAGN,sBAAsB,CAAC7J,OAAO,CAAC;QACvD6J,sBAAsB,CAAC7J,OAAO,CAACgK,KAAK,CAAC,CAAC;;QAEtC;QACAG,WAAW,CAACJ,OAAO,CAAC3G,UAAU,IAAI;UAC9B,IAAI,CAAC9C,aAAa,CAACwD,GAAG,CAACV,UAAU,CAAC,EAAE;YAChCD,WAAW,CAACC,UAAU,CAAC;UAC3B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IAEF,OAAO,MAAMgH,oBAAoB,CAACH,OAAO,CAAC;EAC9C,CAAC,CAAC;EAEF,MAAMI,YAAY,GAAG9N,WAAW,CAAC,CAAC6G,UAAU,EAAEoF,WAAW,KAAK;IAC1D,MAAM8B,UAAU,GAAG7H,QAAQ,CAAC8H,IAAI,CAC3BpB,GAAG,IACAA,GAAG,CAAC/F,UAAU,KAAKA,UAAU,IAC7B+F,GAAG,CAACR,aAAa,IACjB9D,MAAM,CAACsE,GAAG,CAACR,aAAa,CAAC,KAAK9D,MAAM,CAAC2D,WAAW,CACxD,CAAC;;IAED;IACA,IAAI8B,UAAU,IAAI,CAAChK,aAAa,CAACwD,GAAG,CAACV,UAAU,CAAC,EAAE;MAC9CyG,sBAAsB,CAAC7J,OAAO,CAACgE,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAOkH,UAAU;EACrB,CAAC,EAAE,CAAC7H,QAAQ,EAAEnC,aAAa,CAAC,CAAC;EAE7B,MAAMkK,WAAW,GAAGjO,WAAW,CAAC,CAAC6G,UAAU,EAAEoF,WAAW,EAAEiC,IAAI,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC/D,MAAML,UAAU,GAAG,EAAAI,qBAAA,GAAA7H,SAAS,CAACO,UAAU,CAAC,cAAAsH,qBAAA,uBAArBA,qBAAA,CAAuBH,IAAI,CACzCK,CAAC,IAAKA,CAAC,CAACpC,WAAW,KAAKA,WAAW,IAAIoC,CAAC,CAACxB,MAAM,KAAKqB,IACzD,CAAC,KAAI,KAAK;;IAEV;IACA,IAAIH,UAAU,IAAI,CAAChK,aAAa,CAACwD,GAAG,CAACV,UAAU,CAAC,IAAI,EAAAuH,sBAAA,GAAA9H,SAAS,CAACO,UAAU,CAAC,cAAAuH,sBAAA,uBAArBA,sBAAA,CAAuBE,MAAM,MAAK,CAAC,EAAE;MACrFhB,sBAAsB,CAAC7J,OAAO,CAACgE,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAOkH,UAAU;EACrB,CAAC,EAAE,CAACzH,SAAS,EAAEvC,aAAa,CAAC,CAAC;EAE9B,MAAMwK,kBAAkB,GAAGvO,WAAW,CAAE6G,UAAU,IAAK;IAAA,IAAA2H,qBAAA;IACnD,MAAMC,OAAO,GAAGrI,SAAS,CAACuG,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAAC/F,UAAU,KAAKA,UAAU,CAAC;IACtE,MAAM6H,OAAO,GAAG,CAAAD,OAAO,aAAPA,OAAO,wBAAAD,qBAAA,GAAPC,OAAO,CAAErC,aAAa,cAAAoC,qBAAA,uBAAtBA,qBAAA,CAAwBnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAI,EAAE;;IAEnE;IACA,IAAIqB,OAAO,IAAI,CAAC3K,aAAa,CAACwD,GAAG,CAACV,UAAU,CAAC,EAAE;MAC3CyG,sBAAsB,CAAC7J,OAAO,CAACgE,GAAG,CAACZ,UAAU,CAAC;IAClD;IAEA,OAAO6H,OAAO;EAClB,CAAC,EAAE,CAACtI,SAAS,EAAErC,aAAa,CAAC,CAAC;;EAE9B;EACA;EACA;EACA;EACA;;EAEAjE,SAAS,CAAC,MAAM;IACZ,IAAIsC,SAAS,EAAE;MACXyD,aAAa,CAACzD,SAAS,CAAC8E,MAAM,CAAEyH,QAAQ,IAAKA,QAAQ,CAACtC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/EtG,aAAa,CAAC3D,SAAS,CAAC8E,MAAM,CAAEyH,QAAQ,IAAKA,QAAQ,CAACtC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/EpG,cAAc,CAAC7D,SAAS,CAAC8E,MAAM,CAAEyH,QAAQ,IAAKA,QAAQ,CAACtC,cAAc,KAAK,KAAK,CAAC,CAAC;IACrF;EACJ,CAAC,EAAE,CAACjK,SAAS,CAAC,CAAC;EAEftC,SAAS,CAAC,MAAM;IACZ;IACA,IAAI,CAAC8O,KAAK,CAACC,OAAO,CAACxM,OAAO,CAAC,IAAIA,OAAO,CAACiM,MAAM,KAAK,CAAC,EAAE;IAErD,MAAMQ,EAAE,GAAG,EAAE;IACb,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,KAAK,MAAMnC,MAAM,IAAIxK,OAAO,EAAE;MAC1B,IAAIwK,MAAM,CAACR,cAAc,KAAK,IAAI,EAAE;QAChCyC,EAAE,CAAC9B,IAAI,CAACH,MAAM,CAAC;MACnB,CAAC,MAAM,IAAIA,MAAM,CAACR,cAAc,KAAK,KAAK,EAAE;QACxC0C,GAAG,CAAC/B,IAAI,CAACH,MAAM,CAAC;MACpB,CAAC,MAAM,IAAIA,MAAM,CAACR,cAAc,KAAK,IAAI,IAAIQ,MAAM,CAACT,aAAa,EAAE;QAC/D,IAAI;UACA,MAAM6C,MAAM,GAAG7J,IAAI,CAACC,KAAK,CAACwH,MAAM,CAACT,aAAa,CAAC;UAC/C4C,KAAK,CAACnC,MAAM,CAAChG,UAAU,CAAC,GAAGoI,MAAM;QACrC,CAAC,CAAC,OAAOtH,GAAG,EAAE;UACVC,OAAO,CAAC8B,KAAK,CAAC,6BAA6B,EAAE/B,GAAG,CAAC;QACrD;MACJ;IACJ;IAEAxB,WAAW,CAAC2I,EAAE,CAAC;IACfzI,YAAY,CAAC0I,GAAG,CAAC;IACjBxI,YAAY,CAACyI,KAAK,CAAC;;IAEnB;IACA;EACJ,CAAC,EAAE,CAAC3M,OAAO,CAAC,CAAC;EAGbvC,SAAS,CAAC,MAAM;IACZ,IAAI+C,SAAS,EAAE;MACXd,QAAQ,CAACxB,qBAAqB,CAACsC,SAAS,CAAC,CAAC;IAC9C;EACJ,CAAC,EAAE,CAACd,QAAQ,EAAEc,SAAS,CAAC,CAAC;EAEzB/C,SAAS,CAAC,MAAM;IACZ,IAAI,EAACmC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE2H,YAAY,KAAI9F,aAAa,KAAK,IAAI,IAAI,CAACnB,OAAO,EAAE;;IAE/D;IACA,MAAMuM,iBAAiB,GAAIC,IAAI,IAAK;MAChC;MACA,IAAIA,IAAI,KAAK,GAAG,IAAI,CAAC9K,gBAAgB,CAACE,WAAW,EAAE;QAC/CD,mBAAmB,CAAC+C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE9C,WAAW,EAAE;QAAK,CAAC,CAAC,CAAC;QAC7DG,iBAAiB,CAAC,IAAI,CAAC;QACvB3C,QAAQ,CAAC3B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;QACAqJ,UAAU,CAAC,MAAM;UACb/E,iBAAiB,CAAC,KAAK,CAAC;QAC5B,CAAC,EAAE,KAAK,CAAC;MACb;;MAEA;MACA,IAAIyK,IAAI,KAAK,EAAE,IAAI,CAAC9K,gBAAgB,CAACG,SAAS,EAAE;QAC5CF,mBAAmB,CAAC+C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE7C,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC;QAC3DE,iBAAiB,CAAC,IAAI,CAAC;QACvB3C,QAAQ,CAAC3B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;MACJ;IACJ,CAAC;;IAED;IACA,MAAMgP,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACvC,IAAIxM,SAAS,EAAE;QACXd,QAAQ,CAACV,gBAAgB,CAAC;UAAES,MAAM;UAAEe;QAAU,CAAC,CAAC,CAAC,CAC5CuH,IAAI,CAAEC,MAAM,IAAK;UAAA,IAAAiF,gBAAA;UACd,IAAI,EAAAA,gBAAA,GAAAjF,MAAM,CAACE,OAAO,cAAA+E,gBAAA,uBAAdA,gBAAA,CAAgBxL,aAAa,MAAK0G,SAAS,EAAE;YAC7CzI,QAAQ,CAACd,gBAAgB,CAACoJ,MAAM,CAACE,OAAO,CAACzG,aAAa,CAAC,CAAC;UAC5D;QACJ,CAAC,CAAC,CACD2G,KAAK,CAAEf,KAAK,IAAK;UACd9B,OAAO,CAAC8B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACtD,CAAC,CAAC;MACV;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,MAAM6F,QAAQ,GAAGF,WAAW,CAAC,MAAM;MAC/BtN,QAAQ,CAACd,gBAAgB,CAAEoG,IAAI,IAAK;QAChC,IAAIA,IAAI,IAAI,CAAC,EAAE;UAAE;UACbmI,aAAa,CAACD,QAAQ,CAAC;UACvBC,aAAa,CAACJ,gBAAgB,CAAC;UAC/B;UACAzJ,WAAW,CAAC,IAAI,CAAC;UACjBjB,iBAAiB,CAAC,KAAK,CAAC;UACxB;UACAgG,gBAAgB,CAAC,CAAC;UAClB,OAAO,CAAC;QACZ;;QAEA;QACAwE,iBAAiB,CAAC7H,IAAI,CAAC;QAEvB,OAAOA,IAAI,GAAG,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACTmI,aAAa,CAACD,QAAQ,CAAC;MACvBC,aAAa,CAACJ,gBAAgB,CAAC;IACnC,CAAC;EACL,CAAC,EAAE,CAACnN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2H,YAAY,EAAEjH,OAAO,EAAEmB,aAAa,EAAEO,gBAAgB,EAAEtC,QAAQ,EAAEc,SAAS,EAAEf,MAAM,CAAC,CAAC,CAAC;;EAEhG;;EAEA;EACAhC,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC+C,SAAS,IAAI,EAACe,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuD,EAAE,KAAI,CAACrF,MAAM,IAAIe,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK2H,SAAS,EAAE;IACzF,IAAI,EAACvI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsH,sBAAsB,GAAE;IACnC3B,OAAO,CAAC0B,GAAG,CAAC,kCAAkC,CAAC;IAG/C,MAAMmG,UAAU,GAAG,IAAIrL,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAMsL,OAAO,GAAGA,CAACC,GAAG,EAAEpF,OAAO,KAAK;MAC9B,IAAI,EAACtI,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEsH,sBAAsB,KAAIkG,UAAU,CAAClI,GAAG,CAACoI,GAAG,CAAC,EAAE;MAE1DF,UAAU,CAAChI,GAAG,CAACkI,GAAG,CAAC;;MAEnB;MACA5N,QAAQ,CAACT,eAAe,CAAC;QACrBQ,MAAM;QACNe,SAAS;QACT+M,YAAY,EAAErF,OAAO,CAAC2B,IAAI,IAAI,eAAe;QAC7C2D,OAAO,EAAE;UACL,GAAGtF,OAAO;UACVuF,IAAI,EAAElM,IAAI,CAACmM,QAAQ,GAAG,GAAG,GAAGnM,IAAI,CAACoM;QACrC;MACJ,CAAC,CAAC,CAAC;MAEHvG,UAAU,CAAC,MAAMgG,UAAU,CAACjI,MAAM,CAACmI,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC;;IAED;IACA,MAAMM,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAACzJ,QAAQ,CAAC0J,iBAAiB,IAC3B,CAAC1J,QAAQ,CAAC2J,uBAAuB,IACjC,CAAC3J,QAAQ,CAAC4J,oBAAoB,IAC9B,CAAC5J,QAAQ,CAAC6J,mBAAmB,EAAE;QAC/BX,OAAO,CAAC,iBAAiB,EAAE;UACvBY,SAAS,EAAE1M,IAAI,CAACuD,EAAE;UAClBtE,SAAS;UACTf,MAAM;UACNyO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,iBAAiB;UACzBC,MAAM,EAAErL,IAAI,CAAC6H,SAAS,CAAC;YAAEyD,MAAM,EAAE;UAA8B,CAAC;QACpE,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAInK,QAAQ,CAACoK,eAAe,KAAK,QAAQ,EAAE;QACvClB,OAAO,CAAC,UAAU,EAAE;UAChBY,SAAS,EAAE1M,IAAI,CAACuD,EAAE;UAClBtE,SAAS;UACTf,MAAM;UACNyO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,UAAU;UAClBC,MAAM,EAAErL,IAAI,CAAC6H,SAAS,CAAC;YAAE7D,OAAO,EAAE;UAAwC,CAAC;QAC/E,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMyH,UAAU,GAAGA,CAAA,KAAM;MACrBnB,OAAO,CAAC,eAAe,EAAE;QACrBY,SAAS,EAAE1M,IAAI,CAACuD,EAAE;QAClBtE,SAAS;QACTf,MAAM;QACNyO,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAErL,IAAI,CAAC6H,SAAS,CAAC;UAAE7D,OAAO,EAAE;QAAsB,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC;;IAED;IACA,MAAM0H,mBAAmB,GAAIpK,CAAC,IAAK;MAC/B,MAAMqK,cAAc,GAAG,CACnB,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CACjE;MACD,MAAMC,KAAK,MAAArN,MAAA,CAAM+C,CAAC,CAACuK,OAAO,GAAG,OAAO,GAAG,EAAE,EAAAtN,MAAA,CAAG+C,CAAC,CAACwK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAAvN,MAAA,CAAG+C,CAAC,CAACyK,MAAM,GAAG,MAAM,GAAG,EAAE,EAAAxN,MAAA,CAAG+C,CAAC,CAAC0K,OAAO,GAAG,OAAO,GAAG,EAAE,EAAAzN,MAAA,CAAG+C,CAAC,CAACiJ,GAAG,CAAE;MAEpI,IACIoB,cAAc,CAACjK,QAAQ,CAACJ,CAAC,CAACiJ,GAAG,CAAC,IAC9BqB,KAAK,KAAK,cAAc,IACxBA,KAAK,KAAK,cAAc,EAC1B;QACEtB,OAAO,QAAA/L,MAAA,CAAQqN,KAAK,GAAI;UACpBV,SAAS,EAAE1M,IAAI,CAACuD,EAAE;UAClBtE,SAAS;UACTf,MAAM;UACNyO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,gBAAgB;UACxBC,MAAM,EAAErL,IAAI,CAAC6H,SAAS,CAAC;YAAE0C,GAAG,EAAEjJ,CAAC,CAACiJ,GAAG;YAAEY,IAAI,EAAE7J,CAAC,CAAC6J,IAAI;YAAES;UAAM,CAAC;QAC9D,CAAC,CAAC;MACN;IACJ,CAAC;IAEDxK,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEwJ,sBAAsB,CAAC;IACrEzJ,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEkK,sBAAsB,CAAC;IACrEnK,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEoK,UAAU,CAAC;IAC7CrK,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEqK,mBAAmB,CAAC;IAEzD,OAAO,MAAM;MACTtK,QAAQ,CAAC6K,mBAAmB,CAAC,kBAAkB,EAAEpB,sBAAsB,CAAC;MACxEzJ,QAAQ,CAAC6K,mBAAmB,CAAC,kBAAkB,EAAEV,sBAAsB,CAAC;MACxEnK,QAAQ,CAAC6K,mBAAmB,CAAC,MAAM,EAAER,UAAU,CAAC;MAChDrK,QAAQ,CAAC6K,mBAAmB,CAAC,SAAS,EAAEP,mBAAmB,CAAC;IAChE,CAAC;EACL,CAAC,EAAE,CAAClN,IAAI,CAACuD,EAAE,EAAErF,MAAM,EAAEe,SAAS,CAAC,CAAC;EAGhC/C,SAAS,CAAC,MAAM;IACZ;IACA;IACA;IACA;EAAA,CACH,EAAE,CAACmC,IAAI,EAAEH,MAAM,EAAEE,QAAQ,EAAED,QAAQ,EAAE6E,WAAW,EAAEK,cAAc,EAAEF,mBAAmB,EAAEC,gBAAgB,CAAC,CAAC;EAE1GlH,SAAS,CAAC,MAAM;IACZoF,YAAY,CAACoM,OAAO,CAAC,YAAY,EAAElM,IAAI,CAAC6H,SAAS,CAAClI,UAAU,CAAC,CAAC;EAClE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMwM,aAAa,GAAGvR,WAAW,CAAE0G,CAAC,IAAK;IACrC;IACA,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAACI,QAAQ,CAACJ,CAAC,CAACiJ,GAAG,CAAC,EAAE;MACrE;MACAjJ,CAAC,CAACC,cAAc,CAAC,CAAC;;MAElB;MACA,IAAI,CAACvE,SAAS,IAAIA,SAAS,CAACkM,MAAM,KAAK,CAAC,EAAE;MAE1C,MAAMkD,YAAY,GAAG,CAAC,GAAG5L,UAAU,EAAE,GAAGE,UAAU,EAAE,GAAGE,WAAW,CAAC;MACnE,MAAMyL,YAAY,GAAGD,YAAY,CAACE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxK,EAAE,KAAKtC,gBAAgB,CAAC;MAE3E,IAAI,CAAC6B,CAAC,CAACiJ,GAAG,KAAK,SAAS,IAAIjJ,CAAC,CAACiJ,GAAG,KAAK,WAAW,KAAK8B,YAAY,GAAG,CAAC,EAAE;QACpE,MAAMG,cAAc,GAAGJ,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACtK,EAAE;QACxDS,OAAO,CAAC0B,GAAG,CAAC,+CAA+C,EAAEsI,cAAc,CAAC;QAC5E3G,kBAAkB,CAAC2G,cAAc,CAAC;MACtC,CAAC,MAAM,IAAI,CAAClL,CAAC,CAACiJ,GAAG,KAAK,WAAW,IAAIjJ,CAAC,CAACiJ,GAAG,KAAK,YAAY,KAAK8B,YAAY,GAAGD,YAAY,CAAClD,MAAM,GAAG,CAAC,EAAE;QACpG,MAAMuD,cAAc,GAAGL,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACtK,EAAE;QACxDS,OAAO,CAAC0B,GAAG,CAAC,kDAAkD,EAAEuI,cAAc,CAAC;QAC/E5G,kBAAkB,CAAC4G,cAAc,CAAC;MACtC;IACJ;EACJ,CAAC,EAAE,CAACzP,SAAS,EAAEwD,UAAU,EAAEE,UAAU,EAAEE,WAAW,EAAEnB,gBAAgB,EAAEoG,kBAAkB,CAAC,CAAC;EAC1F;EACAnL,SAAS,CAAC,MAAM;IACZ0G,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE8K,aAAa,CAAC;IACnD,OAAO,MAAM;MACT/K,QAAQ,CAAC6K,mBAAmB,CAAC,SAAS,EAAEE,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACI7P,OAAA;IAAKoQ,SAAS,0BAAAnO,MAAA,CAA0BoB,UAAU,GAAG,yBAAyB,GAAG,uBAAuB,CAAG;IAAAgN,QAAA,gBACvGrQ,OAAA,CAAChC,gBAAgB;MAACsS,QAAQ,EAAE/P,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6N,IAAK;MAACmC,gBAAgB,EAAEvK,oBAAqB;MAAC3C,UAAU,EAAE,CAACA;IAAW;MAAAmN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1G1P,OAAO,gBACJjB,OAAA;MAAKoQ,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAEhFrQ,OAAA,CAACb,WAAW;QACRyR,GAAG,EAAE9O,cAAe;QACpB+O,QAAQ,EAAE/M,eAAgB;QAC1BT,UAAU,EAAEA,UAAW;QACvBa,UAAU,EAAEA,UAAW;QACvBE,UAAU,EAAEA,UAAW;QACvBE,WAAW,EAAEA,WAAY;QACzBwM,QAAQ,EAAE;UACNxG,oBAAoB;UACpBO,oBAAoB;UACpBW,qBAAqB;UACrBY,YAAY;UACZG,WAAW;UACXM,kBAAkB;UAClBkE,cAAc,EAAEA,CAACtL,EAAE,EAAEuL,EAAE,KAAMhQ,YAAY,CAACe,OAAO,CAAC0D,EAAE,CAAC,GAAGuL,EAAG;UAC3D5N,mBAAmB,EAAGqC,EAAE,IAAKrC,mBAAmB,CAACqC,EAAE;QACvD,CAAE;QACFwL,QAAQ,EAAE;UACN9N,gBAAgB;UAChBE,UAAU;UACVzC,QAAQ;UACRE,SAAS;UACTmC,iBAAiB;UACjBC,iBAAiB;UACjBc,QAAQ;UACRxB,eAAe;UACfkD;QACJ,CAAE;QACF1B,QAAQ,EAAEA;QACV;QAAA;QACAkN,iBAAiB,EAAEpI,SAAU;QAC7BE,gBAAgB,EAAEA,gBAAiB;QACnCpF,aAAa,EAAEA;MAAc;QAAA4M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAIF3Q,OAAA;QAAKoQ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDrQ,OAAA;UACIoQ,SAAS,gCAAAnO,MAAA,CAAgCoB,UAAU,GAAG,wBAAwB,GAAG,qBAAqB,CAAG;UACzG8N,OAAO,EAAEA,CAAA,KAAM5P,gBAAgB,CAACoE,IAAI,IAAI,CAACA,IAAI,CAAE;UAAA0K,QAAA,eAE/CrQ,OAAA,CAACf,IAAI;YAAAuR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGN3Q,OAAA,CAAChB,eAAe;QAAAqR,QAAA,EACX,CAAC/O,aAAa,IAAI0I,MAAM,CAACoH,UAAU,GAAG,IAAI,kBACvCpR,OAAA,CAACd,WAAW;UACRmE,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7B1C,QAAQ,EAAEA,QAAS;UACnBwF,oBAAoB,EAAEA,oBAAqB;UAC3CtF,SAAS,EAAEA,SAAU;UACrB0F,qBAAqB,EAAEA,qBAAsB;UAC7CtC,UAAU,EAAEA,UAAW;UACvBE,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzB+F,gBAAgB,EAAEd,kBAAmB;UACrCpG,gBAAgB,EAAEA,gBAAiB;UACnCX,eAAe,EAAEA,eAAgB;UACjCkD,kBAAkB,EAAEA,kBAAmB;UACvCsD,gBAAgB,EAAEA,gBAAiB;UACnCpF,aAAa,EAAEA,aAAc;UAC7BE,eAAe,EAAEA,eAAgB;UACjCvD,IAAI,EAAEA,IAAK;UACX6B,aAAa,EAAEA,aAAc;UAC7BqE,UAAU,EAAEA,UAAW;UACvB/F,SAAS,EAAEA,SAAU;UACrB2Q,kBAAkB,EAAE,EAAAlR,qBAAA,GAAA2B,cAAc,CAACC,OAAO,cAAA5B,qBAAA,uBAAtBA,qBAAA,CAAwBqJ,oBAAoB,CAAC,CAAC,KAAI,KAAM;UAC5E8H,qBAAqB,EAAG/K,KAAK,IAAK;YAC9B,IAAIzE,cAAc,CAACC,OAAO,EAAE;cACxB;cACAD,cAAc,CAACC,OAAO,CAACuP,qBAAqB,CAAC/K,KAAK,CAAC;YACvD;UACJ;QAAE;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CAAC,gBAEN3Q,OAAA;MAAKoQ,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC7CrQ,OAAA,CAACjB,mBAAmB;QAChBwS,OAAO,EAAEA,CAAA,KAAM;UACX;UACA,IAAIpQ,SAAS,EAAE;YACXd,QAAQ,CAACP,SAAS,CAAC;cAAEM,MAAM;cAAEe;YAAU,CAAC,CAAC,CAAC;UAC9C;UACAb,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACxC,CAAE;QACFoR,MAAM,EAAE,CAACvQ,OAAQ;QACjBwQ,WAAW,EAAExK;MAAiB;QAAAuJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA,CAAApQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2H,YAAY,KAAIjH,OAAO,iBAC1BjB,OAAA;MAAKoQ,SAAS,qEAAAnO,MAAA,CACRc,cAAc,GACV,0BAA0B,GAC1B,4BAA4B,yEACc;MAAAsN,QAAA,eAChDrQ,OAAA;QAAKoQ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCrQ,OAAA;UAAKoQ,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAAE5J,UAAU,CAACrE,aAAa,CAAC,EAAC,UAAK;QAAA;UAAAoO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEA,CAAC;AAEd,CAAC;AAACzQ,EAAA,CAn4BID,UAAU;EAAA,QACOxB,SAAS,EACXR,WAAW,EACXW,WAAW,EACXV,WAAW,EACNA,WAAW,EACbA,WAAW,EA4BdA,WAAW,EAC6BA,WAAW;AAAA;AAAAwT,EAAA,GAnClEzR,UAAU;AAq4BhB,eAAeA,UAAU;AAAC,IAAAyR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}