import db from "../models/index.js"
import { Op, literal, fn, col } from 'sequelize'
import UserType from "../constants/UserType.js"
import { sendUserNotification } from "../utils/notificationUtils.js"
import StudentClassStatus from "../constants/StudentClassStatus.js"
import TuitionStatus from "../constants/TuitionStatus.js"
import * as tuitionService from "../services/tuition.service.js"

/**
 * L<PERSON><PERSON> danh sách tất cả các khoản đóng học phí
 */
export const getAllTuitionPayments = async (req, res) => {
    try {
        const search = req.query.search || ''
        const page = parseInt(req.query.page, 10) || 1
        const limit = parseInt(req.query.limit, 10) || 10
        const offset = (page - 1) * limit
        const sortOrder = req.query.sortOrder || 'DESC'
        const sortBy = req.query.sortBy || 'createdAt'
        const userId = req.query.userId || null
        const status = req.query.status || null
        const month = req.query.month || null
        const userClass = req.query.userClass || null // Lớp học của người dùng (10, 11, 12)
        const overdue = req.query.overdue === 'true' // Lấy các khoản học phí đã quá hạn
        const classId = req.query.classId || null // ID của lớp học cụ thể


        // Xây dựng điều kiện tìm kiếm
        let whereClause = {}

        // Tìm kiếm theo học sinh nếu có
        if (userId) {
            whereClause.userId = userId
        }

        // Tìm kiếm theo trạng thái nếu có
        if (status) {
            whereClause.status = status
        }

        // Tìm kiếm theo tháng nếu có
        if (month) {
            whereClause.month = month
        }

        // Tìm kiếm theo lớp học của người dùng (10, 11, 12)
        if (userClass) {
            whereClause['$user.class$'] = userClass
        }

        // Tìm kiếm theo lớp học cụ thể (classId)
        if (classId) {
            // Lấy danh sách học sinh đã tham gia lớp học với trạng thái "JS" (Joined Successfully)
            const studentsInClass = await db.StudentClassStatus.findAll({
                where: {
                    classId,
                    status: StudentClassStatus.JOINED // Joined Successfully
                },
                attributes: ['studentId'],
                raw: true
            });

            if (studentsInClass.length > 0) {
                // Lấy danh sách ID của học sinh trong lớp
                const studentIds = studentsInClass.map(student => student.studentId);

                // Thêm điều kiện tìm kiếm theo danh sách học sinh
                whereClause.userId = {
                    [Op.in]: studentIds
                };
            } else {
                // Nếu không có học sinh nào trong lớp, trả về danh sách rỗng
                whereClause.userId = -1; // ID không tồn tại
            }
        }

        // Tìm kiếm các khoản học phí đã quá hạn
        if (overdue) {
            const today = new Date();
            whereClause = {
                ...whereClause,
                dueDate: {
                    [Op.lt]: today
                },
                status: {
                    [Op.ne]: 'PAID'
                }
            }
        }

        // Tìm kiếm theo từ khóa
        if (search.trim() !== '') {
            whereClause = {
                ...whereClause,
                [Op.or]: [
                    literal(`CONCAT(user.lastName, ' ', user.firstName) LIKE '%${search}%'`),
                    { note: { [Op.like]: `%${search}%` } }
                ]
            }
        }
        // console.log(whereClause)

        // Thực hiện truy vấn với include để lấy thông tin học sinh
        const [payments, total] = await Promise.all([
            db.TuitionPayment.findAll({
                where: whereClause,
                include: [
                    {
                        model: db.User,
                        as: 'user',
                        attributes: ['id', 'lastName', 'firstName', 'class', 'highSchool'],
                        required: !!userClass // Nếu có lọc theo lớp thì required = true
                    }
                ],
                offset,
                limit,
                order: [[sortBy, sortOrder]]
            }),
            db.TuitionPayment.count({
                where: whereClause,
                include: [
                    {
                        model: db.User,
                        as: 'user',
                        required: !!userClass // Nếu có lọc theo lớp thì required = true
                    }
                ]
            })
        ])

        // Format lại dữ liệu trước khi trả về
        const formattedPayments = payments.map(payment => {
            const plainPayment = payment.get({ plain: true })
            const [year, month] = plainPayment.month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]

            // Kiểm tra xem khoản học phí có quá hạn không
            const isOverdue = plainPayment.dueDate &&
                plainPayment.status !== 'PAID' &&
                new Date() > new Date(plainPayment.dueDate);

            return {
                ...plainPayment,
                monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
                paymentDateFormatted: plainPayment.paymentDate
                    ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                    : null,
                dueDateFormatted: plainPayment.dueDate
                    ? new Date(plainPayment.dueDate).toLocaleDateString('vi-VN')
                    : null,
                studentName: `${plainPayment.user.lastName} ${plainPayment.user.firstName}`,
                userClass: plainPayment.user.class,
                remainingAmount: plainPayment.expectedAmount - plainPayment.paidAmount,
                isOverdue: isOverdue
            }
        })

        return res.status(200).json({
            message: 'Danh sách đóng học phí',
            data: formattedPayments,
            pagination: {
                page,
                limit,
                totalRows: total,
                totalPages: Math.ceil(total / limit)
            }
        })
    } catch (error) {
        console.error('Lỗi khi lấy danh sách đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Lấy danh sách đóng học phí của một lớp cụ thể
 */
export const getTuitionPaymentsByClassId = async (req, res) => {
    try {
        const { classId } = req.params
        const page = parseInt(req.query.page, 10) || 1
        const limit = parseInt(req.query.limit, 10) || 10
        const offset = (page - 1) * limit
        const sortOrder = req.query.sortOrder || 'DESC'
        const sortBy = req.query.sortBy || 'createdAt'
        const status = req.query.status || null
        const month = req.query.month || null

        // Kiểm tra lớp học có tồn tại không
        const classExists = await db.Class.findByPk(classId)
        if (!classExists) {
            return res.status(404).json({ message: 'Lớp học không tồn tại' })
        }

        // Lấy danh sách học sinh đã tham gia lớp học với trạng thái "JS" (Joined Successfully)
        const studentsInClass = await db.StudentClassStatus.findAll({
            where: {
                classId,
                status: StudentClassStatus.JOINED // Joined Successfully
            },
            attributes: ['studentId'],
            raw: true
        })

        if (studentsInClass.length === 0) {
            return res.status(200).json({
                message: `Không có học sinh nào trong lớp ${classExists.name}`,
                data: [],
                class: {
                    id: classExists.id,
                    name: classExists.name,
                    class_code: classExists.class_code
                },
                pagination: {
                    page,
                    limit,
                    totalRows: 0,
                    totalPages: 0
                }
            })
        }

        // Lấy danh sách ID của học sinh trong lớp
        const studentIds = studentsInClass.map(student => student.studentId)

        // Xây dựng điều kiện tìm kiếm
        let whereClause = {
            userId: {
                [Op.in]: studentIds
            }
        }

        // Tìm kiếm theo trạng thái nếu có
        if (status) {
            whereClause.status = status
        }

        // Tìm kiếm theo tháng nếu có
        if (month) {
            whereClause.month = month
        }

        // Lấy danh sách đóng học phí của học sinh trong lớp
        const [payments, total] = await Promise.all([
            db.TuitionPayment.findAll({
                where: whereClause,
                include: [
                    {
                        model: db.User,
                        as: 'user',
                        attributes: ['id', 'lastName', 'firstName', 'username', 'avatarUrl', 'class']
                    }
                ],
                offset,
                limit,
                order: [[sortBy, sortOrder]]
            }),
            db.TuitionPayment.count({ where: whereClause })
        ])

        // Format lại dữ liệu trước khi trả về
        const formattedPayments = payments.map(payment => {
            const plainPayment = payment.get({ plain: true })
            const [year, month] = plainPayment.month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]

            // Kiểm tra xem khoản học phí có quá hạn không
            const isOverdue = plainPayment.dueDate &&
                plainPayment.status !== 'PAID' &&
                new Date() > new Date(plainPayment.dueDate);

            return {
                ...plainPayment,
                monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
                paymentDateFormatted: plainPayment.paymentDate
                    ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                    : null,
                dueDateFormatted: plainPayment.dueDate
                    ? new Date(plainPayment.dueDate).toLocaleDateString('vi-VN')
                    : null,
                studentName: `${plainPayment.user.lastName} ${plainPayment.user.firstName}`,
                userClass: plainPayment.user.class,
                remainingAmount: plainPayment.expectedAmount - plainPayment.paidAmount,
                isOverdue: isOverdue
            }
        })

        return res.status(200).json({
            message: `Danh sách đóng học phí của lớp ${classExists.name}`,
            data: formattedPayments,
            class: {
                id: classExists.id,
                name: classExists.name,
                class_code: classExists.class_code
            },
            pagination: {
                page,
                limit,
                totalRows: total,
                totalPages: Math.ceil(total / limit)
            }
        })
    } catch (error) {
        console.error('Lỗi khi lấy danh sách đóng học phí của lớp:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Lấy danh sách đóng học phí của một học sinh cụ thể
 */
export const getTuitionPaymentsByUserId = async (req, res) => {
    try {
        const { userId } = req.params
        const page = parseInt(req.query.page, 10) || 1
        const limit = parseInt(req.query.limit, 10) || 10
        const offset = (page - 1) * limit
        const sortOrder = req.query.sortOrder || 'DESC'
        const sortBy = req.query.sortBy || 'createdAt'
        const status = req.query.status || null
        const overdue = req.query.overdue === 'true' // Lấy các khoản học phí đã quá hạn
        const month = req.query.month || null // Lọc theo tháng

        // Kiểm tra học sinh có tồn tại không
        const userExists = await db.User.findByPk(userId)
        if (!userExists) {
            return res.status(404).json({ message: 'Học sinh không tồn tại' })
        }

        // Xây dựng điều kiện tìm kiếm
        let whereClause = { userId }

        // Tìm kiếm theo trạng thái nếu có
        if (status) {
            // Nếu status là UNPAID, tìm kiếm cả các khoản thanh toán một phần (PARTIAL)
            if (status === 'UNPAID') {
                whereClause.status = {
                    [Op.in]: ['UNPAID', 'PARTIAL']
                }
            } else {
                whereClause.status = status
            }
        }

        // Tìm kiếm theo tháng nếu có
        if (month) {
            whereClause.month = month
        }

        // Tìm kiếm các khoản học phí đã quá hạn
        if (overdue) {
            const today = new Date();
            whereClause = {
                ...whereClause,
                dueDate: {
                    [Op.lt]: today
                },
                status: {
                    [Op.ne]: 'PAID'
                }
            }
        }

        // Lấy danh sách đóng học phí của học sinh
        const [payments, total] = await Promise.all([
            db.TuitionPayment.findAll({
                where: whereClause,
                include: [],
                offset,
                limit,
                order: [[sortBy, sortOrder]]
            }),
            db.TuitionPayment.count({ where: whereClause })
        ])

        // Format lại dữ liệu trước khi trả về
        const formattedPayments = payments.map(payment => {
            const plainPayment = payment.get({ plain: true })
            const [year, month] = plainPayment.month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]
            return {
                ...plainPayment,
                isOverdue: plainPayment.dueDate &&
                    plainPayment.status !== 'PAID' &&
                    new Date() > new Date(plainPayment.dueDate),
                monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
                paymentDateFormatted: plainPayment.paymentDate
                    ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                    : null,
                remainingAmount: plainPayment.expectedAmount - plainPayment.paidAmount
            }
        })

        return res.status(200).json({
            message: `Danh sách đóng học phí của học sinh ${userExists.lastName} ${userExists.firstName}`,
            data: formattedPayments,
            user: {
                id: userExists.id,
                lastName: userExists.lastName,
                firstName: userExists.firstName,
                username: userExists.username,
                avatarUrl: userExists.avatarUrl
            },
            pagination: {
                page,
                limit,
                totalRows: total,
                totalPages: Math.ceil(total / limit)
            }
        })
    } catch (error) {
        console.error('Lỗi khi lấy danh sách đóng học phí của học sinh:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Lấy thông tin chi tiết một khoản đóng học phí
 */
export const getTuitionPaymentById = async (req, res) => {
    try {
        const { id } = req.params
        const payment = await db.TuitionPayment.findByPk(id, {
            include: [
                {
                    model: db.User,
                    as: 'user',
                    attributes: ['id', 'lastName', 'firstName', 'username', 'avatarUrl', 'class', 'highSchool', 'phone']
                }
            ]
        })

        if (!payment) {
            return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
        }

        // Format lại dữ liệu trước khi trả về
        const plainPayment = payment.get({ plain: true })
        const [year, month] = plainPayment.month.split('-')
        const monthNames = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ]
        const formattedPayment = {
            ...plainPayment,
            monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
            paymentDateFormatted: plainPayment.paymentDate
                ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                : null,
            studentName: `${plainPayment.user.lastName} ${plainPayment.user.firstName}`,
            userClass: plainPayment.user.class,
            remainingAmount: plainPayment.expectedAmount - plainPayment.paidAmount
        }

        return res.status(200).json({
            message: 'Chi tiết khoản đóng học phí',
            data: formattedPayment
        })
    } catch (error) {
        console.error('Lỗi khi lấy chi tiết khoản đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Học sinh xem chi tiết khoản đóng học phí của mình
 */
export const getUserTuitionPaymentById = async (req, res) => {
    try {
        const { id } = req.params
        const userId = req.user.id

        // Tìm khoản đóng học phí
        const payment = await db.TuitionPayment.findByPk(id, {
            include: [
                {
                    model: db.User,
                    as: 'user',
                    attributes: ['id', 'lastName', 'firstName', 'username', 'avatarUrl', 'class']
                }
            ]
        })

        if (!payment) {
            return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
        }

        // Kiểm tra quyền truy cập
        if (payment.userId !== userId) {
            return res.status(403).json({ message: 'Bạn không có quyền xem khoản đóng học phí này' })
        }

        // Format lại dữ liệu trước khi trả về
        const plainPayment = payment.get({ plain: true })
        const [year, month] = plainPayment.month.split('-')
        const monthNames = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ]
        const formattedPayment = {
            ...plainPayment,
            monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
            paymentDateFormatted: plainPayment.paymentDate
                ? new Date(plainPayment.paymentDate).toLocaleDateString('vi-VN')
                : null,
            studentName: `${plainPayment.user.lastName} ${plainPayment.user.firstName}`,
            userClass: plainPayment.user.class,
            remainingAmount: plainPayment.expectedAmount - plainPayment.paidAmount
        }

        return res.status(200).json({
            message: 'Chi tiết khoản đóng học phí',
            data: formattedPayment
        })
    } catch (error) {
        console.error('Lỗi khi lấy chi tiết khoản đóng học phí của học sinh:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Tạo mới khoản đóng học phí cho học sinh
 */
export const createTuitionPayment = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { userId, month, expectedAmount, paidAmount, paymentDate, status, note } = req.body

        // Kiểm tra học sinh có tồn tại không
        const userExists = await db.User.findByPk(userId, { transaction })
        if (!userExists) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Học sinh không tồn tại' })
        }

        if (userExists.isActive === false) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Học sinh này không còn hoạt động' })
        }



        // Kiểm tra xem đã có khoản đóng học phí cho học sinh này trong tháng này chưa
        const existingPayment = await db.TuitionPayment.findOne({
            where: {
                userId,
                month
            },
            transaction
        })

        if (existingPayment) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Đã tồn tại khoản đóng học phí cho học sinh này trong tháng này' })
        }
        let isCustom = true;
        // Tính toán số tiền học phí dự kiến nếu không được cung cấp
        let calculatedExpectedAmount = expectedAmount;
        if (!calculatedExpectedAmount) {
            calculatedExpectedAmount = await tuitionService.calculateExpectedAmount(userId, month, { transaction });
            isCustom = false;
        }

        // Tạo mới khoản đóng học phí
        const newPayment = await db.TuitionPayment.create({
            userId,
            month,
            expectedAmount: calculatedExpectedAmount,
            paidAmount: paidAmount || 0,
            paymentDate: paymentDate ? new Date(paymentDate) : null,
            status: status || (paidAmount > 0 ? TuitionStatus.UNPAID : TuitionStatus.PAID),
            note,
            isCustom
        }, { transaction })

        await transaction.commit()

        // Gửi thông báo cho học sinh
        try {
            const io = req.app.get('io')
            if (io) {
                const [year, monthNum] = month.split('-')
                const monthNames = [
                    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
                ]
                const monthFormatted = `${monthNames[parseInt(monthNum) - 1]} ${year}`

                await sendUserNotification(
                    io,
                    userId,
                    "Thông báo học phí",
                    `Bạn có khoản học phí tháng ${monthFormatted} cần thanh toán`,
                    "TUITION",
                    newPayment.id,
                    "CLASS",
                    `/tuition-payment/${newPayment.id}`
                )
            }
        } catch (notificationError) {
            console.error("Lỗi khi gửi thông báo học phí:", notificationError)
            // Không ảnh hưởng đến kết quả trả về
        }

        return res.status(201).json({
            message: 'Tạo khoản đóng học phí thành công',
            data: newPayment
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi tạo khoản đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Cập nhật thông tin khoản đóng học phí
 */
export const updateTuitionPayment = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { id } = req.params
        const { expectedAmount, paidAmount, paymentDate, status, note, calculateExpected } = req.body

        // Kiểm tra khoản đóng học phí có tồn tại không
        const payment = await db.TuitionPayment.findByPk(id, {
            include: [],
            transaction
        })

        if (!payment) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
        }

        let isCustom = payment.isCustom;
        // Tính toán lại số tiền học phí dự kiến nếu được yêu cầu
        let updatedExpectedAmount = expectedAmount;
        if (calculateExpected === true) {
            updatedExpectedAmount = await tuitionService.calculateExpectedAmount(
                payment.userId,
                payment.month,
                { transaction }
            );
            isCustom = false;
        } else if (expectedAmount !== undefined) {
            updatedExpectedAmount = expectedAmount;
            isCustom = true;
        } else {
            updatedExpectedAmount = payment.expectedAmount;
        }

        // Xác định trạng thái dựa trên số tiền đã đóng
        let newStatus = status
        if (!newStatus) {
            const newPaidAmount = paidAmount !== undefined ? paidAmount : payment.paidAmount
            const expectedAmt = updatedExpectedAmount || 0
            if (newPaidAmount <= 0) {
                newStatus = 'UNPAID'
            } else if (newPaidAmount < expectedAmt) {
                newStatus = 'PARTIAL'
            } else {
                newStatus = 'PAID'
            }
        }

        // Cập nhật thông tin khoản đóng học phí
        await payment.update({
            expectedAmount: updatedExpectedAmount,
            paidAmount: paidAmount !== undefined ? paidAmount : payment.paidAmount,
            paymentDate: paymentDate ? new Date(paymentDate) : payment.paymentDate,
            status: newStatus,
            note: note !== undefined ? note : payment.note,
            isCustom
        }, { transaction })

        await transaction.commit()

        // Gửi thông báo cho học sinh nếu đã thanh toán đủ
        if (newStatus === 'PAID' && payment.status !== 'PAID') {
            try {
                const io = req.app.get('io')
                if (io) {
                    const [year, monthNum] = payment.month.split('-')
                    const monthNames = [
                        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
                    ]
                    const monthFormatted = `${monthNames[parseInt(monthNum) - 1]} ${year}`

                    await sendUserNotification(
                        io,
                        payment.userId,
                        "Xác nhận thanh toán học phí",
                        `Học phí tháng ${monthFormatted} đã được thanh toán đầy đủ`,
                        "TUITION",
                        payment.id,
                        "CLASS",
                        `/tuition-payment/${payment.id}`
                    )
                }
            } catch (notificationError) {
                console.error("Lỗi khi gửi thông báo xác nhận học phí:", notificationError)
                // Không ảnh hưởng đến kết quả trả về
            }
        }

        return res.status(200).json({
            message: 'Cập nhật khoản đóng học phí thành công',
            data: payment
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi cập nhật khoản đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Xóa khoản đóng học phí
 */
export const deleteTuitionPayment = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { id } = req.params

        // Kiểm tra khoản đóng học phí có tồn tại không
        const payment = await db.TuitionPayment.findByPk(id, { transaction })
        if (!payment) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' })
        }

        // Kiểm tra xem khoản đóng học phí đã được thanh toán chưa
        if (payment.status === 'PAID') {
            await transaction.rollback()
            return res.status(400).json({ message: 'Không thể xóa khoản đóng học phí đã được thanh toán' })
        }

        // Xóa khoản đóng học phí
        await payment.destroy({ transaction })

        await transaction.commit()

        return res.status(200).json({
            message: 'Xóa khoản đóng học phí thành công',
            data: id
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi xóa khoản đóng học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Tạo khoản đóng học phí cho tất cả học sinh trong hệ thống
 */
export const createTuitionPaymentsForAllStudents = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    try {
        const { month, paidAmount, note, dueDate, batchClass, expectedAmount } = req.body

        if (!month) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Tháng không được để trống' })
        }

        if (!batchClass) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Lớp không được để trống' })
        }

        // Kiểm tra định dạng tháng (YYYY-MM)
        const monthRegex = /^\d{4}-\d{2}$/
        if (!monthRegex.test(month)) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Định dạng tháng không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM' })
        }

        // Lấy danh sách tất cả học sinh đang hoạt động
        const students = await db.User.findAll({
            where: {
                userType: UserType.STUDENT,
                isActive: true,
                class: batchClass
            },
            attributes: ['id', 'lastName', 'firstName'],
            transaction
        })

        if (students.length === 0) {
            await transaction.rollback()
            return res.status(400).json({ message: 'Không có học sinh nào trong hệ thống' })
        }

        // Tạo cache để lưu trữ học phí đã truy vấn
        const cache = {
            classTuitions: {} // Lưu trữ học phí theo định dạng: { 'classId_month': amount }
        };

        // Tạo khoản đóng học phí cho từng học sinh
        const createdPayments = []
        const skippedPayments = []


        for (const student of students) {
            let isCustom = true;
            // Kiểm tra xem học sinh đã có khoản đóng học phí trong tháng này chưa
            const existingPayment = await db.TuitionPayment.findOne({
                where: {
                    userId: student.id,
                    month
                },
                transaction
            })


            // Nếu chưa có, tạo mới
            if (!existingPayment) {
                // Tính toán số tiền học phí dự kiến nếu không được cung cấp
                let calculatedExpectedAmount = expectedAmount;
                if (!calculatedExpectedAmount) {
                    // Sử dụng cache để tránh truy vấn lặp lại
                    calculatedExpectedAmount = await tuitionService.calculateExpectedAmount(
                        student.id,
                        month,
                        { transaction },
                        cache
                    );
                    isCustom = false;
                }

                const newPayment = await db.TuitionPayment.create({
                    userId: student.id,
                    month,
                    expectedAmount: calculatedExpectedAmount,
                    paidAmount: paidAmount || 0,
                    dueDate: dueDate ? new Date(dueDate) : null,
                    status: paidAmount > 0 ? TuitionStatus.PAID : TuitionStatus.UNPAID,
                    note,
                    isCustom
                }, { transaction })

                createdPayments.push({
                    id: newPayment.id,
                    userId: student.id,
                    studentName: `${student.lastName} ${student.firstName}`
                })
            } else {
                skippedPayments.push({
                    userId: student.id,
                    studentName: `${student.lastName} ${student.firstName}`
                })
            }
        }

        await transaction.commit()

        // Gửi thông báo cho tất cả học sinh đã được tạo khoản học phí
        try {
            const io = req.app.get('io')
            if (io) {
                const [year, monthNum] = month.split('-')
                const monthNames = [
                    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
                ]
                const monthFormatted = `${monthNames[parseInt(monthNum) - 1]} ${year}`

                for (const payment of createdPayments) {
                    await sendUserNotification(
                        io,
                        payment.userId,
                        "Thông báo học phí",
                        `Bạn có khoản học phí tháng ${monthFormatted} cần thanh toán`,
                        "TUITION",
                        payment.id,
                        "PAYMENT",
                        `/tuition-payment/${payment.id}`
                    )
                }
            }
        } catch (notificationError) {
            console.error("Lỗi khi gửi thông báo học phí:", notificationError)
            // Không ảnh hưởng đến kết quả trả về
        }

        // console.log(createdPayments)

        return res.status(201).json({
            message: `Đã tạo ${createdPayments.length} khoản đóng học phí cho học sinh`,
            data: {
                createdCount: createdPayments.length,
                skippedCount: skippedPayments.length,
                totalStudents: students.length,
                createdPayments,
                skippedPayments
            }
        })
    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi tạo khoản đóng học phí cho học sinh:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Thống kê doanh thu học phí theo tháng và lớp
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - Thống kê doanh thu học phí
 */
/**
 * Lấy thống kê tổng quan về các khoản học phí của một học sinh
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - Thống kê tổng quan về các khoản học phí
 */
export const getUserTuitionSummary = async (req, res) => {
    try {
        const { userId } = req.params;

        // Kiểm tra userId có tồn tại không
        if (!userId) {
            return res.status(400).json({ message: 'Thiếu thông tin userId' });
        }

        // Kiểm tra user có tồn tại không
        const user = await db.User.findByPk(userId);
        if (!user) {
            return res.status(404).json({ message: 'Không tìm thấy người dùng' });
        }

        // Lấy tất cả các khoản học phí của học sinh
        const tuitionPayments = await db.TuitionPayment.findAll({
            where: { userId }
        });

        // Nếu không có khoản học phí nào
        if (!tuitionPayments || tuitionPayments.length === 0) {
            return res.status(200).json({
                message: 'Không có khoản học phí nào',
                data: {
                    totalPayments: 0,
                    overduePayments: 0,
                    paidPayments: 0,
                    unpaidPayments: 0,
                    totalExpectedAmount: 0,
                    totalPaidAmount: 0,
                    remainingAmount: 0,
                    paymentRate: 0
                }
            });
        }

        // Tính toán các thống kê
        const currentDate = new Date();

        // Số lượng các khoản học phí
        const totalPayments = tuitionPayments.length;

        // Số khoản quá hạn (chưa thanh toán đủ và đã quá hạn)
        const overduePayments = tuitionPayments.filter(payment => {
            const dueDate = new Date(payment.dueDate);
            return (payment.status !== 'PAID' && currentDate > dueDate);
        }).length;

        // Số khoản đã thanh toán
        const paidPayments = tuitionPayments.filter(payment => payment.status === 'PAID').length;

        // Số khoản chưa thanh toán hoặc thanh toán một phần
        const unpaidPayments = tuitionPayments.filter(payment => payment.status !== 'PAID').length;

        // Tổng số tiền dự kiến
        const totalExpectedAmount = tuitionPayments.reduce((sum, payment) => sum + parseFloat(payment.expectedAmount || 0), 0);

        // Tổng số tiền đã thanh toán
        const totalPaidAmount = tuitionPayments.reduce((sum, payment) => sum + parseFloat(payment.paidAmount || 0), 0);

        // Số tiền còn lại
        const remainingAmount = totalExpectedAmount - totalPaidAmount;

        // Tỷ lệ thanh toán
        const paymentRate = totalExpectedAmount > 0 ? (totalPaidAmount / totalExpectedAmount * 100).toFixed(2) : 0;

        // Trả về kết quả
        return res.status(200).json({
            message: 'Thống kê tổng quan về các khoản học phí',
            data: {
                totalPayments,
                overduePayments,
                paidPayments,
                unpaidPayments,
                totalExpectedAmount,
                totalPaidAmount,
                remainingAmount,
                paymentRate
            }
        });
    } catch (error) {
        console.error('Lỗi khi lấy thống kê tổng quan về các khoản học phí:', error);
        return res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

export const getTuitionStatistics = async (req, res) => {
    try {
        const { startMonth, endMonth, userClass } = req.query

        // Validate startMonth và endMonth (định dạng YYYY-MM)
        const monthRegex = /^\d{4}-\d{2}$/
        if (startMonth && !monthRegex.test(startMonth)) {
            return res.status(400).json({ message: 'Định dạng tháng bắt đầu không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM' })
        }
        if (endMonth && !monthRegex.test(endMonth)) {
            return res.status(400).json({ message: 'Định dạng tháng kết thúc không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM' })
        }

        // Xây dựng điều kiện tìm kiếm
        let whereClause = {}

        // Lọc theo tháng
        if (startMonth && endMonth) {
            whereClause.month = {
                [Op.between]: [startMonth, endMonth]
            }
        } else if (startMonth) {
            whereClause.month = {
                [Op.gte]: startMonth
            }
        } else if (endMonth) {
            whereClause.month = {
                [Op.lte]: endMonth
            }
        }

        // Lọc theo lớp
        if (userClass) {
            whereClause['$user.class$'] = userClass
        }

        // Thực hiện truy vấn thống kê
        const statistics = await db.TuitionPayment.findAll({
            attributes: [
                'month',
                [fn('SUM', col('TuitionPayment.expectedAmount')), 'totalExpectedAmount'],
                [fn('SUM', col('TuitionPayment.paidAmount')), 'totalPaidAmount'],
                [fn('COUNT', col('TuitionPayment.id')), 'totalPayments']
            ],
            include: [
                {
                    model: db.User,
                    as: 'user',
                    attributes: ['class'],
                    required: !!userClass
                }
            ],
            where: whereClause,
            group: ['month', 'user.class'],
            order: [['month', 'ASC']],
            subQuery: false, // Tắt subquery để tránh Sequelize tự động thêm các cột
            raw: true // Trả về kết quả dạng raw để tránh Sequelize tự động thêm các cột
        })

        // Format lại dữ liệu để dễ sử dụng cho biểu đồ
        const formattedStatistics = statistics.map(stat => {
            // Khi sử dụng raw: true, kết quả trả về là object JavaScript thông thường
            const [year, month] = stat.month.split('-')
            const monthNames = [
                'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
            ]

            return {
                month: stat.month,
                monthFormatted: `${monthNames[parseInt(month) - 1]} ${year}`,
                userClass: stat['user.class'] || 'Tất cả',
                totalExpectedAmount: parseFloat(stat.totalExpectedAmount) || 0,
                totalPaidAmount: parseFloat(stat.totalPaidAmount) || 0,
                totalPayments: parseInt(stat.totalPayments) || 0,
                remainingAmount: parseFloat(stat.totalExpectedAmount) - parseFloat(stat.totalPaidAmount) || 0,
                collectionRate: stat.totalExpectedAmount > 0
                    ? (parseFloat(stat.totalPaidAmount) / parseFloat(stat.totalExpectedAmount) * 100).toFixed(2)
                    : 0
            }
        })

        // Tổng hợp thống kê theo tháng (tất cả các lớp)
        const monthlyStatistics = {}
        formattedStatistics.forEach(stat => {
            if (!monthlyStatistics[stat.month]) {
                monthlyStatistics[stat.month] = {
                    month: stat.month,
                    monthFormatted: stat.monthFormatted,
                    totalExpectedAmount: 0,
                    totalPaidAmount: 0,
                    totalPayments: 0,
                    remainingAmount: 0,
                    collectionRate: 0,
                    classes: {}
                }
            }

            // Cập nhật tổng thống kê theo tháng
            monthlyStatistics[stat.month].totalExpectedAmount += stat.totalExpectedAmount
            monthlyStatistics[stat.month].totalPaidAmount += stat.totalPaidAmount
            monthlyStatistics[stat.month].totalPayments += stat.totalPayments
            monthlyStatistics[stat.month].remainingAmount += stat.remainingAmount

            // Cập nhật tỷ lệ thu
            if (monthlyStatistics[stat.month].totalExpectedAmount > 0) {
                monthlyStatistics[stat.month].collectionRate =
                    (monthlyStatistics[stat.month].totalPaidAmount / monthlyStatistics[stat.month].totalExpectedAmount * 100).toFixed(2)
            }

            // Thêm thống kê theo lớp
            if (stat.userClass) {
                monthlyStatistics[stat.month].classes[stat.userClass] = {
                    totalExpectedAmount: stat.totalExpectedAmount,
                    totalPaidAmount: stat.totalPaidAmount,
                    totalPayments: stat.totalPayments,
                    remainingAmount: stat.remainingAmount,
                    collectionRate: stat.collectionRate
                }
            }
        })

        // Chuyển đổi thành mảng để dễ sử dụng
        const monthlyStatisticsArray = Object.values(monthlyStatistics)

        // Tổng hợp thống kê theo lớp (tất cả các tháng)
        const classStatistics = {}
        formattedStatistics.forEach(stat => {
            if (stat.userClass) {
                if (!classStatistics[stat.userClass]) {
                    classStatistics[stat.userClass] = {
                        userClass: stat.userClass,
                        totalExpectedAmount: 0,
                        totalPaidAmount: 0,
                        totalPayments: 0,
                        remainingAmount: 0,
                        collectionRate: 0,
                        months: {}
                    }
                }

                // Cập nhật tổng thống kê theo lớp
                classStatistics[stat.userClass].totalExpectedAmount += stat.totalExpectedAmount
                classStatistics[stat.userClass].totalPaidAmount += stat.totalPaidAmount
                classStatistics[stat.userClass].totalPayments += stat.totalPayments
                classStatistics[stat.userClass].remainingAmount += stat.remainingAmount

                // Cập nhật tỷ lệ thu
                if (classStatistics[stat.userClass].totalExpectedAmount > 0) {
                    classStatistics[stat.userClass].collectionRate =
                        (classStatistics[stat.userClass].totalPaidAmount / classStatistics[stat.userClass].totalExpectedAmount * 100).toFixed(2)
                }

                // Thêm thống kê theo tháng
                classStatistics[stat.userClass].months[stat.month] = {
                    month: stat.month,
                    monthFormatted: stat.monthFormatted,
                    totalExpectedAmount: stat.totalExpectedAmount,
                    totalPaidAmount: stat.totalPaidAmount,
                    totalPayments: stat.totalPayments,
                    remainingAmount: stat.remainingAmount,
                    collectionRate: stat.collectionRate
                }
            }
        })

        // Chuyển đổi thành mảng để dễ sử dụng
        const classStatisticsArray = Object.values(classStatistics)

        // Tính tổng thống kê cho tất cả các tháng và lớp
        const totalStatistics = {
            totalExpectedAmount: formattedStatistics.reduce((sum, stat) => sum + stat.totalExpectedAmount, 0),
            totalPaidAmount: formattedStatistics.reduce((sum, stat) => sum + stat.totalPaidAmount, 0),
            totalPayments: formattedStatistics.reduce((sum, stat) => sum + stat.totalPayments, 0),
            remainingAmount: formattedStatistics.reduce((sum, stat) => sum + stat.remainingAmount, 0),
            collectionRate: 0
        }

        // Cập nhật tỷ lệ thu tổng
        if (totalStatistics.totalExpectedAmount > 0) {
            totalStatistics.collectionRate =
                (totalStatistics.totalPaidAmount / totalStatistics.totalExpectedAmount * 100).toFixed(2)
        }

        // Chuẩn bị dữ liệu cho biểu đồ
        const chartData = {
            labels: monthlyStatisticsArray.map(stat => stat.monthFormatted),
            datasets: [
                {
                    label: 'Số tiền cần thu',
                    data: monthlyStatisticsArray.map(stat => stat.totalExpectedAmount)
                },
                {
                    label: 'Số tiền đã thu',
                    data: monthlyStatisticsArray.map(stat => stat.totalPaidAmount)
                },
                {
                    label: 'Số tiền còn lại',
                    data: monthlyStatisticsArray.map(stat => stat.remainingAmount)
                }
            ]
        }

        // Chuẩn bị dữ liệu cho biểu đồ theo lớp
        const classChartData = {
            labels: classStatisticsArray.map(stat => `Lớp ${stat.userClass}`),
            datasets: [
                {
                    label: 'Số tiền cần thu',
                    data: classStatisticsArray.map(stat => stat.totalExpectedAmount)
                },
                {
                    label: 'Số tiền đã thu',
                    data: classStatisticsArray.map(stat => stat.totalPaidAmount)
                },
                {
                    label: 'Số tiền còn lại',
                    data: classStatisticsArray.map(stat => stat.remainingAmount)
                }
            ]
        }

        return res.status(200).json({
            message: 'Thống kê doanh thu học phí',
            data: {
                detailedStatistics: formattedStatistics,
                monthlyStatistics: monthlyStatisticsArray,
                classStatistics: classStatisticsArray,
                totalStatistics,
                chartData,
                classChartData
            }
        })
    } catch (error) {
        console.error('Lỗi khi thống kê doanh thu học phí:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

/**
 * Tính toán lại số tiền học phí dự kiến (expectedAmount) cho một khoản đóng học phí
 */
export const recalculateExpectedAmount = async (req, res) => {
    const transaction = await db.sequelize.transaction();
    try {
        const { id } = req.params;

        // Kiểm tra khoản đóng học phí có tồn tại không
        const payment = await db.TuitionPayment.findByPk(id, { transaction });

        if (!payment) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Khoản đóng học phí không tồn tại' });
        }

        // Sử dụng service để tính toán và cập nhật số tiền học phí dự kiến
        const updatedPayment = await tuitionService.updateExpectedAmountForTuitionPayment(id, { transaction });

        await transaction.commit();

        return res.status(200).json({
            message: 'Đã tính toán lại số tiền học phí dự kiến',
            data: updatedPayment
        });
    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi tính toán lại số tiền học phí dự kiến:', error);
        return res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

/**
 * Lấy danh sách học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
 */
export const getStudentClassTuitionsByMonth = async (req, res) => {
    try {
        const userId = req.user.id; // Lấy ID của học sinh đang đăng nhập
        const { month } = req.params; // Lấy tháng từ params

        // Kiểm tra định dạng tháng
        if (!month || !/^\d{4}-\d{2}$/.test(month)) {
            return res.status(400).json({
                message: 'Định dạng tháng không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM'
            });
        }

        // Gọi service để lấy danh sách học phí
        const classTuitions = await tuitionService.getClassTuitionsByStudentAndMonth(userId, month);

        // Tính tổng số tiền học phí
        const totalAmount = classTuitions.reduce((sum, tuition) => sum + tuition.amount, 0);

        return res.status(200).json({
            message: 'Danh sách học phí của các lớp',
            data: {
                classTuitions,
                totalAmount,
                month,
                monthFormatted: formatMonthToVietnamese(month)
            }
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách học phí của các lớp:', error);
        return res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

/**
 * Admin xem danh sách học phí của các lớp mà học sinh đã tham gia trong một tháng cụ thể
 */
export const getStudentClassTuitionsByMonthAdmin = async (req, res) => {
    try {
        const { userId, month } = req.params; // Lấy ID học sinh và tháng từ params
        console.log('userId', userId);
        console.log('month', month);
        // Kiểm tra định dạng tháng
        if (!month || !/^\d{4}-\d{2}$/.test(month)) {
            return res.status(400).json({
                message: 'Định dạng tháng không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM'
            });
        }

        // Kiểm tra userId có tồn tại không
        if (!userId) {
            return res.status(400).json({ message: 'ID học sinh không được để trống' });
        }

        // Kiểm tra học sinh có tồn tại không
        const student = await db.User.findByPk(userId);
        if (!student) {
            return res.status(404).json({ message: 'Không tìm thấy học sinh' });
        }

        // Gọi service để lấy danh sách học phí
        const classTuitions = await tuitionService.getClassTuitionsByStudentAndMonth(userId, month);

        // Tính tổng số tiền học phí
        const totalAmount = classTuitions.reduce((sum, tuition) => sum + tuition.amount, 0);

        // Lấy thông tin học sinh
        const studentInfo = {
            id: student.id,
            name: student.name,
            email: student.email,
            phone: student.phone
        };

        return res.status(200).json({
            message: 'Danh sách học phí của các lớp',
            data: {
                student: studentInfo,
                classTuitions,
                totalAmount,
                month,
                monthFormatted: formatMonthToVietnamese(month)
            }
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách học phí của các lớp:', error);
        return res.status(500).json({ message: 'Lỗi server', error: error.message });
    }
};

/**
 * Hàm hỗ trợ để định dạng tháng sang tiếng Việt
 */
const formatMonthToVietnamese = (monthStr) => {
    const [year, month] = monthStr.split('-');
    const monthNames = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
};

export default {
    getAllTuitionPayments,
    getTuitionPaymentsByClassId,
    getTuitionPaymentsByUserId,
    createTuitionPayment,
    updateTuitionPayment,
    deleteTuitionPayment,
    createTuitionPaymentsForAllStudents,
    getTuitionStatistics,
    recalculateExpectedAmount,
    getStudentClassTuitionsByMonth,
    getStudentClassTuitionsByMonthAdmin
}
