{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\components\\\\questions\\\\MultipleChoiceQuestion.jsx\";\nimport React from 'react';\nimport LatexRenderer from '../latex/RenderLatex';\nimport QuestionImage from './QuestionImage';\nimport { Bookmark } from 'lucide-react';\nimport ReportButton from '../button/ReportButton';\nimport NoTranslate from '../utils/NoTranslate';\n\n/**\r\n * Component hiển thị câu hỏi trắc nghiệm\r\n *\r\n * @param {Object} props - Component props\r\n * @param {Object} props.question - Dữ liệu câu hỏi\r\n * @param {number} props.index - Chỉ số của câu hỏi\r\n * @param {Function} props.handleSelectAnswer - Hàm xử lý khi chọn câu trả lời\r\n * @param {Function} props.isSelected - Hàm kiểm tra xem câu trả lời có được chọn không\r\n * @param {string|number|null} props.selectedQuestion - ID của câu hỏi đang được chọn\r\n * @param {boolean} props.isDarkMode - Chế độ tối\r\n * @param {number} props.fontSize - Cỡ chữ\r\n * @param {number} props.imageSize - Kích thước hình ảnh\r\n * @param {Array} props.prefixStatements - Mảng các tiền tố cho câu trả lời (A, B, C, D...)\r\n * @param {Function} props.setRef - Hàm thiết lập ref cho câu hỏi\r\n * @param {boolean} props.isTimeUp - Đã hết thời gian làm bài\r\n * @param {Set} props.markedQuestions - Set các câu hỏi đã đánh dấu\r\n * @param {Function} props.toggleMarkQuestion - Hàm đánh dấu câu hỏi\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MultipleChoiceQuestion = _ref => {\n  let {\n    question,\n    index,\n    handleSelectAnswer,\n    isSelected,\n    selectedQuestion,\n    isDarkMode,\n    fontSize,\n    imageSize,\n    prefixStatements,\n    setRef,\n    isTimeUp,\n    markedQuestions = new Set(),\n    toggleMarkQuestion = () => {}\n  } = _ref;\n  const isMarked = markedQuestions.has(question.id);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: el => setRef(question.id, el),\n    \"data-question-id\": question.id,\n    className: \"flex flex-col avoid-page-break gap-2 rounded-md p-3 transition\\n        \".concat(selectedQuestion === question.id ? isDarkMode ? \"border-2 border-yellow-400 bg-gray-700\" : \"border-2 border-yellow-400 bg-yellow-50\" : \"\"),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-start\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-bold\",\n          style: {\n            fontSize: \"\".concat(fontSize, \"px\")\n          },\n          children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n            children: [\"C\\xE2u \", index + 1, \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ReportButton, {\n          questionId: question.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: e => {\n          e.preventDefault();\n          e.stopPropagation();\n          toggleMarkQuestion(question.id);\n        },\n        className: \"p-1 rounded-full transition-colors \".concat(isMarked ? isDarkMode ? 'text-sky-400 hover:bg-gray-700' : 'text-sky-600 hover:bg-gray-100' : isDarkMode ? 'text-gray-500 hover:bg-gray-700' : 'text-gray-400 hover:bg-gray-100'),\n        title: isMarked ? \"Bỏ đánh dấu\" : \"Đánh dấu để xem lại\",\n        translate: \"no\",\n        children: /*#__PURE__*/_jsxDEV(Bookmark, {\n          size: 20,\n          fill: isMarked ? \"currentColor\" : \"none\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n      text: question.content,\n      className: \"\",\n      style: {\n        fontSize: \"\".concat(fontSize, \"px\")\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuestionImage, {\n      imageUrl: question.imageUrl,\n      imageSize: imageSize\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-2\",\n      children: question.statements.map((statement, statementIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"radio\",\n          name: \"question-\".concat(question.id),\n          value: statement.id,\n          checked: isSelected(question.id, statement.id),\n          onChange: () => handleSelectAnswer(question.id, statement.id, question.typeOfQuestion),\n          disabled: isTimeUp,\n          className: \"w-5 h-5 accent-sky-600 \".concat(isTimeUp ? 'cursor-not-allowed opacity-60' : 'cursor-pointer')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-bold\",\n              style: {\n                fontSize: \"\".concat(fontSize, \"px\")\n              },\n              children: /*#__PURE__*/_jsxDEV(NoTranslate, {\n                children: prefixStatements[statementIndex]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(LatexRenderer, {\n              text: statement.content,\n              className: \"break-words\",\n              style: {\n                fontSize: \"\".concat(fontSize, \"px\")\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(QuestionImage, {\n            imageUrl: statement.imageUrl,\n            imageSize: imageSize,\n            isStatement: true,\n            altText: \"statement image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this)]\n      }, statement.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, question.id + \"TN\", true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_c = MultipleChoiceQuestion;\nexport default MultipleChoiceQuestion;\nvar _c;\n$RefreshReg$(_c, \"MultipleChoiceQuestion\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "QuestionImage", "Bookmark", "ReportButton", "NoTranslate", "jsxDEV", "_jsxDEV", "MultipleChoiceQuestion", "_ref", "question", "index", "handleSelectAnswer", "isSelected", "selectedQuestion", "isDarkMode", "fontSize", "imageSize", "prefixStatements", "setRef", "isTimeUp", "markedQuestions", "Set", "toggleMarkQuestion", "isMarked", "has", "id", "ref", "el", "className", "concat", "children", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questionId", "onClick", "e", "preventDefault", "stopPropagation", "title", "translate", "size", "fill", "text", "content", "imageUrl", "statements", "map", "statement", "statementIndex", "type", "name", "value", "checked", "onChange", "typeOfQuestion", "disabled", "isStatement", "altText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/components/questions/MultipleChoiceQuestion.jsx"], "sourcesContent": ["import React from 'react';\r\nimport LatexRenderer from '../latex/RenderLatex';\r\nimport QuestionImage from './QuestionImage';\r\nimport { Bookmark } from 'lucide-react';\r\nimport ReportButton from '../button/ReportButton';\r\nimport NoTranslate from '../utils/NoTranslate';\r\n\r\n/**\r\n * Component hiển thị câu hỏi trắc nghiệm\r\n *\r\n * @param {Object} props - Component props\r\n * @param {Object} props.question - Dữ liệu câu hỏi\r\n * @param {number} props.index - Chỉ số của câu hỏi\r\n * @param {Function} props.handleSelectAnswer - Hàm xử lý khi chọn câu trả lời\r\n * @param {Function} props.isSelected - Hàm kiểm tra xem câu trả lời có được chọn không\r\n * @param {string|number|null} props.selectedQuestion - ID của câu hỏi đang được chọn\r\n * @param {boolean} props.isDarkMode - <PERSON><PERSON> độ tối\r\n * @param {number} props.fontSize - Cỡ chữ\r\n * @param {number} props.imageSize - <PERSON><PERSON><PERSON> thư<PERSON><PERSON> h<PERSON>nh <PERSON>\r\n * @param {Array} props.prefixStatements - Mảng các tiền tố cho câu trả lời (A, B, C, D...)\r\n * @param {Function} props.setRef - Hàm thiết lập ref cho câu hỏi\r\n * @param {boolean} props.isTimeUp - Đã hết thời gian làm bài\r\n * @param {Set} props.markedQuestions - Set các câu hỏi đã đánh dấu\r\n * @param {Function} props.toggleMarkQuestion - Hàm đánh dấu câu hỏi\r\n */\r\nconst MultipleChoiceQuestion = ({\r\n  question,\r\n  index,\r\n  handleSelectAnswer,\r\n  isSelected,\r\n  selectedQuestion,\r\n  isDarkMode,\r\n  fontSize,\r\n  imageSize,\r\n  prefixStatements,\r\n  setRef,\r\n  isTimeUp,\r\n  markedQuestions = new Set(),\r\n  toggleMarkQuestion = () => { }\r\n}) => {\r\n  const isMarked = markedQuestions.has(question.id);\r\n  return (\r\n    <div\r\n      key={question.id + \"TN\"}\r\n      ref={(el) => setRef(question.id, el)}\r\n      data-question-id={question.id}\r\n      className={`flex flex-col avoid-page-break gap-2 rounded-md p-3 transition\r\n        ${selectedQuestion === question.id\r\n          ? isDarkMode\r\n            ? \"border-2 border-yellow-400 bg-gray-700\"\r\n            : \"border-2 border-yellow-400 bg-yellow-50\"\r\n          : \"\"}`}\r\n    >\r\n      <div className=\"flex justify-between items-start\">\r\n        <div className='flex gap-2'>\r\n          <p className=\"font-bold\" style={{ fontSize: `${fontSize}px` }}>\r\n            <NoTranslate>Câu {index + 1}:</NoTranslate>\r\n          </p>\r\n\r\n          {/* Nút đánh dấu câu hỏi */}\r\n\r\n\r\n          <ReportButton questionId={question.id} />\r\n        </div>\r\n\r\n        <button\r\n          onClick={(e) => {\r\n            e.preventDefault();\r\n            e.stopPropagation();\r\n            toggleMarkQuestion(question.id);\r\n          }}\r\n          className={`p-1 rounded-full transition-colors ${isMarked\r\n              ? (isDarkMode ? 'text-sky-400 hover:bg-gray-700' : 'text-sky-600 hover:bg-gray-100')\r\n              : (isDarkMode ? 'text-gray-500 hover:bg-gray-700' : 'text-gray-400 hover:bg-gray-100')\r\n            }`}\r\n          title={isMarked ? \"Bỏ đánh dấu\" : \"Đánh dấu để xem lại\"}\r\n          translate=\"no\"\r\n        >\r\n          <Bookmark size={20} fill={isMarked ? \"currentColor\" : \"none\"} />\r\n        </button>\r\n      </div>\r\n\r\n      <LatexRenderer text={question.content} className=\"\" style={{ fontSize: `${fontSize}px` }} />\r\n\r\n      <QuestionImage\r\n        imageUrl={question.imageUrl}\r\n        imageSize={imageSize}\r\n      />\r\n\r\n      <div className=\"flex flex-col gap-2\">\r\n        {question.statements.map((statement, statementIndex) => (\r\n          <div key={statement.id} className=\"flex items-center gap-2\">\r\n            <input\r\n              type=\"radio\"\r\n              name={`question-${question.id}`}\r\n              value={statement.id}\r\n              checked={isSelected(question.id, statement.id)}\r\n              onChange={() =>\r\n                handleSelectAnswer(question.id, statement.id, question.typeOfQuestion)\r\n              }\r\n              disabled={isTimeUp}\r\n              className={`w-5 h-5 accent-sky-600 ${isTimeUp ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}\r\n            />\r\n\r\n            <div className=\"flex flex-col\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <p className=\"font-bold\" style={{ fontSize: `${fontSize}px` }}>\r\n                  <NoTranslate>{prefixStatements[statementIndex]}</NoTranslate>\r\n                </p>\r\n                <LatexRenderer text={statement.content} className=\"break-words\" style={{ fontSize: `${fontSize}px` }} />\r\n              </div>\r\n\r\n              <QuestionImage\r\n                imageUrl={statement.imageUrl}\r\n                imageSize={imageSize}\r\n                isStatement={true}\r\n                altText=\"statement image\"\r\n              />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MultipleChoiceQuestion;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,QAAQ,QAAQ,cAAc;AACvC,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,WAAW,MAAM,sBAAsB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,SAAAC,MAAA,IAAAC,OAAA;AAkBA,MAAMC,sBAAsB,GAAGC,IAAA,IAczB;EAAA,IAd0B;IAC9BC,QAAQ;IACRC,KAAK;IACLC,kBAAkB;IAClBC,UAAU;IACVC,gBAAgB;IAChBC,UAAU;IACVC,QAAQ;IACRC,SAAS;IACTC,gBAAgB;IAChBC,MAAM;IACNC,QAAQ;IACRC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3BC,kBAAkB,GAAGA,CAAA,KAAM,CAAE;EAC/B,CAAC,GAAAd,IAAA;EACC,MAAMe,QAAQ,GAAGH,eAAe,CAACI,GAAG,CAACf,QAAQ,CAACgB,EAAE,CAAC;EACjD,oBACEnB,OAAA;IAEEoB,GAAG,EAAGC,EAAE,IAAKT,MAAM,CAACT,QAAQ,CAACgB,EAAE,EAAEE,EAAE,CAAE;IACrC,oBAAkBlB,QAAQ,CAACgB,EAAG;IAC9BG,SAAS,6EAAAC,MAAA,CACLhB,gBAAgB,KAAKJ,QAAQ,CAACgB,EAAE,GAC9BX,UAAU,GACR,wCAAwC,GACxC,yCAAyC,GAC3C,EAAE,CAAG;IAAAgB,QAAA,gBAEXxB,OAAA;MAAKsB,SAAS,EAAC,kCAAkC;MAAAE,QAAA,gBAC/CxB,OAAA;QAAKsB,SAAS,EAAC,YAAY;QAAAE,QAAA,gBACzBxB,OAAA;UAAGsB,SAAS,EAAC,WAAW;UAACG,KAAK,EAAE;YAAEhB,QAAQ,KAAAc,MAAA,CAAKd,QAAQ;UAAK,CAAE;UAAAe,QAAA,eAC5DxB,OAAA,CAACF,WAAW;YAAA0B,QAAA,GAAC,SAAI,EAACpB,KAAK,GAAG,CAAC,EAAC,GAAC;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAKJ7B,OAAA,CAACH,YAAY;UAACiC,UAAU,EAAE3B,QAAQ,CAACgB;QAAG;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEN7B,OAAA;QACE+B,OAAO,EAAGC,CAAC,IAAK;UACdA,CAAC,CAACC,cAAc,CAAC,CAAC;UAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;UACnBlB,kBAAkB,CAACb,QAAQ,CAACgB,EAAE,CAAC;QACjC,CAAE;QACFG,SAAS,wCAAAC,MAAA,CAAwCN,QAAQ,GAClDT,UAAU,GAAG,gCAAgC,GAAG,gCAAgC,GAChFA,UAAU,GAAG,iCAAiC,GAAG,iCAAkC,CACrF;QACL2B,KAAK,EAAElB,QAAQ,GAAG,aAAa,GAAG,qBAAsB;QACxDmB,SAAS,EAAC,IAAI;QAAAZ,QAAA,eAEdxB,OAAA,CAACJ,QAAQ;UAACyC,IAAI,EAAE,EAAG;UAACC,IAAI,EAAErB,QAAQ,GAAG,cAAc,GAAG;QAAO;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN7B,OAAA,CAACN,aAAa;MAAC6C,IAAI,EAAEpC,QAAQ,CAACqC,OAAQ;MAAClB,SAAS,EAAC,EAAE;MAACG,KAAK,EAAE;QAAEhB,QAAQ,KAAAc,MAAA,CAAKd,QAAQ;MAAK;IAAE;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE5F7B,OAAA,CAACL,aAAa;MACZ8C,QAAQ,EAAEtC,QAAQ,CAACsC,QAAS;MAC5B/B,SAAS,EAAEA;IAAU;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAEF7B,OAAA;MAAKsB,SAAS,EAAC,qBAAqB;MAAAE,QAAA,EACjCrB,QAAQ,CAACuC,UAAU,CAACC,GAAG,CAAC,CAACC,SAAS,EAAEC,cAAc,kBACjD7C,OAAA;QAAwBsB,SAAS,EAAC,yBAAyB;QAAAE,QAAA,gBACzDxB,OAAA;UACE8C,IAAI,EAAC,OAAO;UACZC,IAAI,cAAAxB,MAAA,CAAcpB,QAAQ,CAACgB,EAAE,CAAG;UAChC6B,KAAK,EAAEJ,SAAS,CAACzB,EAAG;UACpB8B,OAAO,EAAE3C,UAAU,CAACH,QAAQ,CAACgB,EAAE,EAAEyB,SAAS,CAACzB,EAAE,CAAE;UAC/C+B,QAAQ,EAAEA,CAAA,KACR7C,kBAAkB,CAACF,QAAQ,CAACgB,EAAE,EAAEyB,SAAS,CAACzB,EAAE,EAAEhB,QAAQ,CAACgD,cAAc,CACtE;UACDC,QAAQ,EAAEvC,QAAS;UACnBS,SAAS,4BAAAC,MAAA,CAA4BV,QAAQ,GAAG,+BAA+B,GAAG,gBAAgB;QAAG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtG,CAAC,eAEF7B,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC5BxB,OAAA;YAAKsB,SAAS,EAAC,yBAAyB;YAAAE,QAAA,gBACtCxB,OAAA;cAAGsB,SAAS,EAAC,WAAW;cAACG,KAAK,EAAE;gBAAEhB,QAAQ,KAAAc,MAAA,CAAKd,QAAQ;cAAK,CAAE;cAAAe,QAAA,eAC5DxB,OAAA,CAACF,WAAW;gBAAA0B,QAAA,EAAEb,gBAAgB,CAACkC,cAAc;cAAC;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACJ7B,OAAA,CAACN,aAAa;cAAC6C,IAAI,EAAEK,SAAS,CAACJ,OAAQ;cAAClB,SAAS,EAAC,aAAa;cAACG,KAAK,EAAE;gBAAEhB,QAAQ,KAAAc,MAAA,CAAKd,QAAQ;cAAK;YAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC,eAEN7B,OAAA,CAACL,aAAa;YACZ8C,QAAQ,EAAEG,SAAS,CAACH,QAAS;YAC7B/B,SAAS,EAAEA,SAAU;YACrB2C,WAAW,EAAE,IAAK;YAClBC,OAAO,EAAC;UAAiB;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GA3BEe,SAAS,CAACzB,EAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4BjB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,GA9ED1B,QAAQ,CAACgB,EAAE,GAAG,IAAI;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA+EpB,CAAC;AAEV,CAAC;AAAC0B,EAAA,GAnGItD,sBAAsB;AAqG5B,eAAeA,sBAAsB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}