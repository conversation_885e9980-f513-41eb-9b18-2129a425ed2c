{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ToanThayBee\\\\ToanThayBeeFrontendReactjs\\\\src\\\\pages\\\\user\\\\practice\\\\DoExamPage.jsx\",\n  _s = $RefreshSig$();\nimport HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useState, useEffect, useRef, useCallback } from \"react\";\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\nimport { useParams } from \"react-router-dom\";\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\nimport { useNavigate } from \"react-router-dom\";\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\nimport { AnimatePresence } from \"framer-motion\";\nimport { Menu } from \"lucide-react\";\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\nimport ExamContent from \"../../../components/questions/ExamContent\";\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\nimport { setRemainingTime, summitExam, setSaveQuestions, setErrorQuestions, getRemainingTime, logUserActivity, submitAnswerWithAttempt, leaveExam } from \"../../../features/doExam/doExamSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DoExamPage = () => {\n  _s();\n  var _examContentRef$curre;\n  const {\n    examId\n  } = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    exam\n  } = useSelector(state => state.exams);\n  const {\n    questions\n  } = useSelector(state => state.questions);\n  const {\n    answers\n  } = useSelector(state => state.answers);\n  const [fontSize, setFontSize] = useState(14); // 14px mặc định\n  const [imageSize, setImageSize] = useState(12); // đơn vị: rem\n  const questionRefs = useRef([]);\n  const [isAgree, setIsAgree] = useState(false);\n  const [attemptId, setAttemptId] = useState(null);\n  const attemptRef = useRef(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [flag, setFlag] = useState(false);\n  const [startTime1, setStartTime1] = useState(null);\n  const hasSubmittedRef = useRef(false);\n  const examRef = useRef(null);\n  const examContentRef = useRef(null);\n  useEffect(() => {\n    examRef.current = exam;\n    if ((exam === null || exam === void 0 ? void 0 : exam.acceptDoExam) === false) {\n      navigate(\"/practice/exam/\".concat(examId));\n    }\n  }, [exam]);\n  useEffect(() => {\n    if (examId) {\n      dispatch(fetchPublicExamById(examId));\n    }\n  }, [dispatch, examId]);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const {\n    remainingTime,\n    saveQuestions,\n    errorQuestions\n  } = useSelector(state => state.doExam);\n  const [markedQuestions, setMarkedQuestions] = useState(new Set());\n  const [timeWarningShown, setTimeWarningShown] = useState({\n    fiveMinutes: false,\n    oneMinute: false\n  });\n  const [isTimeBlinking, setIsTimeBlinking] = useState(false);\n  const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\n  const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    const saved = localStorage.getItem(\"isDarkMode\");\n    return saved ? JSON.parse(saved) : false;\n  });\n  const [loadingSubmit, setLoadingSubmit] = useState(false);\n  const [loadingLoadExam, setLoadingLoadExam] = useState(false);\n  const [isTimeUp, setIsTimeUp] = useState(false);\n  const [questionTN, setQuestionTN] = useState([]);\n  const [questionDS, setQuestionDS] = useState([]);\n  const [questionTLN, setQuestionTLN] = useState([]);\n  const [answerTN, setAnswerTN] = useState([]);\n  const [answerTLN, setAnswerTLN] = useState([]);\n  const [dsAnswers, setDsAnswers] = useState({});\n  document.addEventListener(\"copy\", e => {\n    e.preventDefault();\n  });\n  const addQuestion = questionId => {\n    const newSet = new Set(saveQuestions);\n    newSet.add(questionId);\n    dispatch(setSaveQuestions(Array.from(newSet))); // convert to array before saving\n    removeErrorQuestion(questionId);\n  };\n  const addErrorQuestion = questionId => {\n    const newSet = new Set(errorQuestions);\n    newSet.add(questionId);\n    dispatch(setErrorQuestions(Array.from(newSet)));\n    removeQuestion(questionId);\n  };\n  const removeQuestion = questionId => {\n    const newSet = new Set(saveQuestions);\n    newSet.delete(questionId);\n    dispatch(setSaveQuestions(Array.from(newSet)));\n  };\n  const removeErrorQuestion = questionId => {\n    const newSet = new Set(errorQuestions);\n    newSet.delete(questionId);\n    dispatch(setErrorQuestions(Array.from(newSet)));\n  };\n\n  // Hàm đánh dấu câu hỏi để xem lại sau\n  const toggleMarkQuestion = questionId => {\n    setMarkedQuestions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(questionId)) {\n        newSet.delete(questionId);\n      } else {\n        newSet.add(questionId);\n      }\n      return newSet;\n    });\n  };\n  const handleExitFullscreen = () => {\n    try {\n      exitFullscreen();\n    } catch (err) {\n      // Chỉ ghi log lỗi, không bắt lỗi\n      console.warn(\"Không thể thoát fullscreen:\", err);\n    }\n  };\n  const handleFontSizeChange = e => {\n    setFontSize(Number(e.target.value));\n  };\n  const handleImageSizeChange = e => {\n    setImageSize(Number(e.target.value));\n  };\n  const formatTime = seconds => {\n    const min = String(Math.floor(seconds / 60)).padStart(2, '0');\n    const sec = String(seconds % 60).padStart(2, '0');\n    return \"\".concat(min, \":\").concat(sec);\n  };\n  const handleFullScreen = async () => {\n    setLoadingLoadExam(true);\n    const startExam = () => {\n      socket.emit(\"join_exam\", {\n        studentId: user.id,\n        examId\n      });\n      console.log(\"📨 Đã gửi yêu cầu vào thi\");\n    };\n    if (!socket.connected) {\n      console.log(\"⚠️ Socket chưa kết nối. Đang kết nối...\");\n      socket.connect();\n\n      // Chờ socket connect xong mới gửi\n      socket.once(\"connect\", () => {\n        console.log(\"✅ Socket đã kết nối!\");\n        startExam();\n      });\n      socket.once(\"exam_error\", _ref => {\n        let {\n          message\n        } = _ref;\n        dispatch(setErrorMessage(\"Lỗi: \" + message));\n        setLoadingLoadExam(false);\n        navigate(\"/practice/exam/\".concat(examId));\n        return;\n      });\n\n      // Trường hợp connect fail trong 5 giây → timeout\n      setTimeout(() => {\n        if (!socket.connected) {\n          setLoadingLoadExam(false);\n        }\n      }, 5000);\n    } else {\n      startExam();\n    }\n\n    // Lắng nghe lỗi\n    socket.once(\"exam_error\", _ref2 => {\n      let {\n        message\n      } = _ref2;\n      dispatch(setErrorMessage(\"Lỗi: \" + message));\n      setLoadingLoadExam(false);\n    });\n  };\n  useEffect(() => {\n    const handleExamStarted = async _ref3 => {\n      let {\n        attemptId,\n        startTime\n      } = _ref3;\n      console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\n      setIsAgree(true);\n      attemptRef.current = attemptId;\n      setAttemptId(attemptId);\n      if (examId) {\n        dispatch(fetchPublicQuestionsByExamId(examId));\n      }\n      setStartTime1(startTime);\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) {\n        setLoadingLoadExam(false);\n        return;\n      }\n      try {\n        const success = await requestFullscreen();\n        if (success) {\n          setTimeout(() => {\n            setLoadingLoadExam(false);\n          }, 800);\n        } else {\n          // Nếu không thể vào fullscreen, vẫn cho phép làm bài\n          console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\n          setLoadingLoadExam(false);\n        }\n      } catch (err) {\n        console.error(\"❌ Lỗi khi bật fullscreen:\", err);\n        // Vẫn cho phép làm bài ngay cả khi không thể vào fullscreen\n        alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\n        setLoadingLoadExam(false);\n      }\n    };\n    socket.on(\"exam_started\", handleExamStarted);\n    return () => {\n      socket.off(\"exam_started\", handleExamStarted);\n    };\n  }, [examId, dispatch, socket]);\n  useEffect(() => {\n    if (exam !== null && exam !== void 0 && exam.testDuration && startTime1) {\n      const start = new Date(startTime1);\n      const now = new Date();\n      const elapsedSeconds = Math.floor((now - start) / 1000);\n      const totalSeconds = exam.testDuration * 60;\n      const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\n      // console.log(start, now, remaining)\n      dispatch(setRemainingTime(remaining));\n\n      // Yêu cầu thời gian từ server khi bắt đầu\n      if (socket.connected && attemptId) {\n        socket.emit(\"request_time\", {\n          examId,\n          attemptId\n        });\n        console.log(\"Đã gửi yêu cầu thời gian từ server\");\n      }\n    }\n  }, [startTime1, exam, socket.connected, attemptId, examId]);\n  useEffect(() => {\n    if (flag) return;\n    if (!remainingTime) setFlag(true);\n  }, [remainingTime]);\n  const handleAutoSubmit = async () => {\n    if (hasSubmittedRef.current) {\n      console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\n      return;\n    }\n    hasSubmittedRef.current = true; // Đánh dấu đã submit\n    console.log(\"Kiểm tra attemptId:\", attemptId);\n    if (!attemptId) {\n      console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\n      return;\n    }\n    console.log(\"Đang nộp bài với attemptId:\", attemptId);\n    dispatch(setSaveQuestions(new Set()));\n    setLoadingSubmit(true);\n    try {\n      // Sử dụng API thay vì socket để nộp bài\n      const result = await dispatch(summitExam(attemptId)).unwrap();\n      console.log(\"Nộp bài thành công:\", result);\n\n      // Xử lý khi nộp bài thành công\n      dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\n\n      // Thoát fullscreen mà không bắt lỗi\n      try {\n        exitFullscreen();\n      } catch (err) {\n        // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\n        console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\n      }\n      const safeAttemptId = attemptRef.current;\n      const currentExam = examRef.current;\n      if (!safeAttemptId) {\n        console.error(\"Không có attemptId khi navigate!\");\n        return;\n      }\n\n      // Log để debug\n      console.log(\"Current exam state:\", currentExam);\n      console.log(\"Attempt ID:\", safeAttemptId);\n      if (!currentExam || !currentExam.seeCorrectAnswer) {\n        console.log(\"Chuyển về trang danh sách do:\", {\n          examNull: !currentExam,\n          cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\n        });\n        navigate(\"/practice/exam/\".concat(examId));\n        return;\n      }\n      navigate(\"/practice/exam/attempt/\".concat(safeAttemptId, \"/score\"));\n    } catch (error) {\n      console.error(\"Lỗi khi nộp bài:\", error);\n      setLoadingSubmit(false);\n      dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\n      hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\n\n      // Thử nộp lại sau 3 giây nếu lỗi xảy ra\n      setTimeout(() => {\n        if (!loadingSubmit && attemptRef.current) {\n          console.log(\"Thử nộp bài lại sau lỗi...\");\n          handleAutoSubmit();\n        }\n      }, 5000);\n    }\n  };\n\n  // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\n  const navigateToQuestion = useCallback(questionId => {\n    setSelectedQuestion(questionId);\n\n    // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\n    if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\n      // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\n      examContentRef.current.goToQuestionById(questionId);\n    } else {\n      // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\n      // Tìm phần tử câu hỏi bằng querySelector\n      setTimeout(() => {\n        // Thử tìm phần tử bằng data-question-id\n        const element = document.querySelector(\"[data-question-id=\\\"\".concat(questionId, \"\\\"]\"));\n        if (element) {\n          const offset = 80; // chiều cao của header sticky\n          const y = element.getBoundingClientRect().top + window.scrollY - offset;\n          window.scrollTo({\n            top: y,\n            behavior: \"smooth\"\n          });\n        } else {\n          // Fallback: Sử dụng refs\n          const refElement = questionRefs.current[questionId];\n          if (refElement) {\n            const offset = 80; // chiều cao của header sticky\n            const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\n            window.scrollTo({\n              top: y,\n              behavior: \"smooth\"\n            });\n          }\n        }\n      }, 0);\n    }\n  }, [questionRefs, examContentRef]);\n\n  // Alias cho navigateToQuestion để tương thích với các component khác\n  const scrollToQuestion = navigateToQuestion;\n  const handleSelectAnswerTN = (questionId, statementId, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const payload = {\n      attemptId,\n      questionId,\n      answerContent: statementId,\n      studentId: user.id,\n      // nếu cần xác định user\n      type,\n      examId,\n      name: user.lastName + \" \" + user.firstName\n    };\n    const newAnswer = {\n      questionId,\n      answerContent: statementId,\n      typeOfQuestion: type\n    };\n    dispatch(setAnswers(newAnswer));\n    socket.emit(\"select_answer\", payload);\n  };\n  const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    const currentAnswers = dsAnswers[questionId] || [];\n    const existing = currentAnswers.find(ans => ans.statementId === statementId);\n\n    // 🔁 Nếu đáp án đã giống thì không gửi lại\n    if (existing && existing.answer === selectedAnswer) {\n      return;\n    }\n    const updatedAnswers = currentAnswers.map(ans => ans.statementId === statementId ? {\n      ...ans,\n      answer: selectedAnswer\n    } : ans);\n\n    // Nếu chưa có statement này\n    if (!existing) {\n      updatedAnswers.push({\n        statementId,\n        answer: selectedAnswer\n      });\n    }\n\n    // ✨ Gửi toàn bộ lên server\n    socket.emit(\"select_answer\", {\n      questionId,\n      answerContent: updatedAnswers,\n      studentId: user.id,\n      attemptId,\n      type: \"DS\",\n      examId,\n      name: user.lastName + \" \" + user.firstName\n    });\n    dispatch(setAnswers({\n      questionId,\n      answerContent: JSON.stringify(updatedAnswers),\n      typeOfQuestion: \"DS\"\n    }));\n  };\n  const handleSelectAnswerTLN = (questionId, answerContent, type) => {\n    // Không cho phép làm bài nếu đã hết thời gian\n    if (isTimeUp) {\n      dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\n      return;\n    }\n    if (!answerContent || answerContent.trim() === \"\") {\n      return;\n    }\n    const payload = {\n      attemptId,\n      questionId,\n      answerContent: answerContent.trim().replace(\",\", \".\"),\n      studentId: user.id,\n      type,\n      examId,\n      name: user.lastName + \" \" + user.firstName\n    };\n    dispatch(setAnswers({\n      questionId,\n      answerContent,\n      typeOfQuestion: type\n    }));\n    socket.emit(\"select_answer\", payload);\n  };\n\n  // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\n  const questionsToMarkAsSaved = useRef(new Set());\n\n  // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\n  useEffect(() => {\n    if (questionsToMarkAsSaved.current.size > 0) {\n      questionsToMarkAsSaved.current.forEach(questionId => {\n        if (!saveQuestions.has(questionId)) {\n          addQuestion(questionId);\n        }\n      });\n      questionsToMarkAsSaved.current.clear();\n    }\n  }, [saveQuestions, addQuestion]);\n\n  // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\n  useEffect(() => {\n    // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\n    const frameId = requestAnimationFrame(() => {\n      if (questionsToMarkAsSaved.current.size > 0) {\n        const questionIds = [...questionsToMarkAsSaved.current];\n        questionsToMarkAsSaved.current.clear();\n\n        // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\n        questionIds.forEach(questionId => {\n          if (!saveQuestions.has(questionId)) {\n            addQuestion(questionId);\n          }\n        });\n      }\n    });\n    return () => cancelAnimationFrame(frameId);\n  });\n  const isTNSelected = useCallback((questionId, statementId) => {\n    const isSelected = answerTN.some(ans => ans.questionId === questionId && ans.answerContent && String(ans.answerContent) === String(statementId));\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.has(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [answerTN, saveQuestions]);\n  const isDSChecked = useCallback((questionId, statementId, bool) => {\n    var _dsAnswers$questionId, _dsAnswers$questionId2;\n    const isSelected = ((_dsAnswers$questionId = dsAnswers[questionId]) === null || _dsAnswers$questionId === void 0 ? void 0 : _dsAnswers$questionId.some(a => a.statementId === statementId && a.answer === bool)) || false;\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (isSelected && !saveQuestions.has(questionId) && ((_dsAnswers$questionId2 = dsAnswers[questionId]) === null || _dsAnswers$questionId2 === void 0 ? void 0 : _dsAnswers$questionId2.length) === 4) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return isSelected;\n  }, [dsAnswers, saveQuestions]);\n  const getTLNDefaultValue = useCallback(questionId => {\n    var _matched$answerConten;\n    const matched = answerTLN.find(ans => ans.questionId === questionId);\n    const content = (matched === null || matched === void 0 ? void 0 : (_matched$answerConten = matched.answerContent) === null || _matched$answerConten === void 0 ? void 0 : _matched$answerConten.replace(/^\"|\"$/g, \"\")) || \"\";\n\n    // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\n    if (content && !saveQuestions.has(questionId)) {\n      questionsToMarkAsSaved.current.add(questionId);\n    }\n    return content;\n  }, [answerTLN, saveQuestions]);\n\n  // useEffect(() => {\n  //     if (examId) {\n  //         dispatch(fetchPublicQuestionsByExamId(examId));\n  //     }\n  // }, [dispatch, examId]);\n\n  useEffect(() => {\n    if (questions) {\n      setQuestionTN(questions.filter(question => question.typeOfQuestion === \"TN\"));\n      setQuestionDS(questions.filter(question => question.typeOfQuestion === \"DS\"));\n      setQuestionTLN(questions.filter(question => question.typeOfQuestion === \"TLN\"));\n    }\n  }, [questions]);\n  useEffect(() => {\n    // Kiểm tra answers có phải là mảng không\n    if (!Array.isArray(answers) || answers.length === 0) return;\n    const tn = [];\n    const tln = [];\n    const dsMap = {};\n\n    // Sử dụng for...of thay vì forEach để tránh lỗi\n    for (const answer of answers) {\n      if (answer.typeOfQuestion === \"TN\") {\n        tn.push(answer);\n      } else if (answer.typeOfQuestion === \"TLN\") {\n        tln.push(answer);\n      } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\n        try {\n          const parsed = JSON.parse(answer.answerContent);\n          dsMap[answer.questionId] = parsed;\n        } catch (err) {\n          console.error(\"Lỗi parse DS answerContent:\", err);\n        }\n      }\n    }\n    setAnswerTN(tn);\n    setAnswerTLN(tln);\n    setDsAnswers(dsMap);\n    if (!socket || !socket.connected || !attemptId || !examId) return;\n    socket.emit(\"calculate_score\", {\n      attemptId,\n      answers,\n      examId,\n      student: user\n    });\n  }, [answers]);\n  useEffect(() => {\n    if (attemptId) {\n      dispatch(fetchAnswersByAttempt(attemptId));\n    }\n  }, [dispatch, attemptId]);\n  useEffect(() => {\n    if (!(exam !== null && exam !== void 0 && exam.testDuration) || remainingTime === null || !isAgree) return;\n\n    // Kiểm tra và hiển thị cảnh báo thời gian\n    const checkTimeWarnings = time => {\n      // Cảnh báo khi còn 5 phút\n      if (time === 300 && !timeWarningShown.fiveMinutes) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          fiveMinutes: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\n\n        // Tắt hiệu ứng nhấp nháy sau 10 giây\n        setTimeout(() => {\n          setIsTimeBlinking(false);\n        }, 10000);\n      }\n\n      // Cảnh báo khi còn 1 phút\n      if (time === 60 && !timeWarningShown.oneMinute) {\n        setTimeWarningShown(prev => ({\n          ...prev,\n          oneMinute: true\n        }));\n        setIsTimeBlinking(true);\n        dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\n\n        // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\n      }\n    };\n\n    // Định kỳ yêu cầu thời gian từ server để đồng bộ\n    const syncTimeInterval = setInterval(() => {\n      if (socket.connected && attemptId) {\n        socket.emit(\"request_time\", {\n          examId,\n          attemptId\n        });\n      }\n    }, 30000); // Đồng bộ thời gian mỗi 30 giây\n\n    const interval = setInterval(() => {\n      dispatch(setRemainingTime(prev => {\n        if (prev <= 1) {\n          // dùng <=1 để đảm bảo không bị âm\n          clearInterval(interval);\n          clearInterval(syncTimeInterval);\n          // Đánh dấu là đã hết thời gian\n          setIsTimeUp(true);\n          setIsTimeBlinking(false);\n          // Thử nộp bài\n          handleAutoSubmit();\n          return 0;\n        }\n\n        // Kiểm tra cảnh báo thời gian\n        checkTimeWarnings(prev);\n        return prev - 1;\n      }));\n    }, 1000);\n    return () => {\n      clearInterval(interval);\n      clearInterval(syncTimeInterval);\n    };\n  }, [exam === null || exam === void 0 ? void 0 : exam.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, socket, attemptId, examId]); // Chỉ phụ thuộc vào các giá trị cần thiết\n\n  useEffect(() => {\n    if (isAgree && !socket.connected) {\n      socket.connect();\n    }\n    return () => {\n      socket.disconnect();\n    };\n  }, [isAgree]);\n\n  // frontend\n  useEffect(() => {\n    if (!attemptId || !(user !== null && user !== void 0 && user.id) || !examId || attemptId === null || attemptId === undefined) return;\n    if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled)) return;\n    console.log(\"Đã bật theo dõi hành vi gian lận\");\n    const recentLogs = new Set(); // chống log lặp\n    const logOnce = (key, payload) => {\n      if (!(exam !== null && exam !== void 0 && exam.isCheatingCheckEnabled) || recentLogs.has(key)) return;\n      recentLogs.add(key);\n      socket.emit(\"user_log\", {\n        ...payload,\n        name: user.lastName + \" \" + user.firstName\n      });\n      setTimeout(() => recentLogs.delete(key), 5000);\n    };\n\n    // 📌 Thoát fullscreen\n    const handleFullscreenChange = () => {\n      if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {\n        logOnce(\"exit_fullscreen\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"EF\",\n          action: \"exit_fullscreen\",\n          detail: JSON.stringify({\n            reason: \"User exited fullscreen mode\"\n          })\n        });\n      }\n    };\n\n    // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === \"hidden\") {\n        logOnce(\"tab_blur\", {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"TB\",\n          action: \"tab_blur\",\n          detail: JSON.stringify({\n            message: \"User switched tab or minimized window\"\n          })\n        });\n      }\n    };\n\n    // 📌 Copy nội dung\n    const handleCopy = () => {\n      logOnce(\"copy_detected\", {\n        studentId: user.id,\n        attemptId,\n        examId,\n        code: \"COP\",\n        action: \"copy_detected\",\n        detail: JSON.stringify({\n          message: \"User copied content\"\n        })\n      });\n    };\n\n    // 📌 Phím đáng ngờ\n    const handleSuspiciousKey = e => {\n      const suspiciousKeys = [\"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"];\n      const combo = \"\".concat(e.ctrlKey ? \"Ctrl+\" : \"\").concat(e.shiftKey ? \"Shift+\" : \"\").concat(e.altKey ? \"Alt+\" : \"\").concat(e.metaKey ? \"Meta+\" : \"\").concat(e.key);\n      if (suspiciousKeys.includes(e.key) || combo === \"Ctrl+Shift+I\" || combo === \"Ctrl+Shift+C\") {\n        logOnce(\"key_\".concat(combo), {\n          studentId: user.id,\n          attemptId,\n          examId,\n          code: \"SK\",\n          action: \"suspicious_key\",\n          detail: JSON.stringify({\n            key: e.key,\n            code: e.code,\n            combo\n          })\n        });\n      }\n    };\n    document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n    document.addEventListener(\"copy\", handleCopy);\n    document.addEventListener(\"keydown\", handleSuspiciousKey);\n    return () => {\n      document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      document.removeEventListener(\"copy\", handleCopy);\n      document.removeEventListener(\"keydown\", handleSuspiciousKey);\n    };\n  }, [socket, user.id, examId, attemptId]);\n  useEffect(() => {\n    // Chỉ lắng nghe các sự kiện liên quan đến câu trả lời\n    const handleAnswerSaved = _ref4 => {\n      let {\n        questionId\n      } = _ref4;\n      addQuestion(questionId);\n      removeErrorQuestion(questionId);\n    };\n    const handleAnswerError = _ref5 => {\n      let {\n        questionId,\n        message\n      } = _ref5;\n      dispatch(setErrorMessage(message));\n      removeQuestion(questionId);\n      addErrorQuestion(questionId);\n    };\n\n    // Lắng nghe sự kiện cập nhật thời gian từ server\n    const handleExamTimer = _ref6 => {\n      let {\n        remainingTime: serverRemainingTime\n      } = _ref6;\n      console.log(\"Nhận thời gian từ server:\", serverRemainingTime);\n      dispatch(setRemainingTime(serverRemainingTime));\n    };\n\n    // Lắng nghe sự kiện bài thi tự động nộp\n    const handleExamAutoSubmitted = _ref7 => {\n      let {\n        message,\n        attemptId: autoSubmitAttemptId,\n        score\n      } = _ref7;\n      console.log(\"Bài thi đã tự động nộp:\", {\n        message,\n        autoSubmitAttemptId,\n        score\n      });\n      dispatch(setSuccessMessage(message));\n      setIsTimeUp(true);\n\n      // Thoát fullscreen\n      try {\n        exitFullscreen();\n      } catch (err) {\n        console.warn(\"Không thể thoát fullscreen khi bài thi tự động nộp:\", err);\n      }\n\n      // Chuyển hướng đến trang kết quả nếu được phép xem đáp án\n      if (exam !== null && exam !== void 0 && exam.seeCorrectAnswer) {\n        navigate(\"/practice/exam/attempt/\".concat(autoSubmitAttemptId, \"/score\"));\n      } else {\n        navigate(\"/practice/exam/\".concat(examId));\n      }\n    };\n\n    // Lắng nghe thông báo từ giáo viên hoặc hệ thống\n    const handleExamNotification = _ref8 => {\n      let {\n        message\n      } = _ref8;\n      console.log(\"Nhận thông báo:\", message);\n      dispatch(setSuccessMessage(message));\n    };\n\n    // Đăng ký các event listeners\n    socket.on(\"answer_saved\", handleAnswerSaved);\n    socket.on(\"answer_error\", handleAnswerError);\n    socket.on(\"exam_timer\", handleExamTimer);\n    socket.on(\"exam_auto_submitted\", handleExamAutoSubmitted);\n    socket.on(\"exam_notification\", handleExamNotification);\n    return () => {\n      // Hủy đăng ký các event listeners\n      socket.off(\"answer_saved\", handleAnswerSaved);\n      socket.off(\"answer_error\", handleAnswerError);\n      socket.off(\"exam_timer\", handleExamTimer);\n      socket.off(\"exam_auto_submitted\", handleExamAutoSubmitted);\n      socket.off(\"exam_notification\", handleExamNotification);\n    };\n  }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\n  useEffect(() => {\n    localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\n  }, [isDarkMode]);\n\n  // Hàm xử lý chuyển đổi câu hỏi\n  const handleKeyDown = useCallback(e => {\n    // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\n    if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\n      // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\n      e.preventDefault();\n\n      // Nếu không có câu hỏi, thoát khỏi hàm\n      if (!questions || questions.length === 0) return;\n      const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\n      const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\n      if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\n        const prevQuestionId = allQuestions[currentIndex - 1].id;\n        console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\n        navigateToQuestion(prevQuestionId);\n      } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\n        const nextQuestionId = allQuestions[currentIndex + 1].id;\n        console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\n        navigateToQuestion(nextQuestionId);\n      }\n    }\n  }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\n  // Lắng nghe sự kiện bàn phím\n  useEffect(() => {\n    document.addEventListener(\"keydown\", handleKeyDown);\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [handleKeyDown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full \".concat(isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'),\n    children: [/*#__PURE__*/_jsxDEV(HeaderDoExamPage, {\n      nameExam: exam === null || exam === void 0 ? void 0 : exam.name,\n      onExitFullscreen: handleExitFullscreen,\n      isDarkMode: !isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 868,\n      columnNumber: 13\n    }, this), isAgree ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\",\n      children: [/*#__PURE__*/_jsxDEV(ExamContent, {\n        ref: examContentRef,\n        loading1: loadingLoadExam,\n        isDarkMode: isDarkMode,\n        questionTN: questionTN,\n        questionDS: questionDS,\n        questionTLN: questionTLN,\n        handlers: {\n          handleSelectAnswerTN,\n          handleSelectAnswerDS,\n          handleSelectAnswerTLN,\n          isTNSelected,\n          isDSChecked,\n          getTLNDefaultValue,\n          setQuestionRef: (id, el) => questionRefs.current[id] = el,\n          setSelectedQuestion: id => setSelectedQuestion(id)\n        },\n        settings: {\n          selectedQuestion,\n          isDarkMode,\n          fontSize,\n          imageSize,\n          prefixStatementTN,\n          prefixStatementDS,\n          isTimeUp,\n          markedQuestions,\n          toggleMarkQuestion\n        },\n        isTimeUp: isTimeUp\n        // Để undefined để component tự quyết định dựa trên thiết bị\n        ,\n        initialSingleMode: undefined,\n        handleAutoSubmit: handleAutoSubmit,\n        loadingSubmit: loadingSubmit\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 rounded-full shadow-md \".concat(isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"),\n          onClick: () => setIsSidebarOpen(prev => !prev),\n          children: /*#__PURE__*/_jsxDEV(Menu, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 910,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 909,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: (isSidebarOpen || window.innerWidth > 1024) && /*#__PURE__*/_jsxDEV(ExamSidebar, {\n          isDarkMode: isDarkMode,\n          setIsDarkMode: setIsDarkMode,\n          fontSize: fontSize,\n          handleFontSizeChange: handleFontSizeChange,\n          imageSize: imageSize,\n          handleImageSizeChange: handleImageSizeChange,\n          questionTN: questionTN,\n          questionDS: questionDS,\n          questionTLN: questionTLN,\n          scrollToQuestion: navigateToQuestion,\n          selectedQuestion: selectedQuestion,\n          markedQuestions: markedQuestions,\n          toggleMarkQuestion: toggleMarkQuestion,\n          handleAutoSubmit: handleAutoSubmit,\n          loadingSubmit: loadingSubmit,\n          loadingLoadExam: loadingLoadExam,\n          exam: exam,\n          remainingTime: remainingTime,\n          formatTime: formatTime,\n          questions: questions,\n          singleQuestionMode: ((_examContentRef$curre = examContentRef.current) === null || _examContentRef$curre === void 0 ? void 0 : _examContentRef$curre.isSingleQuestionMode()) || false,\n          setSingleQuestionMode: value => {\n            if (examContentRef.current) {\n              // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\n              examContentRef.current.setSingleQuestionMode(value);\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 919,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 870,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(ExamRegulationModal, {\n        onClose: () => {\n          if (socket.connected) {\n            socket.emit(\"leave_exam\", {\n              studentId: user === null || user === void 0 ? void 0 : user.id,\n              examId\n            });\n            socket.disconnect();\n          }\n          socket.removeAllListeners(); // Xóa hết listener để tránh lỗi khi component bị unmount\n          navigate(\"/practice/exam/\".concat(examId));\n        },\n        isOpen: !isAgree,\n        onStartExam: handleFullScreen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 955,\n      columnNumber: 17\n    }, this), (exam === null || exam === void 0 ? void 0 : exam.testDuration) && isAgree && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-2 rounded-md left-2 px-4 py-2\\n                    \".concat(isTimeBlinking ? 'bg-red-600 animate-pulse' : 'bg-slate-700 bg-opacity-80', \"\\n                    text-white z-50 transition-colors duration-300\"),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-bold\",\n          children: [formatTime(remainingTime), \" ph\\xFAt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 972,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 867,\n    columnNumber: 9\n  }, this);\n};\n_s(DoExamPage, \"4V6qRN3gRKLreKGbPiNpJzWq1rU=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DoExamPage;\nexport default DoExamPage;\nvar _c;\n$RefreshReg$(_c, \"DoExamPage\");", "map": {"version": 3, "names": ["HeaderDoExamPage", "useDispatch", "useSelector", "useState", "useEffect", "useRef", "useCallback", "fetchPublicQuestionsByExamId", "fetchPublicExamById", "useParams", "setErrorMessage", "setSuccessMessage", "useNavigate", "fetchAnswersByAttempt", "setAnswers", "ExamRegulationModal", "AnimatePresence", "<PERSON><PERSON>", "ExamSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestFullscreen", "exitFullscreen", "isFullscreen", "setRemainingTime", "summitExam", "setSaveQuestions", "setErrorQuestions", "getRemainingTime", "logUserActivity", "submitAnswerWithAttempt", "leaveExam", "jsxDEV", "_jsxDEV", "DoExamPage", "_s", "_examContentRef$curre", "examId", "dispatch", "navigate", "exam", "state", "exams", "questions", "answers", "fontSize", "setFontSize", "imageSize", "setImageSize", "questionRefs", "isAgree", "setIsAgree", "attemptId", "setAttemptId", "attemptRef", "isSidebarOpen", "setIsSidebarOpen", "flag", "setFlag", "startTime1", "setStartTime1", "hasSubmittedRef", "examRef", "examContentRef", "current", "acceptDoExam", "concat", "user", "auth", "remainingTime", "saveQuestions", "errorQuestions", "doExam", "markedQuestions", "setMarkedQuestions", "Set", "timeWarningShown", "setTimeWarningShown", "fiveMinutes", "oneMinute", "isTimeBlinking", "setIsTimeBlinking", "prefixStatementTN", "prefixStatementDS", "selectedQuestion", "setSelectedQuestion", "isDarkMode", "setIsDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "loadingSubmit", "setLoadingSubmit", "loadingLoadExam", "setLoadingLoadExam", "isTimeUp", "setIsTimeUp", "questionTN", "setQuestionTN", "questionDS", "setQuestionDS", "questionTLN", "setQuestionTLN", "answerTN", "setAnswerTN", "answerTLN", "setAnswerTLN", "dsAnswers", "setDsAnswers", "document", "addEventListener", "e", "preventDefault", "addQuestion", "questionId", "newSet", "add", "Array", "from", "removeErrorQuestion", "addErrorQuestion", "removeQuestion", "delete", "toggleMarkQuestion", "prev", "has", "handleExitFullscreen", "err", "console", "warn", "handleFontSizeChange", "Number", "target", "value", "handleImageSizeChange", "formatTime", "seconds", "min", "String", "Math", "floor", "padStart", "sec", "handleFullScreen", "startExam", "socket", "emit", "studentId", "id", "log", "connected", "connect", "once", "_ref", "message", "setTimeout", "_ref2", "handleExamStarted", "_ref3", "startTime", "isCheatingCheckEnabled", "success", "error", "alert", "on", "off", "testDuration", "start", "Date", "now", "elapsedSeconds", "totalSeconds", "remaining", "max", "handleAutoSubmit", "result", "unwrap", "safeAttemptId", "currentExam", "seeCorrectAnswer", "examNull", "cantSeeAnswer", "navigateToQuestion", "isSingleQuestionMode", "goToQuestionById", "element", "querySelector", "offset", "y", "getBoundingClientRect", "top", "window", "scrollY", "scrollTo", "behavior", "refElement", "scrollToQuestion", "handleSelectAnswerTN", "statementId", "type", "payload", "answerContent", "name", "lastName", "firstName", "newAnswer", "typeOfQuestion", "handleSelectAnswerDS", "<PERSON><PERSON><PERSON><PERSON>", "currentAnswers", "existing", "find", "ans", "answer", "updatedAnswers", "map", "push", "stringify", "handleSelectAnswerTLN", "trim", "replace", "questionsToMarkAsSaved", "size", "for<PERSON>ach", "clear", "frameId", "requestAnimationFrame", "questionIds", "cancelAnimationFrame", "isTNSelected", "isSelected", "some", "isDSChecked", "bool", "_dsAnswers$questionId", "_dsAnswers$questionId2", "a", "length", "getTLNDefaultValue", "_matched$answerConten", "matched", "content", "filter", "question", "isArray", "tn", "tln", "dsMap", "parsed", "student", "checkTimeWarnings", "time", "syncTimeInterval", "setInterval", "interval", "clearInterval", "disconnect", "undefined", "recentLogs", "logOnce", "key", "handleFullscreenChange", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "code", "action", "detail", "reason", "handleVisibilityChange", "visibilityState", "handleCopy", "handleSuspiciousKey", "<PERSON><PERSON><PERSON><PERSON>", "combo", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "includes", "removeEventListener", "handleAnswerSaved", "_ref4", "handleAnswerError", "_ref5", "handleExamTimer", "_ref6", "serverRemainingTime", "handleExamAutoSubmitted", "_ref7", "autoSubmitAttemptId", "score", "handleExamNotification", "_ref8", "setItem", "handleKeyDown", "allQuestions", "currentIndex", "findIndex", "q", "prevQuestionId", "nextQuestionId", "className", "children", "nameExam", "onExitFullscreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "loading1", "handlers", "setQuestionRef", "el", "settings", "initialSingleMode", "onClick", "innerWidth", "singleQuestionMode", "setSingleQuestionMode", "onClose", "removeAllListeners", "isOpen", "onStartExam", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ToanThayBee/ToanThayBeeFrontendReactjs/src/pages/user/practice/DoExamPage.jsx"], "sourcesContent": ["import HeaderDoExamPage from \"../../../components/header/HeaderDoExamPage\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { fetchPublicQuestionsByExamId } from \"../../../features/question/questionSlice\";\r\nimport { fetchPublicExamById } from \"../../../features/exam/examSlice\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { setErrorMessage, setSuccessMessage } from \"../../../features/state/stateApiSlice\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { fetchAnswersByAttempt, setAnswers } from \"../../../features/answer/answerSlice\";\r\nimport ExamRegulationModal from \"../../../components/modal/ExamRegulationModal\";\r\nimport { AnimatePresence } from \"framer-motion\";\r\nimport { Menu } from \"lucide-react\";\r\nimport ExamSidebar from \"../../../components/sidebar/ExamSidebar\";\r\nimport ExamContent from \"../../../components/questions/ExamContent\";\r\nimport { requestFullscreen, exitFullscreen, isFullscreen } from \"../../../utils/fullscreenUtils\";\r\nimport {\r\n    setRemainingTime,\r\n    summitExam,\r\n    setSaveQuestions,\r\n    setErrorQuestions,\r\n    getRemainingTime,\r\n    logUserActivity,\r\n    submitAnswerWithAttempt,\r\n    leaveExam,\r\n} from \"../../../features/doExam/doExamSlice\";\r\n\r\nconst DoExamPage = () => {\r\n    const { examId } = useParams();\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n    const { exam } = useSelector(state => state.exams);\r\n    const { questions } = useSelector(state => state.questions);\r\n    const { answers } = useSelector(state => state.answers);\r\n    const [fontSize, setFontSize] = useState(14); // 14px mặc định\r\n    const [imageSize, setImageSize] = useState(12); // đơn vị: rem\r\n    const questionRefs = useRef([]);\r\n    const [isAgree, setIsAgree] = useState(false);\r\n    const [attemptId, setAttemptId] = useState(null);\r\n    const attemptRef = useRef(null);\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const [flag, setFlag] = useState(false);\r\n    const [startTime1, setStartTime1] = useState(null);\r\n    const hasSubmittedRef = useRef(false);\r\n    const examRef = useRef(null);\r\n    const examContentRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        examRef.current = exam;\r\n        if (exam?.acceptDoExam === false) {\r\n            navigate(`/practice/exam/${examId}`)\r\n        }\r\n    }, [exam]);\r\n\r\n    useEffect(() => {\r\n        if (examId) {\r\n            dispatch(fetchPublicExamById(examId));\r\n        }\r\n    }, [dispatch, examId]);\r\n\r\n\r\n    const { user } = useSelector((state) => state.auth);\r\n    const { remainingTime, saveQuestions, errorQuestions } = useSelector((state) => state.doExam);\r\n\r\n\r\n    const [markedQuestions, setMarkedQuestions] = useState(new Set());\r\n    const [timeWarningShown, setTimeWarningShown] = useState({\r\n        fiveMinutes: false,\r\n        oneMinute: false\r\n    });\r\n    const [isTimeBlinking, setIsTimeBlinking] = useState(false);\r\n\r\n    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];\r\n    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];\r\n\r\n    const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n    const [isDarkMode, setIsDarkMode] = useState(() => {\r\n        const saved = localStorage.getItem(\"isDarkMode\");\r\n        return saved ? JSON.parse(saved) : false;\r\n    });\r\n\r\n    const [loadingSubmit, setLoadingSubmit] = useState(false);\r\n    const [loadingLoadExam, setLoadingLoadExam] = useState(false);\r\n    const [isTimeUp, setIsTimeUp] = useState(false);\r\n\r\n    const [questionTN, setQuestionTN] = useState([]);\r\n    const [questionDS, setQuestionDS] = useState([]);\r\n    const [questionTLN, setQuestionTLN] = useState([]);\r\n\r\n    const [answerTN, setAnswerTN] = useState([]);\r\n    const [answerTLN, setAnswerTLN] = useState([]);\r\n    const [dsAnswers, setDsAnswers] = useState({});\r\n\r\n    document.addEventListener(\"copy\", (e) => {\r\n        e.preventDefault();\r\n    });\r\n\r\n    const addQuestion = (questionId) => {\r\n        const newSet = new Set(saveQuestions);\r\n        newSet.add(questionId);\r\n        dispatch(setSaveQuestions(Array.from(newSet))); // convert to array before saving\r\n        removeErrorQuestion(questionId);\r\n    };\r\n\r\n    const addErrorQuestion = (questionId) => {\r\n        const newSet = new Set(errorQuestions);\r\n        newSet.add(questionId);\r\n        dispatch(setErrorQuestions(Array.from(newSet)));\r\n        removeQuestion(questionId);\r\n    };\r\n\r\n    const removeQuestion = (questionId) => {\r\n        const newSet = new Set(saveQuestions);\r\n        newSet.delete(questionId);\r\n        dispatch(setSaveQuestions(Array.from(newSet)));\r\n    };\r\n\r\n    const removeErrorQuestion = (questionId) => {\r\n        const newSet = new Set(errorQuestions);\r\n        newSet.delete(questionId);\r\n        dispatch(setErrorQuestions(Array.from(newSet)));\r\n    };\r\n\r\n    // Hàm đánh dấu câu hỏi để xem lại sau\r\n    const toggleMarkQuestion = (questionId) => {\r\n        setMarkedQuestions(prev => {\r\n            const newSet = new Set(prev);\r\n            if (newSet.has(questionId)) {\r\n                newSet.delete(questionId);\r\n            } else {\r\n                newSet.add(questionId);\r\n            }\r\n            return newSet;\r\n        });\r\n    };\r\n\r\n\r\n    const handleExitFullscreen = () => {\r\n        try {\r\n            exitFullscreen();\r\n        } catch (err) {\r\n            // Chỉ ghi log lỗi, không bắt lỗi\r\n            console.warn(\"Không thể thoát fullscreen:\", err);\r\n        }\r\n    };\r\n\r\n    const handleFontSizeChange = (e) => {\r\n        setFontSize(Number(e.target.value));\r\n    };\r\n\r\n    const handleImageSizeChange = (e) => {\r\n        setImageSize(Number(e.target.value));\r\n    };\r\n\r\n    const formatTime = (seconds) => {\r\n        const min = String(Math.floor(seconds / 60)).padStart(2, '0');\r\n        const sec = String(seconds % 60).padStart(2, '0');\r\n        return `${min}:${sec}`;\r\n    };\r\n\r\n    const handleFullScreen = async () => {\r\n        setLoadingLoadExam(true);\r\n\r\n        const startExam = () => {\r\n            socket.emit(\"join_exam\", { studentId: user.id, examId });\r\n            console.log(\"📨 Đã gửi yêu cầu vào thi\");\r\n        };\r\n\r\n        if (!socket.connected) {\r\n            console.log(\"⚠️ Socket chưa kết nối. Đang kết nối...\");\r\n            socket.connect();\r\n\r\n            // Chờ socket connect xong mới gửi\r\n            socket.once(\"connect\", () => {\r\n                console.log(\"✅ Socket đã kết nối!\");\r\n                startExam();\r\n            });\r\n\r\n            socket.once(\"exam_error\", ({ message }) => {\r\n                dispatch(setErrorMessage(\"Lỗi: \" + message));\r\n                setLoadingLoadExam(false);\r\n                navigate(`/practice/exam/${examId}`);\r\n                return;\r\n            });\r\n\r\n            // Trường hợp connect fail trong 5 giây → timeout\r\n            setTimeout(() => {\r\n                if (!socket.connected) {\r\n                    setLoadingLoadExam(false);\r\n                }\r\n            }, 5000);\r\n\r\n        } else {\r\n            startExam();\r\n        }\r\n\r\n        // Lắng nghe lỗi\r\n        socket.once(\"exam_error\", ({ message }) => {\r\n            dispatch(setErrorMessage(\"Lỗi: \" + message));\r\n            setLoadingLoadExam(false);\r\n        });\r\n    };\r\n\r\n    useEffect(() => {\r\n        const handleExamStarted = async ({ attemptId, startTime }) => {\r\n            console.log(\"Đã nhận được thông báo bắt đầu thi từ server:\", attemptId);\r\n            setIsAgree(true);\r\n\r\n            attemptRef.current = attemptId;\r\n            setAttemptId(attemptId);\r\n\r\n            if (examId) {\r\n                dispatch(fetchPublicQuestionsByExamId(examId));\r\n            }\r\n            setStartTime1(startTime)\r\n            if (!exam?.isCheatingCheckEnabled) {\r\n                setLoadingLoadExam(false);\r\n                return\r\n            }\r\n            try {\r\n                const success = await requestFullscreen();\r\n                if (success) {\r\n                    setTimeout(() => {\r\n                        setLoadingLoadExam(false);\r\n                    }, 800);\r\n                } else {\r\n                    // Nếu không thể vào fullscreen, vẫn cho phép làm bài\r\n                    console.warn(\"Không thể vào fullscreen, nhưng vẫn cho phép làm bài\");\r\n                    setLoadingLoadExam(false);\r\n                }\r\n            } catch (err) {\r\n                console.error(\"❌ Lỗi khi bật fullscreen:\", err);\r\n                // Vẫn cho phép làm bài ngay cả khi không thể vào fullscreen\r\n                alert(\"Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.\");\r\n                setLoadingLoadExam(false);\r\n            }\r\n        };\r\n\r\n        socket.on(\"exam_started\", handleExamStarted);\r\n\r\n        return () => {\r\n            socket.off(\"exam_started\", handleExamStarted);\r\n        };\r\n    }, [examId, dispatch, socket]);\r\n\r\n    useEffect(() => {\r\n        if (exam?.testDuration && startTime1) {\r\n            const start = new Date(startTime1);\r\n            const now = new Date();\r\n            const elapsedSeconds = Math.floor((now - start) / 1000);\r\n            const totalSeconds = exam.testDuration * 60;\r\n            const remaining = Math.max(totalSeconds - elapsedSeconds, 0);\r\n            // console.log(start, now, remaining)\r\n            dispatch(setRemainingTime(remaining));\r\n\r\n            // Yêu cầu thời gian từ server khi bắt đầu\r\n            if (socket.connected && attemptId) {\r\n                socket.emit(\"request_time\", { examId, attemptId });\r\n                console.log(\"Đã gửi yêu cầu thời gian từ server\");\r\n            }\r\n        }\r\n    }, [startTime1, exam, socket.connected, attemptId, examId]);\r\n\r\n    useEffect(() => {\r\n        if (flag) return\r\n        if (!remainingTime) setFlag(true)\r\n    }, [remainingTime])\r\n\r\n    const handleAutoSubmit = async () => {\r\n        if (hasSubmittedRef.current) {\r\n            console.warn(\"⛔ Đã submit rồi, bỏ qua lần gọi lại.\");\r\n            return;\r\n        }\r\n        hasSubmittedRef.current = true; // Đánh dấu đã submit\r\n        console.log(\"Kiểm tra attemptId:\", attemptId);\r\n        if (!attemptId) {\r\n            console.log(\"Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải\");\r\n            return;\r\n        }\r\n\r\n        console.log(\"Đang nộp bài với attemptId:\", attemptId);\r\n        dispatch(setSaveQuestions(new Set()));\r\n        setLoadingSubmit(true);\r\n\r\n        try {\r\n            // Sử dụng API thay vì socket để nộp bài\r\n            const result = await dispatch(summitExam(attemptId)).unwrap();\r\n            console.log(\"Nộp bài thành công:\", result);\r\n\r\n            // Xử lý khi nộp bài thành công\r\n            dispatch(setSuccessMessage(\"Nộp bài thành công!\"));\r\n\r\n            // Thoát fullscreen mà không bắt lỗi\r\n            try {\r\n                exitFullscreen();\r\n            } catch (err) {\r\n                // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính\r\n                console.warn(\"Không thể thoát fullscreen khi nộp bài:\", err);\r\n            }\r\n\r\n            const safeAttemptId = attemptRef.current;\r\n            const currentExam = examRef.current;\r\n\r\n            if (!safeAttemptId) {\r\n                console.error(\"Không có attemptId khi navigate!\");\r\n                return;\r\n            }\r\n\r\n            // Log để debug\r\n            console.log(\"Current exam state:\", currentExam);\r\n            console.log(\"Attempt ID:\", safeAttemptId);\r\n\r\n            if (!currentExam || !currentExam.seeCorrectAnswer) {\r\n                console.log(\"Chuyển về trang danh sách do:\", {\r\n                    examNull: !currentExam,\r\n                    cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer\r\n                });\r\n                navigate(`/practice/exam/${examId}`);\r\n                return;\r\n            }\r\n\r\n            navigate(`/practice/exam/attempt/${safeAttemptId}/score`);\r\n        } catch (error) {\r\n            console.error(\"Lỗi khi nộp bài:\", error);\r\n            setLoadingSubmit(false);\r\n            dispatch(setErrorMessage(\"Lỗi khi nộp bài. Vui lòng thử lại.\"));\r\n            hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại\r\n\r\n            // Thử nộp lại sau 3 giây nếu lỗi xảy ra\r\n            setTimeout(() => {\r\n                if (!loadingSubmit && attemptRef.current) {\r\n                    console.log(\"Thử nộp bài lại sau lỗi...\");\r\n                    handleAutoSubmit();\r\n                }\r\n            }, 5000);\r\n        }\r\n    };\r\n\r\n    // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị\r\n    const navigateToQuestion = useCallback((questionId) => {\r\n        setSelectedQuestion(questionId);\r\n\r\n        // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không\r\n        if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {\r\n            // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById\r\n            examContentRef.current.goToQuestionById(questionId);\r\n        } else {\r\n            // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi\r\n            // Tìm phần tử câu hỏi bằng querySelector\r\n            setTimeout(() => {\r\n                // Thử tìm phần tử bằng data-question-id\r\n                const element = document.querySelector(`[data-question-id=\"${questionId}\"]`);\r\n\r\n                if (element) {\r\n                    const offset = 80; // chiều cao của header sticky\r\n                    const y = element.getBoundingClientRect().top + window.scrollY - offset;\r\n                    window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                } else {\r\n                    // Fallback: Sử dụng refs\r\n                    const refElement = questionRefs.current[questionId];\r\n\r\n                    if (refElement) {\r\n                        const offset = 80; // chiều cao của header sticky\r\n                        const y = refElement.getBoundingClientRect().top + window.scrollY - offset;\r\n                        window.scrollTo({ top: y, behavior: \"smooth\" });\r\n                    }\r\n                }\r\n            }, 0);\r\n        }\r\n    }, [questionRefs, examContentRef]);\r\n\r\n    // Alias cho navigateToQuestion để tương thích với các component khác\r\n    const scrollToQuestion = navigateToQuestion;\r\n\r\n    const handleSelectAnswerTN = (questionId, statementId, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const payload = {\r\n            attemptId,\r\n            questionId,\r\n            answerContent: statementId,\r\n            studentId: user.id, // nếu cần xác định user\r\n            type,\r\n            examId,\r\n            name: user.lastName + \" \" + user.firstName,\r\n        };\r\n        const newAnswer = {\r\n            questionId,\r\n            answerContent: statementId,\r\n            typeOfQuestion: type,\r\n        };\r\n        dispatch(setAnswers(newAnswer));\r\n\r\n        socket.emit(\"select_answer\", payload);\r\n    };\r\n\r\n    const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        const currentAnswers = dsAnswers[questionId] || [];\r\n\r\n        const existing = currentAnswers.find(ans => ans.statementId === statementId);\r\n\r\n        // 🔁 Nếu đáp án đã giống thì không gửi lại\r\n        if (existing && existing.answer === selectedAnswer) {\r\n            return\r\n        }\r\n\r\n        const updatedAnswers = currentAnswers.map(ans =>\r\n            ans.statementId === statementId\r\n                ? { ...ans, answer: selectedAnswer }\r\n                : ans\r\n        );\r\n\r\n        // Nếu chưa có statement này\r\n        if (!existing) {\r\n            updatedAnswers.push({ statementId, answer: selectedAnswer });\r\n        }\r\n\r\n        // ✨ Gửi toàn bộ lên server\r\n        socket.emit(\"select_answer\", {\r\n            questionId,\r\n            answerContent: updatedAnswers,\r\n            studentId: user.id,\r\n            attemptId,\r\n            type: \"DS\",\r\n            examId,\r\n            name: user.lastName + \" \" + user.firstName,\r\n        });\r\n        dispatch(setAnswers({ questionId, answerContent: JSON.stringify(updatedAnswers), typeOfQuestion: \"DS\" }));\r\n    };\r\n\r\n\r\n    const handleSelectAnswerTLN = (questionId, answerContent, type) => {\r\n        // Không cho phép làm bài nếu đã hết thời gian\r\n        if (isTimeUp) {\r\n            dispatch(setErrorMessage(\"Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!\"));\r\n            return;\r\n        }\r\n\r\n        if (!answerContent || answerContent.trim() === \"\") {\r\n            return;\r\n        }\r\n\r\n        const payload = {\r\n            attemptId,\r\n            questionId,\r\n            answerContent: answerContent.trim().replace(\",\", \".\"),\r\n            studentId: user.id,\r\n            type,\r\n            examId,\r\n            name: user.lastName + \" \" + user.firstName,\r\n        };\r\n\r\n        dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));\r\n\r\n        socket.emit(\"select_answer\", payload);\r\n    }\r\n\r\n    // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu\r\n    const questionsToMarkAsSaved = useRef(new Set());\r\n\r\n    // useEffect để xử lý việc đánh dấu câu hỏi đã lưu\r\n    useEffect(() => {\r\n        if (questionsToMarkAsSaved.current.size > 0) {\r\n            questionsToMarkAsSaved.current.forEach(questionId => {\r\n                if (!saveQuestions.has(questionId)) {\r\n                    addQuestion(questionId);\r\n                }\r\n            });\r\n            questionsToMarkAsSaved.current.clear();\r\n        }\r\n    }, [saveQuestions, addQuestion]);\r\n\r\n    // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render\r\n    useEffect(() => {\r\n        // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất\r\n        const frameId = requestAnimationFrame(() => {\r\n            if (questionsToMarkAsSaved.current.size > 0) {\r\n                const questionIds = [...questionsToMarkAsSaved.current];\r\n                questionsToMarkAsSaved.current.clear();\r\n\r\n                // Cập nhật state cho tất cả các câu hỏi cần đánh dấu\r\n                questionIds.forEach(questionId => {\r\n                    if (!saveQuestions.has(questionId)) {\r\n                        addQuestion(questionId);\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        return () => cancelAnimationFrame(frameId);\r\n    });\r\n\r\n    const isTNSelected = useCallback((questionId, statementId) => {\r\n        const isSelected = answerTN.some(\r\n            (ans) =>\r\n                ans.questionId === questionId &&\r\n                ans.answerContent &&\r\n                String(ans.answerContent) === String(statementId)\r\n        );\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.has(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [answerTN, saveQuestions]);\r\n\r\n    const isDSChecked = useCallback((questionId, statementId, bool) => {\r\n        const isSelected = dsAnswers[questionId]?.some(\r\n            (a) => a.statementId === statementId && a.answer === bool\r\n        ) || false;\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (isSelected && !saveQuestions.has(questionId) && dsAnswers[questionId]?.length === 4) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return isSelected;\r\n    }, [dsAnswers, saveQuestions]);\r\n\r\n    const getTLNDefaultValue = useCallback((questionId) => {\r\n        const matched = answerTLN.find((ans) => ans.questionId === questionId);\r\n        const content = matched?.answerContent?.replace(/^\"|\"$/g, \"\") || \"\";\r\n\r\n        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý\r\n        if (content && !saveQuestions.has(questionId)) {\r\n            questionsToMarkAsSaved.current.add(questionId);\r\n        }\r\n\r\n        return content;\r\n    }, [answerTLN, saveQuestions]);\r\n\r\n    // useEffect(() => {\r\n    //     if (examId) {\r\n    //         dispatch(fetchPublicQuestionsByExamId(examId));\r\n    //     }\r\n    // }, [dispatch, examId]);\r\n\r\n    useEffect(() => {\r\n        if (questions) {\r\n            setQuestionTN(questions.filter((question) => question.typeOfQuestion === \"TN\"));\r\n            setQuestionDS(questions.filter((question) => question.typeOfQuestion === \"DS\"));\r\n            setQuestionTLN(questions.filter((question) => question.typeOfQuestion === \"TLN\"));\r\n        }\r\n    }, [questions]);\r\n\r\n    useEffect(() => {\r\n        // Kiểm tra answers có phải là mảng không\r\n        if (!Array.isArray(answers) || answers.length === 0) return;\r\n\r\n        const tn = [];\r\n        const tln = [];\r\n        const dsMap = {};\r\n\r\n        // Sử dụng for...of thay vì forEach để tránh lỗi\r\n        for (const answer of answers) {\r\n            if (answer.typeOfQuestion === \"TN\") {\r\n                tn.push(answer);\r\n            } else if (answer.typeOfQuestion === \"TLN\") {\r\n                tln.push(answer);\r\n            } else if (answer.typeOfQuestion === \"DS\" && answer.answerContent) {\r\n                try {\r\n                    const parsed = JSON.parse(answer.answerContent);\r\n                    dsMap[answer.questionId] = parsed;\r\n                } catch (err) {\r\n                    console.error(\"Lỗi parse DS answerContent:\", err);\r\n                }\r\n            }\r\n        }\r\n\r\n        setAnswerTN(tn);\r\n        setAnswerTLN(tln);\r\n        setDsAnswers(dsMap);\r\n        if (!socket || !socket.connected || !attemptId || !examId) return;\r\n        socket.emit(\"calculate_score\", {\r\n            attemptId,\r\n            answers,\r\n            examId,\r\n            student: user,\r\n        });\r\n    }, [answers]);\r\n\r\n\r\n    useEffect(() => {\r\n        if (attemptId) {\r\n            dispatch(fetchAnswersByAttempt(attemptId));\r\n        }\r\n    }, [dispatch, attemptId]);\r\n\r\n    useEffect(() => {\r\n        if (!exam?.testDuration || remainingTime === null || !isAgree) return;\r\n\r\n        // Kiểm tra và hiển thị cảnh báo thời gian\r\n        const checkTimeWarnings = (time) => {\r\n            // Cảnh báo khi còn 5 phút\r\n            if (time === 300 && !timeWarningShown.fiveMinutes) {\r\n                setTimeWarningShown(prev => ({ ...prev, fiveMinutes: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 5 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Tắt hiệu ứng nhấp nháy sau 10 giây\r\n                setTimeout(() => {\r\n                    setIsTimeBlinking(false);\r\n                }, 10000);\r\n            }\r\n\r\n            // Cảnh báo khi còn 1 phút\r\n            if (time === 60 && !timeWarningShown.oneMinute) {\r\n                setTimeWarningShown(prev => ({ ...prev, oneMinute: true }));\r\n                setIsTimeBlinking(true);\r\n                dispatch(setErrorMessage(\"Còn 1 phút nữa là hết thời gian làm bài!\"));\r\n\r\n                // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian\r\n            }\r\n        };\r\n\r\n        // Định kỳ yêu cầu thời gian từ server để đồng bộ\r\n        const syncTimeInterval = setInterval(() => {\r\n            if (socket.connected && attemptId) {\r\n                socket.emit(\"request_time\", { examId, attemptId });\r\n            }\r\n        }, 30000); // Đồng bộ thời gian mỗi 30 giây\r\n\r\n        const interval = setInterval(() => {\r\n            dispatch(setRemainingTime((prev) => {\r\n                if (prev <= 1) { // dùng <=1 để đảm bảo không bị âm\r\n                    clearInterval(interval);\r\n                    clearInterval(syncTimeInterval);\r\n                    // Đánh dấu là đã hết thời gian\r\n                    setIsTimeUp(true);\r\n                    setIsTimeBlinking(false);\r\n                    // Thử nộp bài\r\n                    handleAutoSubmit();\r\n                    return 0;\r\n                }\r\n\r\n                // Kiểm tra cảnh báo thời gian\r\n                checkTimeWarnings(prev);\r\n\r\n                return prev - 1;\r\n            }));\r\n        }, 1000);\r\n\r\n        return () => {\r\n            clearInterval(interval);\r\n            clearInterval(syncTimeInterval);\r\n        };\r\n    }, [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, socket, attemptId, examId]);// Chỉ phụ thuộc vào các giá trị cần thiết\r\n\r\n    useEffect(() => {\r\n        if (isAgree && !socket.connected) {\r\n            socket.connect();\r\n        }\r\n        return () => {\r\n            socket.disconnect();\r\n        };\r\n    }, [isAgree]);\r\n\r\n    // frontend\r\n    useEffect(() => {\r\n        if (!attemptId || !user?.id || !examId || attemptId === null || attemptId === undefined) return;\r\n        if (!exam?.isCheatingCheckEnabled) return;\r\n        console.log(\"Đã bật theo dõi hành vi gian lận\");\r\n\r\n\r\n        const recentLogs = new Set(); // chống log lặp\r\n        const logOnce = (key, payload) => {\r\n\r\n            if (!exam?.isCheatingCheckEnabled || recentLogs.has(key)) return;\r\n\r\n            recentLogs.add(key);\r\n            socket.emit(\"user_log\", { ...payload, name: user.lastName + \" \" + user.firstName });\r\n\r\n            setTimeout(() => recentLogs.delete(key), 5000);\r\n        };\r\n\r\n        // 📌 Thoát fullscreen\r\n        const handleFullscreenChange = () => {\r\n            if (!document.fullscreenElement &&\r\n                !document.webkitFullscreenElement &&\r\n                !document.mozFullScreenElement &&\r\n                !document.msFullscreenElement) {\r\n                logOnce(\"exit_fullscreen\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"EF\",\r\n                    action: \"exit_fullscreen\",\r\n                    detail: JSON.stringify({ reason: \"User exited fullscreen mode\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Chuyển tab hoặc thu nhỏ trình duyệt\r\n        const handleVisibilityChange = () => {\r\n            if (document.visibilityState === \"hidden\") {\r\n                logOnce(\"tab_blur\", {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"TB\",\r\n                    action: \"tab_blur\",\r\n                    detail: JSON.stringify({ message: \"User switched tab or minimized window\" }),\r\n                });\r\n            }\r\n        };\r\n\r\n        // 📌 Copy nội dung\r\n        const handleCopy = () => {\r\n            logOnce(\"copy_detected\", {\r\n                studentId: user.id,\r\n                attemptId,\r\n                examId,\r\n                code: \"COP\",\r\n                action: \"copy_detected\",\r\n                detail: JSON.stringify({ message: \"User copied content\" }),\r\n            });\r\n        };\r\n\r\n        // 📌 Phím đáng ngờ\r\n        const handleSuspiciousKey = (e) => {\r\n            const suspiciousKeys = [\r\n                \"F12\", \"PrintScreen\", \"Alt\", \"Tab\", \"Meta\", \"Control\", \"Shift\"\r\n            ];\r\n            const combo = `${e.ctrlKey ? \"Ctrl+\" : \"\"}${e.shiftKey ? \"Shift+\" : \"\"}${e.altKey ? \"Alt+\" : \"\"}${e.metaKey ? \"Meta+\" : \"\"}${e.key}`;\r\n\r\n            if (\r\n                suspiciousKeys.includes(e.key) ||\r\n                combo === \"Ctrl+Shift+I\" ||\r\n                combo === \"Ctrl+Shift+C\"\r\n            ) {\r\n                logOnce(`key_${combo}`, {\r\n                    studentId: user.id,\r\n                    attemptId,\r\n                    examId,\r\n                    code: \"SK\",\r\n                    action: \"suspicious_key\",\r\n                    detail: JSON.stringify({ key: e.key, code: e.code, combo }),\r\n                });\r\n            }\r\n        };\r\n\r\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n        document.addEventListener(\"copy\", handleCopy);\r\n        document.addEventListener(\"keydown\", handleSuspiciousKey);\r\n\r\n        return () => {\r\n            document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\r\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n            document.removeEventListener(\"copy\", handleCopy);\r\n            document.removeEventListener(\"keydown\", handleSuspiciousKey);\r\n        };\r\n    }, [socket, user.id, examId, attemptId]);\r\n\r\n\r\n    useEffect(() => {\r\n        // Chỉ lắng nghe các sự kiện liên quan đến câu trả lời\r\n        const handleAnswerSaved = ({ questionId }) => {\r\n            addQuestion(questionId);\r\n            removeErrorQuestion(questionId);\r\n        };\r\n\r\n        const handleAnswerError = ({ questionId, message }) => {\r\n            dispatch(setErrorMessage(message));\r\n            removeQuestion(questionId);\r\n            addErrorQuestion(questionId);\r\n        };\r\n\r\n        // Lắng nghe sự kiện cập nhật thời gian từ server\r\n        const handleExamTimer = ({ remainingTime: serverRemainingTime }) => {\r\n            console.log(\"Nhận thời gian từ server:\", serverRemainingTime);\r\n            dispatch(setRemainingTime(serverRemainingTime));\r\n        };\r\n\r\n        // Lắng nghe sự kiện bài thi tự động nộp\r\n        const handleExamAutoSubmitted = ({ message, attemptId: autoSubmitAttemptId, score }) => {\r\n            console.log(\"Bài thi đã tự động nộp:\", { message, autoSubmitAttemptId, score });\r\n            dispatch(setSuccessMessage(message));\r\n            setIsTimeUp(true);\r\n\r\n            // Thoát fullscreen\r\n            try {\r\n                exitFullscreen();\r\n            } catch (err) {\r\n                console.warn(\"Không thể thoát fullscreen khi bài thi tự động nộp:\", err);\r\n            }\r\n\r\n            // Chuyển hướng đến trang kết quả nếu được phép xem đáp án\r\n            if (exam?.seeCorrectAnswer) {\r\n                navigate(`/practice/exam/attempt/${autoSubmitAttemptId}/score`);\r\n            } else {\r\n                navigate(`/practice/exam/${examId}`);\r\n            }\r\n        };\r\n\r\n        // Lắng nghe thông báo từ giáo viên hoặc hệ thống\r\n        const handleExamNotification = ({ message }) => {\r\n            console.log(\"Nhận thông báo:\", message);\r\n            dispatch(setSuccessMessage(message));\r\n        };\r\n\r\n        // Đăng ký các event listeners\r\n        socket.on(\"answer_saved\", handleAnswerSaved);\r\n        socket.on(\"answer_error\", handleAnswerError);\r\n        socket.on(\"exam_timer\", handleExamTimer);\r\n        socket.on(\"exam_auto_submitted\", handleExamAutoSubmitted);\r\n        socket.on(\"exam_notification\", handleExamNotification);\r\n\r\n        return () => {\r\n            // Hủy đăng ký các event listeners\r\n            socket.off(\"answer_saved\", handleAnswerSaved);\r\n            socket.off(\"answer_error\", handleAnswerError);\r\n            socket.off(\"exam_timer\", handleExamTimer);\r\n            socket.off(\"exam_auto_submitted\", handleExamAutoSubmitted);\r\n            socket.off(\"exam_notification\", handleExamNotification);\r\n        };\r\n    }, [exam, examId, navigate, dispatch, addQuestion, removeQuestion, removeErrorQuestion, addErrorQuestion]);\r\n\r\n    useEffect(() => {\r\n        localStorage.setItem(\"isDarkMode\", JSON.stringify(isDarkMode));\r\n    }, [isDarkMode]);\r\n\r\n    // Hàm xử lý chuyển đổi câu hỏi\r\n    const handleKeyDown = useCallback((e) => {\r\n        // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons\r\n        if ([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"].includes(e.key)) {\r\n            // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)\r\n            e.preventDefault();\r\n\r\n            // Nếu không có câu hỏi, thoát khỏi hàm\r\n            if (!questions || questions.length === 0) return;\r\n\r\n            const allQuestions = [...questionTN, ...questionDS, ...questionTLN];\r\n            const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);\r\n\r\n            if ((e.key === \"ArrowUp\" || e.key === \"ArrowLeft\") && currentIndex > 0) {\r\n                const prevQuestionId = allQuestions[currentIndex - 1].id;\r\n                console.log(\"ArrowUp/Left pressed, navigating to question:\", prevQuestionId);\r\n                navigateToQuestion(prevQuestionId);\r\n            } else if ((e.key === \"ArrowDown\" || e.key === \"ArrowRight\") && currentIndex < allQuestions.length - 1) {\r\n                const nextQuestionId = allQuestions[currentIndex + 1].id;\r\n                console.log(\"ArrowDown/Right pressed, navigating to question:\", nextQuestionId);\r\n                navigateToQuestion(nextQuestionId);\r\n            }\r\n        }\r\n    }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);\r\n    // Lắng nghe sự kiện bàn phím\r\n    useEffect(() => {\r\n        document.addEventListener(\"keydown\", handleKeyDown);\r\n        return () => {\r\n            document.removeEventListener(\"keydown\", handleKeyDown);\r\n        };\r\n    }, [handleKeyDown]);\r\n\r\n    return (\r\n        <div className={`flex flex-col h-full ${isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'}`}>\r\n            <HeaderDoExamPage nameExam={exam?.name} onExitFullscreen={handleExitFullscreen} isDarkMode={!isDarkMode} />\r\n            {isAgree ? (\r\n                <div className=\"flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5\">\r\n                    {/* Main Content */}\r\n                    <ExamContent\r\n                        ref={examContentRef}\r\n                        loading1={loadingLoadExam}\r\n                        isDarkMode={isDarkMode}\r\n                        questionTN={questionTN}\r\n                        questionDS={questionDS}\r\n                        questionTLN={questionTLN}\r\n                        handlers={{\r\n                            handleSelectAnswerTN,\r\n                            handleSelectAnswerDS,\r\n                            handleSelectAnswerTLN,\r\n                            isTNSelected,\r\n                            isDSChecked,\r\n                            getTLNDefaultValue,\r\n                            setQuestionRef: (id, el) => (questionRefs.current[id] = el),\r\n                            setSelectedQuestion: (id) => setSelectedQuestion(id)\r\n                        }}\r\n                        settings={{\r\n                            selectedQuestion,\r\n                            isDarkMode,\r\n                            fontSize,\r\n                            imageSize,\r\n                            prefixStatementTN,\r\n                            prefixStatementDS,\r\n                            isTimeUp,\r\n                            markedQuestions,\r\n                            toggleMarkQuestion\r\n                        }}\r\n                        isTimeUp={isTimeUp}\r\n                        // Để undefined để component tự quyết định dựa trên thiết bị\r\n                        initialSingleMode={undefined}\r\n                        handleAutoSubmit={handleAutoSubmit}\r\n                        loadingSubmit={loadingSubmit}\r\n                    />\r\n\r\n\r\n                    {/* Button toggle cho mobile */}\r\n                    <div className=\"fixed bottom-4 right-4 z-50 lg:hidden\">\r\n                        <button\r\n                            className={`p-2 rounded-full shadow-md ${isDarkMode ? \"bg-gray-800 text-white\" : \"bg-white text-black\"}`}\r\n                            onClick={() => setIsSidebarOpen(prev => !prev)}\r\n                        >\r\n                            <Menu />\r\n                        </button>\r\n                    </div>\r\n\r\n                    {/* Sidebar chính */}\r\n                    <AnimatePresence>\r\n                        {(isSidebarOpen || window.innerWidth > 1024) && (\r\n                            <ExamSidebar\r\n                                isDarkMode={isDarkMode}\r\n                                setIsDarkMode={setIsDarkMode}\r\n                                fontSize={fontSize}\r\n                                handleFontSizeChange={handleFontSizeChange}\r\n                                imageSize={imageSize}\r\n                                handleImageSizeChange={handleImageSizeChange}\r\n                                questionTN={questionTN}\r\n                                questionDS={questionDS}\r\n                                questionTLN={questionTLN}\r\n                                scrollToQuestion={navigateToQuestion}\r\n                                selectedQuestion={selectedQuestion}\r\n                                markedQuestions={markedQuestions}\r\n                                toggleMarkQuestion={toggleMarkQuestion}\r\n                                handleAutoSubmit={handleAutoSubmit}\r\n                                loadingSubmit={loadingSubmit}\r\n                                loadingLoadExam={loadingLoadExam}\r\n                                exam={exam}\r\n                                remainingTime={remainingTime}\r\n                                formatTime={formatTime}\r\n                                questions={questions}\r\n                                singleQuestionMode={examContentRef.current?.isSingleQuestionMode() || false}\r\n                                setSingleQuestionMode={(value) => {\r\n                                    if (examContentRef.current) {\r\n                                        // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị\r\n                                        examContentRef.current.setSingleQuestionMode(value);\r\n                                    }\r\n                                }}\r\n                            />\r\n                        )}\r\n                    </AnimatePresence>\r\n\r\n                </div>\r\n            ) : (\r\n                <div className=\"flex items-center justify-center\">\r\n                    <ExamRegulationModal\r\n                        onClose={() => {\r\n                            if (socket.connected) {\r\n                                socket.emit(\"leave_exam\", { studentId: user?.id, examId });\r\n                                socket.disconnect();\r\n                            }\r\n                            socket.removeAllListeners(); // Xóa hết listener để tránh lỗi khi component bị unmount\r\n                            navigate(`/practice/exam/${examId}`);\r\n                        }}\r\n                        isOpen={!isAgree}\r\n                        onStartExam={handleFullScreen}\r\n                    />\r\n                </div>\r\n            )}\r\n\r\n            {exam?.testDuration && isAgree && (\r\n                <div className={`fixed bottom-2 rounded-md left-2 px-4 py-2\r\n                    ${isTimeBlinking\r\n                        ? 'bg-red-600 animate-pulse'\r\n                        : 'bg-slate-700 bg-opacity-80'}\r\n                    text-white z-50 transition-colors duration-300`}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                        <div className=\"text-sm font-bold\">{formatTime(remainingTime)} phút</div>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DoExamPage;\r\n"], "mappings": ";;AAAA,OAAOA,gBAAgB,MAAM,6CAA6C;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAChE,SAASC,4BAA4B,QAAQ,0CAA0C;AACvF,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,eAAe,EAAEC,iBAAiB,QAAQ,uCAAuC;AAC1F,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,sCAAsC;AACxF,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,WAAW,MAAM,2CAA2C;AACnE,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gCAAgC;AAChG,SACIC,gBAAgB,EAChBC,UAAU,EACVC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,eAAe,EACfC,uBAAuB,EACvBC,SAAS,QACN,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrB,MAAM;IAAEC;EAAO,CAAC,GAAG3B,SAAS,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAMqC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAK,CAAC,GAAGrC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAClD,MAAM;IAAEC;EAAU,CAAC,GAAGxC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACE,SAAS,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAGzC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;EACvD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM6C,YAAY,GAAG3C,MAAM,CAAC,EAAE,CAAC;EAC/B,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMkD,UAAU,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM,CAACiD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqD,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAMyD,eAAe,GAAGvD,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMwD,OAAO,GAAGxD,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMyD,cAAc,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACZyD,OAAO,CAACE,OAAO,GAAGxB,IAAI;IACtB,IAAI,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,YAAY,MAAK,KAAK,EAAE;MAC9B1B,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;IACxC;EACJ,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;EAEVnC,SAAS,CAAC,MAAM;IACZ,IAAIgC,MAAM,EAAE;MACRC,QAAQ,CAAC7B,mBAAmB,CAAC4B,MAAM,CAAC,CAAC;IACzC;EACJ,CAAC,EAAE,CAACC,QAAQ,EAAED,MAAM,CAAC,CAAC;EAGtB,MAAM;IAAE8B;EAAK,CAAC,GAAGhE,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAAC2B,IAAI,CAAC;EACnD,MAAM;IAAEC,aAAa;IAAEC,aAAa;IAAEC;EAAe,CAAC,GAAGpE,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAAC+B,MAAM,CAAC;EAG7F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,IAAIuE,GAAG,CAAC,CAAC,CAAC;EACjE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC;IACrD0E,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM8E,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtF,MAAMC,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEtF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,MAAM;IAC/C,MAAMoF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC5C,CAAC,CAAC;EAEF,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2F,eAAe,EAAEC,kBAAkB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM,CAAC+F,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmG,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACqG,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuG,SAAS,EAAEC,YAAY,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyG,SAAS,EAAEC,YAAY,CAAC,GAAG1G,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C2G,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAGC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAIC,UAAU,IAAK;IAChC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACL,aAAa,CAAC;IACrC+C,MAAM,CAACC,GAAG,CAACF,UAAU,CAAC;IACtB9E,QAAQ,CAACZ,gBAAgB,CAAC6F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAChDI,mBAAmB,CAACL,UAAU,CAAC;EACnC,CAAC;EAED,MAAMM,gBAAgB,GAAIN,UAAU,IAAK;IACrC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACJ,cAAc,CAAC;IACtC8C,MAAM,CAACC,GAAG,CAACF,UAAU,CAAC;IACtB9E,QAAQ,CAACX,iBAAiB,CAAC4F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;IAC/CM,cAAc,CAACP,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMO,cAAc,GAAIP,UAAU,IAAK;IACnC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACL,aAAa,CAAC;IACrC+C,MAAM,CAACO,MAAM,CAACR,UAAU,CAAC;IACzB9E,QAAQ,CAACZ,gBAAgB,CAAC6F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMI,mBAAmB,GAAIL,UAAU,IAAK;IACxC,MAAMC,MAAM,GAAG,IAAI1C,GAAG,CAACJ,cAAc,CAAC;IACtC8C,MAAM,CAACO,MAAM,CAACR,UAAU,CAAC;IACzB9E,QAAQ,CAACX,iBAAiB,CAAC4F,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAIT,UAAU,IAAK;IACvC1C,kBAAkB,CAACoD,IAAI,IAAI;MACvB,MAAMT,MAAM,GAAG,IAAI1C,GAAG,CAACmD,IAAI,CAAC;MAC5B,IAAIT,MAAM,CAACU,GAAG,CAACX,UAAU,CAAC,EAAE;QACxBC,MAAM,CAACO,MAAM,CAACR,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHC,MAAM,CAACC,GAAG,CAACF,UAAU,CAAC;MAC1B;MACA,OAAOC,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAGD,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACA1G,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO2G,GAAG,EAAE;MACV;MACAC,OAAO,CAACC,IAAI,CAAC,6BAA6B,EAAEF,GAAG,CAAC;IACpD;EACJ,CAAC;EAED,MAAMG,oBAAoB,GAAInB,CAAC,IAAK;IAChCnE,WAAW,CAACuF,MAAM,CAACpB,CAAC,CAACqB,MAAM,CAACC,KAAK,CAAC,CAAC;EACvC,CAAC;EAED,MAAMC,qBAAqB,GAAIvB,CAAC,IAAK;IACjCjE,YAAY,CAACqF,MAAM,CAACpB,CAAC,CAACqB,MAAM,CAACC,KAAK,CAAC,CAAC;EACxC,CAAC;EAED,MAAME,UAAU,GAAIC,OAAO,IAAK;IAC5B,MAAMC,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,MAAMC,GAAG,GAAGJ,MAAM,CAACF,OAAO,GAAG,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjD,UAAA7E,MAAA,CAAUyE,GAAG,OAAAzE,MAAA,CAAI8E,GAAG;EACxB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjCjD,kBAAkB,CAAC,IAAI,CAAC;IAExB,MAAMkD,SAAS,GAAGA,CAAA,KAAM;MACpBC,MAAM,CAACC,IAAI,CAAC,WAAW,EAAE;QAAEC,SAAS,EAAElF,IAAI,CAACmF,EAAE;QAAEjH;MAAO,CAAC,CAAC;MACxD6F,OAAO,CAACqB,GAAG,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED,IAAI,CAACJ,MAAM,CAACK,SAAS,EAAE;MACnBtB,OAAO,CAACqB,GAAG,CAAC,yCAAyC,CAAC;MACtDJ,MAAM,CAACM,OAAO,CAAC,CAAC;;MAEhB;MACAN,MAAM,CAACO,IAAI,CAAC,SAAS,EAAE,MAAM;QACzBxB,OAAO,CAACqB,GAAG,CAAC,sBAAsB,CAAC;QACnCL,SAAS,CAAC,CAAC;MACf,CAAC,CAAC;MAEFC,MAAM,CAACO,IAAI,CAAC,YAAY,EAAEC,IAAA,IAAiB;QAAA,IAAhB;UAAEC;QAAQ,CAAC,GAAAD,IAAA;QAClCrH,QAAQ,CAAC3B,eAAe,CAAC,OAAO,GAAGiJ,OAAO,CAAC,CAAC;QAC5C5D,kBAAkB,CAAC,KAAK,CAAC;QACzBzD,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACpC;MACJ,CAAC,CAAC;;MAEF;MACAwH,UAAU,CAAC,MAAM;QACb,IAAI,CAACV,MAAM,CAACK,SAAS,EAAE;UACnBxD,kBAAkB,CAAC,KAAK,CAAC;QAC7B;MACJ,CAAC,EAAE,IAAI,CAAC;IAEZ,CAAC,MAAM;MACHkD,SAAS,CAAC,CAAC;IACf;;IAEA;IACAC,MAAM,CAACO,IAAI,CAAC,YAAY,EAAEI,KAAA,IAAiB;MAAA,IAAhB;QAAEF;MAAQ,CAAC,GAAAE,KAAA;MAClCxH,QAAQ,CAAC3B,eAAe,CAAC,OAAO,GAAGiJ,OAAO,CAAC,CAAC;MAC5C5D,kBAAkB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC;EACN,CAAC;EAED3F,SAAS,CAAC,MAAM;IACZ,MAAM0J,iBAAiB,GAAG,MAAAC,KAAA,IAAoC;MAAA,IAA7B;QAAE5G,SAAS;QAAE6G;MAAU,CAAC,GAAAD,KAAA;MACrD9B,OAAO,CAACqB,GAAG,CAAC,+CAA+C,EAAEnG,SAAS,CAAC;MACvED,UAAU,CAAC,IAAI,CAAC;MAEhBG,UAAU,CAACU,OAAO,GAAGZ,SAAS;MAC9BC,YAAY,CAACD,SAAS,CAAC;MAEvB,IAAIf,MAAM,EAAE;QACRC,QAAQ,CAAC9B,4BAA4B,CAAC6B,MAAM,CAAC,CAAC;MAClD;MACAuB,aAAa,CAACqG,SAAS,CAAC;MACxB,IAAI,EAACzH,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0H,sBAAsB,GAAE;QAC/BlE,kBAAkB,CAAC,KAAK,CAAC;QACzB;MACJ;MACA,IAAI;QACA,MAAMmE,OAAO,GAAG,MAAM9I,iBAAiB,CAAC,CAAC;QACzC,IAAI8I,OAAO,EAAE;UACTN,UAAU,CAAC,MAAM;YACb7D,kBAAkB,CAAC,KAAK,CAAC;UAC7B,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,MAAM;UACH;UACAkC,OAAO,CAACC,IAAI,CAAC,sDAAsD,CAAC;UACpEnC,kBAAkB,CAAC,KAAK,CAAC;QAC7B;MACJ,CAAC,CAAC,OAAOiC,GAAG,EAAE;QACVC,OAAO,CAACkC,KAAK,CAAC,2BAA2B,EAAEnC,GAAG,CAAC;QAC/C;QACAoC,KAAK,CAAC,yDAAyD,CAAC;QAChErE,kBAAkB,CAAC,KAAK,CAAC;MAC7B;IACJ,CAAC;IAEDmD,MAAM,CAACmB,EAAE,CAAC,cAAc,EAAEP,iBAAiB,CAAC;IAE5C,OAAO,MAAM;MACTZ,MAAM,CAACoB,GAAG,CAAC,cAAc,EAAER,iBAAiB,CAAC;IACjD,CAAC;EACL,CAAC,EAAE,CAAC1H,MAAM,EAAEC,QAAQ,EAAE6G,MAAM,CAAC,CAAC;EAE9B9I,SAAS,CAAC,MAAM;IACZ,IAAImC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgI,YAAY,IAAI7G,UAAU,EAAE;MAClC,MAAM8G,KAAK,GAAG,IAAIC,IAAI,CAAC/G,UAAU,CAAC;MAClC,MAAMgH,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,cAAc,GAAG/B,IAAI,CAACC,KAAK,CAAC,CAAC6B,GAAG,GAAGF,KAAK,IAAI,IAAI,CAAC;MACvD,MAAMI,YAAY,GAAGrI,IAAI,CAACgI,YAAY,GAAG,EAAE;MAC3C,MAAMM,SAAS,GAAGjC,IAAI,CAACkC,GAAG,CAACF,YAAY,GAAGD,cAAc,EAAE,CAAC,CAAC;MAC5D;MACAtI,QAAQ,CAACd,gBAAgB,CAACsJ,SAAS,CAAC,CAAC;;MAErC;MACA,IAAI3B,MAAM,CAACK,SAAS,IAAIpG,SAAS,EAAE;QAC/B+F,MAAM,CAACC,IAAI,CAAC,cAAc,EAAE;UAAE/G,MAAM;UAAEe;QAAU,CAAC,CAAC;QAClD8E,OAAO,CAACqB,GAAG,CAAC,oCAAoC,CAAC;MACrD;IACJ;EACJ,CAAC,EAAE,CAAC5F,UAAU,EAAEnB,IAAI,EAAE2G,MAAM,CAACK,SAAS,EAAEpG,SAAS,EAAEf,MAAM,CAAC,CAAC;EAE3DhC,SAAS,CAAC,MAAM;IACZ,IAAIoD,IAAI,EAAE;IACV,IAAI,CAACY,aAAa,EAAEX,OAAO,CAAC,IAAI,CAAC;EACrC,CAAC,EAAE,CAACW,aAAa,CAAC,CAAC;EAEnB,MAAM2G,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAInH,eAAe,CAACG,OAAO,EAAE;MACzBkE,OAAO,CAACC,IAAI,CAAC,sCAAsC,CAAC;MACpD;IACJ;IACAtE,eAAe,CAACG,OAAO,GAAG,IAAI,CAAC,CAAC;IAChCkE,OAAO,CAACqB,GAAG,CAAC,qBAAqB,EAAEnG,SAAS,CAAC;IAC7C,IAAI,CAACA,SAAS,EAAE;MACZ8E,OAAO,CAACqB,GAAG,CAAC,8EAA8E,CAAC;MAC3F;IACJ;IAEArB,OAAO,CAACqB,GAAG,CAAC,6BAA6B,EAAEnG,SAAS,CAAC;IACrDd,QAAQ,CAACZ,gBAAgB,CAAC,IAAIiD,GAAG,CAAC,CAAC,CAAC,CAAC;IACrCmB,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACA;MACA,MAAMmF,MAAM,GAAG,MAAM3I,QAAQ,CAACb,UAAU,CAAC2B,SAAS,CAAC,CAAC,CAAC8H,MAAM,CAAC,CAAC;MAC7DhD,OAAO,CAACqB,GAAG,CAAC,qBAAqB,EAAE0B,MAAM,CAAC;;MAE1C;MACA3I,QAAQ,CAAC1B,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;;MAElD;MACA,IAAI;QACAU,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,OAAO2G,GAAG,EAAE;QACV;QACAC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAChE;MAEA,MAAMkD,aAAa,GAAG7H,UAAU,CAACU,OAAO;MACxC,MAAMoH,WAAW,GAAGtH,OAAO,CAACE,OAAO;MAEnC,IAAI,CAACmH,aAAa,EAAE;QAChBjD,OAAO,CAACkC,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACJ;;MAEA;MACAlC,OAAO,CAACqB,GAAG,CAAC,qBAAqB,EAAE6B,WAAW,CAAC;MAC/ClD,OAAO,CAACqB,GAAG,CAAC,aAAa,EAAE4B,aAAa,CAAC;MAEzC,IAAI,CAACC,WAAW,IAAI,CAACA,WAAW,CAACC,gBAAgB,EAAE;QAC/CnD,OAAO,CAACqB,GAAG,CAAC,+BAA+B,EAAE;UACzC+B,QAAQ,EAAE,CAACF,WAAW;UACtBG,aAAa,EAAEH,WAAW,IAAI,CAACA,WAAW,CAACC;QAC/C,CAAC,CAAC;QACF9I,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACpC;MACJ;MAEAE,QAAQ,2BAAA2B,MAAA,CAA2BiH,aAAa,WAAQ,CAAC;IAC7D,CAAC,CAAC,OAAOf,KAAK,EAAE;MACZlC,OAAO,CAACkC,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCtE,gBAAgB,CAAC,KAAK,CAAC;MACvBxD,QAAQ,CAAC3B,eAAe,CAAC,oCAAoC,CAAC,CAAC;MAC/DkD,eAAe,CAACG,OAAO,GAAG,KAAK,CAAC,CAAC;;MAEjC;MACA6F,UAAU,CAAC,MAAM;QACb,IAAI,CAAChE,aAAa,IAAIvC,UAAU,CAACU,OAAO,EAAE;UACtCkE,OAAO,CAACqB,GAAG,CAAC,4BAA4B,CAAC;UACzCyB,gBAAgB,CAAC,CAAC;QACtB;MACJ,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAGjL,WAAW,CAAE6G,UAAU,IAAK;IACnD/B,mBAAmB,CAAC+B,UAAU,CAAC;;IAE/B;IACA,IAAIrD,cAAc,CAACC,OAAO,IAAID,cAAc,CAACC,OAAO,CAACyH,oBAAoB,CAAC,CAAC,EAAE;MACzE;MACA1H,cAAc,CAACC,OAAO,CAAC0H,gBAAgB,CAACtE,UAAU,CAAC;IACvD,CAAC,MAAM;MACH;MACA;MACAyC,UAAU,CAAC,MAAM;QACb;QACA,MAAM8B,OAAO,GAAG5E,QAAQ,CAAC6E,aAAa,wBAAA1H,MAAA,CAAuBkD,UAAU,QAAI,CAAC;QAE5E,IAAIuE,OAAO,EAAE;UACT,MAAME,MAAM,GAAG,EAAE,CAAC,CAAC;UACnB,MAAMC,CAAC,GAAGH,OAAO,CAACI,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;UACvEI,MAAM,CAACE,QAAQ,CAAC;YAAEH,GAAG,EAAEF,CAAC;YAAEM,QAAQ,EAAE;UAAS,CAAC,CAAC;QACnD,CAAC,MAAM;UACH;UACA,MAAMC,UAAU,GAAGpJ,YAAY,CAACe,OAAO,CAACoD,UAAU,CAAC;UAEnD,IAAIiF,UAAU,EAAE;YACZ,MAAMR,MAAM,GAAG,EAAE,CAAC,CAAC;YACnB,MAAMC,CAAC,GAAGO,UAAU,CAACN,qBAAqB,CAAC,CAAC,CAACC,GAAG,GAAGC,MAAM,CAACC,OAAO,GAAGL,MAAM;YAC1EI,MAAM,CAACE,QAAQ,CAAC;cAAEH,GAAG,EAAEF,CAAC;cAAEM,QAAQ,EAAE;YAAS,CAAC,CAAC;UACnD;QACJ;MACJ,CAAC,EAAE,CAAC,CAAC;IACT;EACJ,CAAC,EAAE,CAACnJ,YAAY,EAAEc,cAAc,CAAC,CAAC;;EAElC;EACA,MAAMuI,gBAAgB,GAAGd,kBAAkB;EAE3C,MAAMe,oBAAoB,GAAGA,CAACnF,UAAU,EAAEoF,WAAW,EAAEC,IAAI,KAAK;IAC5D;IACA,IAAIxG,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAM+L,OAAO,GAAG;MACZtJ,SAAS;MACTgE,UAAU;MACVuF,aAAa,EAAEH,WAAW;MAC1BnD,SAAS,EAAElF,IAAI,CAACmF,EAAE;MAAE;MACpBmD,IAAI;MACJpK,MAAM;MACNuK,IAAI,EAAEzI,IAAI,CAAC0I,QAAQ,GAAG,GAAG,GAAG1I,IAAI,CAAC2I;IACrC,CAAC;IACD,MAAMC,SAAS,GAAG;MACd3F,UAAU;MACVuF,aAAa,EAAEH,WAAW;MAC1BQ,cAAc,EAAEP;IACpB,CAAC;IACDnK,QAAQ,CAACvB,UAAU,CAACgM,SAAS,CAAC,CAAC;IAE/B5D,MAAM,CAACC,IAAI,CAAC,eAAe,EAAEsD,OAAO,CAAC;EACzC,CAAC;EAED,MAAMO,oBAAoB,GAAGA,CAAC7F,UAAU,EAAEoF,WAAW,EAAEU,cAAc,KAAK;IACtE;IACA,IAAIjH,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,MAAMwM,cAAc,GAAGtG,SAAS,CAACO,UAAU,CAAC,IAAI,EAAE;IAElD,MAAMgG,QAAQ,GAAGD,cAAc,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACd,WAAW,KAAKA,WAAW,CAAC;;IAE5E;IACA,IAAIY,QAAQ,IAAIA,QAAQ,CAACG,MAAM,KAAKL,cAAc,EAAE;MAChD;IACJ;IAEA,MAAMM,cAAc,GAAGL,cAAc,CAACM,GAAG,CAACH,GAAG,IACzCA,GAAG,CAACd,WAAW,KAAKA,WAAW,GACzB;MAAE,GAAGc,GAAG;MAAEC,MAAM,EAAEL;IAAe,CAAC,GAClCI,GACV,CAAC;;IAED;IACA,IAAI,CAACF,QAAQ,EAAE;MACXI,cAAc,CAACE,IAAI,CAAC;QAAElB,WAAW;QAAEe,MAAM,EAAEL;MAAe,CAAC,CAAC;IAChE;;IAEA;IACA/D,MAAM,CAACC,IAAI,CAAC,eAAe,EAAE;MACzBhC,UAAU;MACVuF,aAAa,EAAEa,cAAc;MAC7BnE,SAAS,EAAElF,IAAI,CAACmF,EAAE;MAClBlG,SAAS;MACTqJ,IAAI,EAAE,IAAI;MACVpK,MAAM;MACNuK,IAAI,EAAEzI,IAAI,CAAC0I,QAAQ,GAAG,GAAG,GAAG1I,IAAI,CAAC2I;IACrC,CAAC,CAAC;IACFxK,QAAQ,CAACvB,UAAU,CAAC;MAAEqG,UAAU;MAAEuF,aAAa,EAAEhH,IAAI,CAACgI,SAAS,CAACH,cAAc,CAAC;MAAER,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC;EAC7G,CAAC;EAGD,MAAMY,qBAAqB,GAAGA,CAACxG,UAAU,EAAEuF,aAAa,EAAEF,IAAI,KAAK;IAC/D;IACA,IAAIxG,QAAQ,EAAE;MACV3D,QAAQ,CAAC3B,eAAe,CAAC,2DAA2D,CAAC,CAAC;MACtF;IACJ;IAEA,IAAI,CAACgM,aAAa,IAAIA,aAAa,CAACkB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/C;IACJ;IAEA,MAAMnB,OAAO,GAAG;MACZtJ,SAAS;MACTgE,UAAU;MACVuF,aAAa,EAAEA,aAAa,CAACkB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MACrDzE,SAAS,EAAElF,IAAI,CAACmF,EAAE;MAClBmD,IAAI;MACJpK,MAAM;MACNuK,IAAI,EAAEzI,IAAI,CAAC0I,QAAQ,GAAG,GAAG,GAAG1I,IAAI,CAAC2I;IACrC,CAAC;IAEDxK,QAAQ,CAACvB,UAAU,CAAC;MAAEqG,UAAU;MAAEuF,aAAa;MAAEK,cAAc,EAAEP;IAAK,CAAC,CAAC,CAAC;IAEzEtD,MAAM,CAACC,IAAI,CAAC,eAAe,EAAEsD,OAAO,CAAC;EACzC,CAAC;;EAED;EACA,MAAMqB,sBAAsB,GAAGzN,MAAM,CAAC,IAAIqE,GAAG,CAAC,CAAC,CAAC;;EAEhD;EACAtE,SAAS,CAAC,MAAM;IACZ,IAAI0N,sBAAsB,CAAC/J,OAAO,CAACgK,IAAI,GAAG,CAAC,EAAE;MACzCD,sBAAsB,CAAC/J,OAAO,CAACiK,OAAO,CAAC7G,UAAU,IAAI;QACjD,IAAI,CAAC9C,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;UAChCD,WAAW,CAACC,UAAU,CAAC;QAC3B;MACJ,CAAC,CAAC;MACF2G,sBAAsB,CAAC/J,OAAO,CAACkK,KAAK,CAAC,CAAC;IAC1C;EACJ,CAAC,EAAE,CAAC5J,aAAa,EAAE6C,WAAW,CAAC,CAAC;;EAEhC;EACA9G,SAAS,CAAC,MAAM;IACZ;IACA,MAAM8N,OAAO,GAAGC,qBAAqB,CAAC,MAAM;MACxC,IAAIL,sBAAsB,CAAC/J,OAAO,CAACgK,IAAI,GAAG,CAAC,EAAE;QACzC,MAAMK,WAAW,GAAG,CAAC,GAAGN,sBAAsB,CAAC/J,OAAO,CAAC;QACvD+J,sBAAsB,CAAC/J,OAAO,CAACkK,KAAK,CAAC,CAAC;;QAEtC;QACAG,WAAW,CAACJ,OAAO,CAAC7G,UAAU,IAAI;UAC9B,IAAI,CAAC9C,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;YAChCD,WAAW,CAACC,UAAU,CAAC;UAC3B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IAEF,OAAO,MAAMkH,oBAAoB,CAACH,OAAO,CAAC;EAC9C,CAAC,CAAC;EAEF,MAAMI,YAAY,GAAGhO,WAAW,CAAC,CAAC6G,UAAU,EAAEoF,WAAW,KAAK;IAC1D,MAAMgC,UAAU,GAAG/H,QAAQ,CAACgI,IAAI,CAC3BnB,GAAG,IACAA,GAAG,CAAClG,UAAU,KAAKA,UAAU,IAC7BkG,GAAG,CAACX,aAAa,IACjB/D,MAAM,CAAC0E,GAAG,CAACX,aAAa,CAAC,KAAK/D,MAAM,CAAC4D,WAAW,CACxD,CAAC;;IAED;IACA,IAAIgC,UAAU,IAAI,CAAClK,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;MAC9C2G,sBAAsB,CAAC/J,OAAO,CAACsD,GAAG,CAACF,UAAU,CAAC;IAClD;IAEA,OAAOoH,UAAU;EACrB,CAAC,EAAE,CAAC/H,QAAQ,EAAEnC,aAAa,CAAC,CAAC;EAE7B,MAAMoK,WAAW,GAAGnO,WAAW,CAAC,CAAC6G,UAAU,EAAEoF,WAAW,EAAEmC,IAAI,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAC/D,MAAML,UAAU,GAAG,EAAAI,qBAAA,GAAA/H,SAAS,CAACO,UAAU,CAAC,cAAAwH,qBAAA,uBAArBA,qBAAA,CAAuBH,IAAI,CACzCK,CAAC,IAAKA,CAAC,CAACtC,WAAW,KAAKA,WAAW,IAAIsC,CAAC,CAACvB,MAAM,KAAKoB,IACzD,CAAC,KAAI,KAAK;;IAEV;IACA,IAAIH,UAAU,IAAI,CAAClK,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,IAAI,EAAAyH,sBAAA,GAAAhI,SAAS,CAACO,UAAU,CAAC,cAAAyH,sBAAA,uBAArBA,sBAAA,CAAuBE,MAAM,MAAK,CAAC,EAAE;MACrFhB,sBAAsB,CAAC/J,OAAO,CAACsD,GAAG,CAACF,UAAU,CAAC;IAClD;IAEA,OAAOoH,UAAU;EACrB,CAAC,EAAE,CAAC3H,SAAS,EAAEvC,aAAa,CAAC,CAAC;EAE9B,MAAM0K,kBAAkB,GAAGzO,WAAW,CAAE6G,UAAU,IAAK;IAAA,IAAA6H,qBAAA;IACnD,MAAMC,OAAO,GAAGvI,SAAS,CAAC0G,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAAClG,UAAU,KAAKA,UAAU,CAAC;IACtE,MAAM+H,OAAO,GAAG,CAAAD,OAAO,aAAPA,OAAO,wBAAAD,qBAAA,GAAPC,OAAO,CAAEvC,aAAa,cAAAsC,qBAAA,uBAAtBA,qBAAA,CAAwBnB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAI,EAAE;;IAEnE;IACA,IAAIqB,OAAO,IAAI,CAAC7K,aAAa,CAACyD,GAAG,CAACX,UAAU,CAAC,EAAE;MAC3C2G,sBAAsB,CAAC/J,OAAO,CAACsD,GAAG,CAACF,UAAU,CAAC;IAClD;IAEA,OAAO+H,OAAO;EAClB,CAAC,EAAE,CAACxI,SAAS,EAAErC,aAAa,CAAC,CAAC;;EAE9B;EACA;EACA;EACA;EACA;;EAEAjE,SAAS,CAAC,MAAM;IACZ,IAAIsC,SAAS,EAAE;MACXyD,aAAa,CAACzD,SAAS,CAACyM,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAACrC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/E1G,aAAa,CAAC3D,SAAS,CAACyM,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAACrC,cAAc,KAAK,IAAI,CAAC,CAAC;MAC/ExG,cAAc,CAAC7D,SAAS,CAACyM,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAACrC,cAAc,KAAK,KAAK,CAAC,CAAC;IACrF;EACJ,CAAC,EAAE,CAACrK,SAAS,CAAC,CAAC;EAEftC,SAAS,CAAC,MAAM;IACZ;IACA,IAAI,CAACkH,KAAK,CAAC+H,OAAO,CAAC1M,OAAO,CAAC,IAAIA,OAAO,CAACmM,MAAM,KAAK,CAAC,EAAE;IAErD,MAAMQ,EAAE,GAAG,EAAE;IACb,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,KAAK,GAAG,CAAC,CAAC;;IAEhB;IACA,KAAK,MAAMlC,MAAM,IAAI3K,OAAO,EAAE;MAC1B,IAAI2K,MAAM,CAACP,cAAc,KAAK,IAAI,EAAE;QAChCuC,EAAE,CAAC7B,IAAI,CAACH,MAAM,CAAC;MACnB,CAAC,MAAM,IAAIA,MAAM,CAACP,cAAc,KAAK,KAAK,EAAE;QACxCwC,GAAG,CAAC9B,IAAI,CAACH,MAAM,CAAC;MACpB,CAAC,MAAM,IAAIA,MAAM,CAACP,cAAc,KAAK,IAAI,IAAIO,MAAM,CAACZ,aAAa,EAAE;QAC/D,IAAI;UACA,MAAM+C,MAAM,GAAG/J,IAAI,CAACC,KAAK,CAAC2H,MAAM,CAACZ,aAAa,CAAC;UAC/C8C,KAAK,CAAClC,MAAM,CAACnG,UAAU,CAAC,GAAGsI,MAAM;QACrC,CAAC,CAAC,OAAOzH,GAAG,EAAE;UACVC,OAAO,CAACkC,KAAK,CAAC,6BAA6B,EAAEnC,GAAG,CAAC;QACrD;MACJ;IACJ;IAEAvB,WAAW,CAAC6I,EAAE,CAAC;IACf3I,YAAY,CAAC4I,GAAG,CAAC;IACjB1I,YAAY,CAAC2I,KAAK,CAAC;IACnB,IAAI,CAACtG,MAAM,IAAI,CAACA,MAAM,CAACK,SAAS,IAAI,CAACpG,SAAS,IAAI,CAACf,MAAM,EAAE;IAC3D8G,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAE;MAC3BhG,SAAS;MACTR,OAAO;MACPP,MAAM;MACNsN,OAAO,EAAExL;IACb,CAAC,CAAC;EACN,CAAC,EAAE,CAACvB,OAAO,CAAC,CAAC;EAGbvC,SAAS,CAAC,MAAM;IACZ,IAAI+C,SAAS,EAAE;MACXd,QAAQ,CAACxB,qBAAqB,CAACsC,SAAS,CAAC,CAAC;IAC9C;EACJ,CAAC,EAAE,CAACd,QAAQ,EAAEc,SAAS,CAAC,CAAC;EAEzB/C,SAAS,CAAC,MAAM;IACZ,IAAI,EAACmC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgI,YAAY,KAAInG,aAAa,KAAK,IAAI,IAAI,CAACnB,OAAO,EAAE;;IAE/D;IACA,MAAM0M,iBAAiB,GAAIC,IAAI,IAAK;MAChC;MACA,IAAIA,IAAI,KAAK,GAAG,IAAI,CAACjL,gBAAgB,CAACE,WAAW,EAAE;QAC/CD,mBAAmB,CAACiD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEhD,WAAW,EAAE;QAAK,CAAC,CAAC,CAAC;QAC7DG,iBAAiB,CAAC,IAAI,CAAC;QACvB3C,QAAQ,CAAC3B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;QACAkJ,UAAU,CAAC,MAAM;UACb5E,iBAAiB,CAAC,KAAK,CAAC;QAC5B,CAAC,EAAE,KAAK,CAAC;MACb;;MAEA;MACA,IAAI4K,IAAI,KAAK,EAAE,IAAI,CAACjL,gBAAgB,CAACG,SAAS,EAAE;QAC5CF,mBAAmB,CAACiD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE/C,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC;QAC3DE,iBAAiB,CAAC,IAAI,CAAC;QACvB3C,QAAQ,CAAC3B,eAAe,CAAC,0CAA0C,CAAC,CAAC;;QAErE;MACJ;IACJ,CAAC;;IAED;IACA,MAAMmP,gBAAgB,GAAGC,WAAW,CAAC,MAAM;MACvC,IAAI5G,MAAM,CAACK,SAAS,IAAIpG,SAAS,EAAE;QAC/B+F,MAAM,CAACC,IAAI,CAAC,cAAc,EAAE;UAAE/G,MAAM;UAAEe;QAAU,CAAC,CAAC;MACtD;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,MAAM4M,QAAQ,GAAGD,WAAW,CAAC,MAAM;MAC/BzN,QAAQ,CAACd,gBAAgB,CAAEsG,IAAI,IAAK;QAChC,IAAIA,IAAI,IAAI,CAAC,EAAE;UAAE;UACbmI,aAAa,CAACD,QAAQ,CAAC;UACvBC,aAAa,CAACH,gBAAgB,CAAC;UAC/B;UACA5J,WAAW,CAAC,IAAI,CAAC;UACjBjB,iBAAiB,CAAC,KAAK,CAAC;UACxB;UACA+F,gBAAgB,CAAC,CAAC;UAClB,OAAO,CAAC;QACZ;;QAEA;QACA4E,iBAAiB,CAAC9H,IAAI,CAAC;QAEvB,OAAOA,IAAI,GAAG,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM;MACTmI,aAAa,CAACD,QAAQ,CAAC;MACvBC,aAAa,CAACH,gBAAgB,CAAC;IACnC,CAAC;EACL,CAAC,EAAE,CAACtN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,YAAY,EAAEtH,OAAO,EAAEmB,aAAa,EAAEO,gBAAgB,EAAEtC,QAAQ,EAAE6G,MAAM,EAAE/F,SAAS,EAAEf,MAAM,CAAC,CAAC,CAAC;;EAExGhC,SAAS,CAAC,MAAM;IACZ,IAAI6C,OAAO,IAAI,CAACiG,MAAM,CAACK,SAAS,EAAE;MAC9BL,MAAM,CAACM,OAAO,CAAC,CAAC;IACpB;IACA,OAAO,MAAM;MACTN,MAAM,CAAC+G,UAAU,CAAC,CAAC;IACvB,CAAC;EACL,CAAC,EAAE,CAAChN,OAAO,CAAC,CAAC;;EAEb;EACA7C,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC+C,SAAS,IAAI,EAACe,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmF,EAAE,KAAI,CAACjH,MAAM,IAAIe,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK+M,SAAS,EAAE;IACzF,IAAI,EAAC3N,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0H,sBAAsB,GAAE;IACnChC,OAAO,CAACqB,GAAG,CAAC,kCAAkC,CAAC;IAG/C,MAAM6G,UAAU,GAAG,IAAIzL,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9B,MAAM0L,OAAO,GAAGA,CAACC,GAAG,EAAE5D,OAAO,KAAK;MAE9B,IAAI,EAAClK,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0H,sBAAsB,KAAIkG,UAAU,CAACrI,GAAG,CAACuI,GAAG,CAAC,EAAE;MAE1DF,UAAU,CAAC9I,GAAG,CAACgJ,GAAG,CAAC;MACnBnH,MAAM,CAACC,IAAI,CAAC,UAAU,EAAE;QAAE,GAAGsD,OAAO;QAAEE,IAAI,EAAEzI,IAAI,CAAC0I,QAAQ,GAAG,GAAG,GAAG1I,IAAI,CAAC2I;MAAU,CAAC,CAAC;MAEnFjD,UAAU,CAAC,MAAMuG,UAAU,CAACxI,MAAM,CAAC0I,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC;;IAED;IACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAACxJ,QAAQ,CAACyJ,iBAAiB,IAC3B,CAACzJ,QAAQ,CAAC0J,uBAAuB,IACjC,CAAC1J,QAAQ,CAAC2J,oBAAoB,IAC9B,CAAC3J,QAAQ,CAAC4J,mBAAmB,EAAE;QAC/BN,OAAO,CAAC,iBAAiB,EAAE;UACvBhH,SAAS,EAAElF,IAAI,CAACmF,EAAE;UAClBlG,SAAS;UACTf,MAAM;UACNuO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,iBAAiB;UACzBC,MAAM,EAAEnL,IAAI,CAACgI,SAAS,CAAC;YAAEoD,MAAM,EAAE;UAA8B,CAAC;QACpE,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACjC,IAAIjK,QAAQ,CAACkK,eAAe,KAAK,QAAQ,EAAE;QACvCZ,OAAO,CAAC,UAAU,EAAE;UAChBhH,SAAS,EAAElF,IAAI,CAACmF,EAAE;UAClBlG,SAAS;UACTf,MAAM;UACNuO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,UAAU;UAClBC,MAAM,EAAEnL,IAAI,CAACgI,SAAS,CAAC;YAAE/D,OAAO,EAAE;UAAwC,CAAC;QAC/E,CAAC,CAAC;MACN;IACJ,CAAC;;IAED;IACA,MAAMsH,UAAU,GAAGA,CAAA,KAAM;MACrBb,OAAO,CAAC,eAAe,EAAE;QACrBhH,SAAS,EAAElF,IAAI,CAACmF,EAAE;QAClBlG,SAAS;QACTf,MAAM;QACNuO,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,eAAe;QACvBC,MAAM,EAAEnL,IAAI,CAACgI,SAAS,CAAC;UAAE/D,OAAO,EAAE;QAAsB,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC;;IAED;IACA,MAAMuH,mBAAmB,GAAIlK,CAAC,IAAK;MAC/B,MAAMmK,cAAc,GAAG,CACnB,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CACjE;MACD,MAAMC,KAAK,MAAAnN,MAAA,CAAM+C,CAAC,CAACqK,OAAO,GAAG,OAAO,GAAG,EAAE,EAAApN,MAAA,CAAG+C,CAAC,CAACsK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAArN,MAAA,CAAG+C,CAAC,CAACuK,MAAM,GAAG,MAAM,GAAG,EAAE,EAAAtN,MAAA,CAAG+C,CAAC,CAACwK,OAAO,GAAG,OAAO,GAAG,EAAE,EAAAvN,MAAA,CAAG+C,CAAC,CAACqJ,GAAG,CAAE;MAEpI,IACIc,cAAc,CAACM,QAAQ,CAACzK,CAAC,CAACqJ,GAAG,CAAC,IAC9Be,KAAK,KAAK,cAAc,IACxBA,KAAK,KAAK,cAAc,EAC1B;QACEhB,OAAO,QAAAnM,MAAA,CAAQmN,KAAK,GAAI;UACpBhI,SAAS,EAAElF,IAAI,CAACmF,EAAE;UAClBlG,SAAS;UACTf,MAAM;UACNuO,IAAI,EAAE,IAAI;UACVC,MAAM,EAAE,gBAAgB;UACxBC,MAAM,EAAEnL,IAAI,CAACgI,SAAS,CAAC;YAAE2C,GAAG,EAAErJ,CAAC,CAACqJ,GAAG;YAAEM,IAAI,EAAE3J,CAAC,CAAC2J,IAAI;YAAES;UAAM,CAAC;QAC9D,CAAC,CAAC;MACN;IACJ,CAAC;IAEDtK,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEuJ,sBAAsB,CAAC;IACrExJ,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAEgK,sBAAsB,CAAC;IACrEjK,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEkK,UAAU,CAAC;IAC7CnK,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEmK,mBAAmB,CAAC;IAEzD,OAAO,MAAM;MACTpK,QAAQ,CAAC4K,mBAAmB,CAAC,kBAAkB,EAAEpB,sBAAsB,CAAC;MACxExJ,QAAQ,CAAC4K,mBAAmB,CAAC,kBAAkB,EAAEX,sBAAsB,CAAC;MACxEjK,QAAQ,CAAC4K,mBAAmB,CAAC,MAAM,EAAET,UAAU,CAAC;MAChDnK,QAAQ,CAAC4K,mBAAmB,CAAC,SAAS,EAAER,mBAAmB,CAAC;IAChE,CAAC;EACL,CAAC,EAAE,CAAChI,MAAM,EAAEhF,IAAI,CAACmF,EAAE,EAAEjH,MAAM,EAAEe,SAAS,CAAC,CAAC;EAGxC/C,SAAS,CAAC,MAAM;IACZ;IACA,MAAMuR,iBAAiB,GAAGC,KAAA,IAAoB;MAAA,IAAnB;QAAEzK;MAAW,CAAC,GAAAyK,KAAA;MACrC1K,WAAW,CAACC,UAAU,CAAC;MACvBK,mBAAmB,CAACL,UAAU,CAAC;IACnC,CAAC;IAED,MAAM0K,iBAAiB,GAAGC,KAAA,IAA6B;MAAA,IAA5B;QAAE3K,UAAU;QAAEwC;MAAQ,CAAC,GAAAmI,KAAA;MAC9CzP,QAAQ,CAAC3B,eAAe,CAACiJ,OAAO,CAAC,CAAC;MAClCjC,cAAc,CAACP,UAAU,CAAC;MAC1BM,gBAAgB,CAACN,UAAU,CAAC;IAChC,CAAC;;IAED;IACA,MAAM4K,eAAe,GAAGC,KAAA,IAA4C;MAAA,IAA3C;QAAE5N,aAAa,EAAE6N;MAAoB,CAAC,GAAAD,KAAA;MAC3D/J,OAAO,CAACqB,GAAG,CAAC,2BAA2B,EAAE2I,mBAAmB,CAAC;MAC7D5P,QAAQ,CAACd,gBAAgB,CAAC0Q,mBAAmB,CAAC,CAAC;IACnD,CAAC;;IAED;IACA,MAAMC,uBAAuB,GAAGC,KAAA,IAAwD;MAAA,IAAvD;QAAExI,OAAO;QAAExG,SAAS,EAAEiP,mBAAmB;QAAEC;MAAM,CAAC,GAAAF,KAAA;MAC/ElK,OAAO,CAACqB,GAAG,CAAC,yBAAyB,EAAE;QAAEK,OAAO;QAAEyI,mBAAmB;QAAEC;MAAM,CAAC,CAAC;MAC/EhQ,QAAQ,CAAC1B,iBAAiB,CAACgJ,OAAO,CAAC,CAAC;MACpC1D,WAAW,CAAC,IAAI,CAAC;;MAEjB;MACA,IAAI;QACA5E,cAAc,CAAC,CAAC;MACpB,CAAC,CAAC,OAAO2G,GAAG,EAAE;QACVC,OAAO,CAACC,IAAI,CAAC,qDAAqD,EAAEF,GAAG,CAAC;MAC5E;;MAEA;MACA,IAAIzF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6I,gBAAgB,EAAE;QACxB9I,QAAQ,2BAAA2B,MAAA,CAA2BmO,mBAAmB,WAAQ,CAAC;MACnE,CAAC,MAAM;QACH9P,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;MACxC;IACJ,CAAC;;IAED;IACA,MAAMkQ,sBAAsB,GAAGC,KAAA,IAAiB;MAAA,IAAhB;QAAE5I;MAAQ,CAAC,GAAA4I,KAAA;MACvCtK,OAAO,CAACqB,GAAG,CAAC,iBAAiB,EAAEK,OAAO,CAAC;MACvCtH,QAAQ,CAAC1B,iBAAiB,CAACgJ,OAAO,CAAC,CAAC;IACxC,CAAC;;IAED;IACAT,MAAM,CAACmB,EAAE,CAAC,cAAc,EAAEsH,iBAAiB,CAAC;IAC5CzI,MAAM,CAACmB,EAAE,CAAC,cAAc,EAAEwH,iBAAiB,CAAC;IAC5C3I,MAAM,CAACmB,EAAE,CAAC,YAAY,EAAE0H,eAAe,CAAC;IACxC7I,MAAM,CAACmB,EAAE,CAAC,qBAAqB,EAAE6H,uBAAuB,CAAC;IACzDhJ,MAAM,CAACmB,EAAE,CAAC,mBAAmB,EAAEiI,sBAAsB,CAAC;IAEtD,OAAO,MAAM;MACT;MACApJ,MAAM,CAACoB,GAAG,CAAC,cAAc,EAAEqH,iBAAiB,CAAC;MAC7CzI,MAAM,CAACoB,GAAG,CAAC,cAAc,EAAEuH,iBAAiB,CAAC;MAC7C3I,MAAM,CAACoB,GAAG,CAAC,YAAY,EAAEyH,eAAe,CAAC;MACzC7I,MAAM,CAACoB,GAAG,CAAC,qBAAqB,EAAE4H,uBAAuB,CAAC;MAC1DhJ,MAAM,CAACoB,GAAG,CAAC,mBAAmB,EAAEgI,sBAAsB,CAAC;IAC3D,CAAC;EACL,CAAC,EAAE,CAAC/P,IAAI,EAAEH,MAAM,EAAEE,QAAQ,EAAED,QAAQ,EAAE6E,WAAW,EAAEQ,cAAc,EAAEF,mBAAmB,EAAEC,gBAAgB,CAAC,CAAC;EAE1GrH,SAAS,CAAC,MAAM;IACZoF,YAAY,CAACgN,OAAO,CAAC,YAAY,EAAE9M,IAAI,CAACgI,SAAS,CAACrI,UAAU,CAAC,CAAC;EAClE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMoN,aAAa,GAAGnS,WAAW,CAAE0G,CAAC,IAAK;IACrC;IACA,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAACyK,QAAQ,CAACzK,CAAC,CAACqJ,GAAG,CAAC,EAAE;MACrE;MACArJ,CAAC,CAACC,cAAc,CAAC,CAAC;;MAElB;MACA,IAAI,CAACvE,SAAS,IAAIA,SAAS,CAACoM,MAAM,KAAK,CAAC,EAAE;MAE1C,MAAM4D,YAAY,GAAG,CAAC,GAAGxM,UAAU,EAAE,GAAGE,UAAU,EAAE,GAAGE,WAAW,CAAC;MACnE,MAAMqM,YAAY,GAAGD,YAAY,CAACE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxJ,EAAE,KAAKlE,gBAAgB,CAAC;MAE3E,IAAI,CAAC6B,CAAC,CAACqJ,GAAG,KAAK,SAAS,IAAIrJ,CAAC,CAACqJ,GAAG,KAAK,WAAW,KAAKsC,YAAY,GAAG,CAAC,EAAE;QACpE,MAAMG,cAAc,GAAGJ,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACtJ,EAAE;QACxDpB,OAAO,CAACqB,GAAG,CAAC,+CAA+C,EAAEwJ,cAAc,CAAC;QAC5EvH,kBAAkB,CAACuH,cAAc,CAAC;MACtC,CAAC,MAAM,IAAI,CAAC9L,CAAC,CAACqJ,GAAG,KAAK,WAAW,IAAIrJ,CAAC,CAACqJ,GAAG,KAAK,YAAY,KAAKsC,YAAY,GAAGD,YAAY,CAAC5D,MAAM,GAAG,CAAC,EAAE;QACpG,MAAMiE,cAAc,GAAGL,YAAY,CAACC,YAAY,GAAG,CAAC,CAAC,CAACtJ,EAAE;QACxDpB,OAAO,CAACqB,GAAG,CAAC,kDAAkD,EAAEyJ,cAAc,CAAC;QAC/ExH,kBAAkB,CAACwH,cAAc,CAAC;MACtC;IACJ;EACJ,CAAC,EAAE,CAACrQ,SAAS,EAAEwD,UAAU,EAAEE,UAAU,EAAEE,WAAW,EAAEnB,gBAAgB,EAAEoG,kBAAkB,CAAC,CAAC;EAC1F;EACAnL,SAAS,CAAC,MAAM;IACZ0G,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE0L,aAAa,CAAC;IACnD,OAAO,MAAM;MACT3L,QAAQ,CAAC4K,mBAAmB,CAAC,SAAS,EAAEe,aAAa,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,oBACIzQ,OAAA;IAAKgR,SAAS,0BAAA/O,MAAA,CAA0BoB,UAAU,GAAG,yBAAyB,GAAG,uBAAuB,CAAG;IAAA4N,QAAA,gBACvGjR,OAAA,CAAChC,gBAAgB;MAACkT,QAAQ,EAAE3Q,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoK,IAAK;MAACwG,gBAAgB,EAAEpL,oBAAqB;MAAC1C,UAAU,EAAE,CAACA;IAAW;MAAA+N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC1GtQ,OAAO,gBACJjB,OAAA;MAAKgR,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAEhFjR,OAAA,CAACb,WAAW;QACRqS,GAAG,EAAE1P,cAAe;QACpB2P,QAAQ,EAAE3N,eAAgB;QAC1BT,UAAU,EAAEA,UAAW;QACvBa,UAAU,EAAEA,UAAW;QACvBE,UAAU,EAAEA,UAAW;QACvBE,WAAW,EAAEA,WAAY;QACzBoN,QAAQ,EAAE;UACNpH,oBAAoB;UACpBU,oBAAoB;UACpBW,qBAAqB;UACrBW,YAAY;UACZG,WAAW;UACXM,kBAAkB;UAClB4E,cAAc,EAAEA,CAACtK,EAAE,EAAEuK,EAAE,KAAM5Q,YAAY,CAACe,OAAO,CAACsF,EAAE,CAAC,GAAGuK,EAAG;UAC3DxO,mBAAmB,EAAGiE,EAAE,IAAKjE,mBAAmB,CAACiE,EAAE;QACvD,CAAE;QACFwK,QAAQ,EAAE;UACN1O,gBAAgB;UAChBE,UAAU;UACVzC,QAAQ;UACRE,SAAS;UACTmC,iBAAiB;UACjBC,iBAAiB;UACjBc,QAAQ;UACRxB,eAAe;UACfoD;QACJ,CAAE;QACF5B,QAAQ,EAAEA;QACV;QAAA;QACA8N,iBAAiB,EAAE5D,SAAU;QAC7BnF,gBAAgB,EAAEA,gBAAiB;QACnCnF,aAAa,EAAEA;MAAc;QAAAwN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAIFvR,OAAA;QAAKgR,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eAClDjR,OAAA;UACIgR,SAAS,gCAAA/O,MAAA,CAAgCoB,UAAU,GAAG,wBAAwB,GAAG,qBAAqB,CAAG;UACzG0O,OAAO,EAAEA,CAAA,KAAMxQ,gBAAgB,CAACsE,IAAI,IAAI,CAACA,IAAI,CAAE;UAAAoL,QAAA,eAE/CjR,OAAA,CAACf,IAAI;YAAAmS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGNvR,OAAA,CAAChB,eAAe;QAAAiS,QAAA,EACX,CAAC3P,aAAa,IAAI0I,MAAM,CAACgI,UAAU,GAAG,IAAI,kBACvChS,OAAA,CAACd,WAAW;UACRmE,UAAU,EAAEA,UAAW;UACvBC,aAAa,EAAEA,aAAc;UAC7B1C,QAAQ,EAAEA,QAAS;UACnBuF,oBAAoB,EAAEA,oBAAqB;UAC3CrF,SAAS,EAAEA,SAAU;UACrByF,qBAAqB,EAAEA,qBAAsB;UAC7CrC,UAAU,EAAEA,UAAW;UACvBE,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzB+F,gBAAgB,EAAEd,kBAAmB;UACrCpG,gBAAgB,EAAEA,gBAAiB;UACnCX,eAAe,EAAEA,eAAgB;UACjCoD,kBAAkB,EAAEA,kBAAmB;UACvCmD,gBAAgB,EAAEA,gBAAiB;UACnCnF,aAAa,EAAEA,aAAc;UAC7BE,eAAe,EAAEA,eAAgB;UACjCvD,IAAI,EAAEA,IAAK;UACX6B,aAAa,EAAEA,aAAc;UAC7BoE,UAAU,EAAEA,UAAW;UACvB9F,SAAS,EAAEA,SAAU;UACrBuR,kBAAkB,EAAE,EAAA9R,qBAAA,GAAA2B,cAAc,CAACC,OAAO,cAAA5B,qBAAA,uBAAtBA,qBAAA,CAAwBqJ,oBAAoB,CAAC,CAAC,KAAI,KAAM;UAC5E0I,qBAAqB,EAAG5L,KAAK,IAAK;YAC9B,IAAIxE,cAAc,CAACC,OAAO,EAAE;cACxB;cACAD,cAAc,CAACC,OAAO,CAACmQ,qBAAqB,CAAC5L,KAAK,CAAC;YACvD;UACJ;QAAE;UAAA8K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEjB,CAAC,gBAENvR,OAAA;MAAKgR,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC7CjR,OAAA,CAACjB,mBAAmB;QAChBoT,OAAO,EAAEA,CAAA,KAAM;UACX,IAAIjL,MAAM,CAACK,SAAS,EAAE;YAClBL,MAAM,CAACC,IAAI,CAAC,YAAY,EAAE;cAAEC,SAAS,EAAElF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,EAAE;cAAEjH;YAAO,CAAC,CAAC;YAC1D8G,MAAM,CAAC+G,UAAU,CAAC,CAAC;UACvB;UACA/G,MAAM,CAACkL,kBAAkB,CAAC,CAAC,CAAC,CAAC;UAC7B9R,QAAQ,mBAAA2B,MAAA,CAAmB7B,MAAM,CAAE,CAAC;QACxC,CAAE;QACFiS,MAAM,EAAE,CAACpR,OAAQ;QACjBqR,WAAW,EAAEtL;MAAiB;QAAAoK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA,CAAAhR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,YAAY,KAAItH,OAAO,iBAC1BjB,OAAA;MAAKgR,SAAS,qEAAA/O,MAAA,CACRc,cAAc,GACV,0BAA0B,GAC1B,4BAA4B,yEACc;MAAAkO,QAAA,eAChDjR,OAAA;QAAKgR,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCjR,OAAA;UAAKgR,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAAEzK,UAAU,CAACpE,aAAa,CAAC,EAAC,UAAK;QAAA;UAAAgP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEA,CAAC;AAEd,CAAC;AAACrR,EAAA,CA97BID,UAAU;EAAA,QACOxB,SAAS,EACXR,WAAW,EACXW,WAAW,EACXV,WAAW,EACNA,WAAW,EACbA,WAAW,EA4BdA,WAAW,EAC6BA,WAAW;AAAA;AAAAqU,EAAA,GAnClEtS,UAAU;AAg8BhB,eAAeA,UAAU;AAAC,IAAAsS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}