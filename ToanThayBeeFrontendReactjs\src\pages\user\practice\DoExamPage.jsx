import HeaderDoExamPage from "../../../components/header/HeaderDoExamPage";
import { useDispatch, useSelector } from "react-redux";
import { useState, useEffect, useRef, useCallback } from "react";
import { debounce } from "lodash";
import { fetchPublicQuestionsByExamId } from "../../../features/question/questionSlice";
import { fetchPublicExamById } from "../../../features/exam/examSlice";
import { useParams } from "react-router-dom";
import { setErrorMessage, setSuccessMessage } from "../../../features/state/stateApiSlice";
import { useNavigate } from "react-router-dom";
import { fetchAnswersByAttempt, setAnswers } from "../../../features/answer/answerSlice";
import ExamRegulationModal from "../../../components/modal/ExamRegulationModal";
import { AnimatePresence } from "framer-motion";
import { Menu } from "lucide-react";
import ExamSidebar from "../../../components/sidebar/ExamSidebar";
import ExamContent from "../../../components/questions/ExamContent";
import { requestFullscreen, exitFullscreen, isFullscreen } from "../../../utils/fullscreenUtils";
import {
    setRemainingTime,
    summitExam,
    setSaveQuestions,
    getRemainingTime,
    logUserActivity,
    submitAnswerWithAttempt,
    leaveExam,
    joinExam,
    resetQuestionStates,
} from "../../../features/doExam/doExamSlice";

const DoExamPage = () => {
    const { examId } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { exam } = useSelector(state => state.exams);
    const { questions } = useSelector(state => state.questions);
    const { answers } = useSelector(state => state.answers);
    const [fontSize, setFontSize] = useState(14); // 14px mặc định
    const [imageSize, setImageSize] = useState(12); // đơn vị: rem
    const questionRefs = useRef([]);
    const [isAgree, setIsAgree] = useState(false);
    const [attemptId, setAttemptId] = useState(null);
    const attemptRef = useRef(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [flag, setFlag] = useState(false);
    const [startTime1, setStartTime1] = useState(null);
    const hasSubmittedRef = useRef(false);
    const examRef = useRef(null);
    const examContentRef = useRef(null);

    useEffect(() => {
        examRef.current = exam;
        if (exam?.acceptDoExam === false) {
            navigate(`/practice/exam/${examId}`)
        }
    }, [exam]);

    useEffect(() => {
        if (examId) {
            dispatch(fetchPublicExamById(examId));
        }
    }, [dispatch, examId]);


    const { user } = useSelector((state) => state.auth);
    const { remainingTime, saveQuestions, errorQuestions, loadingJoin } = useSelector((state) => state.doExam);


    const [markedQuestions, setMarkedQuestions] = useState(new Set());
    const [timeWarningShown, setTimeWarningShown] = useState({
        fiveMinutes: false,
        oneMinute: false
    });
    const [isTimeBlinking, setIsTimeBlinking] = useState(false);

    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.', 'G.', 'H.', 'I.', 'J.'];
    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)', 'g)', 'h)', 'i)', 'j)'];

    const [selectedQuestion, setSelectedQuestion] = useState(null);
    const [isDarkMode, setIsDarkMode] = useState(() => {
        const saved = localStorage.getItem("isDarkMode");
        return saved ? JSON.parse(saved) : false;
    });

    const [loadingSubmit, setLoadingSubmit] = useState(false);
    const [isTimeUp, setIsTimeUp] = useState(false);

    const [questionTN, setQuestionTN] = useState([]);
    const [savingQuestions, setSavingQuestions] = useState(new Set()); // Track questions being saved
    const [questionDS, setQuestionDS] = useState([]);
    const [questionTLN, setQuestionTLN] = useState([]);

    const [answerTN, setAnswerTN] = useState([]);
    const [answerTLN, setAnswerTLN] = useState([]);
    const [dsAnswers, setDsAnswers] = useState({});

    document.addEventListener("copy", (e) => {
        e.preventDefault();
    });

    // These functions are now DEPRECATED - Redux slice handles state automatically
    // Only keeping them for backward compatibility with existing useEffect logic
    // TODO: Remove these after refactoring useEffect dependencies

    const addQuestion = (questionId) => {
        // Only add to saveQuestions if not already there and not in errorQuestions
        if (!saveQuestions.includes(questionId) && !errorQuestions.includes(questionId)) {
            dispatch(setSaveQuestions([...saveQuestions, questionId]));
        }
        // DO NOT call removeErrorQuestion here - let Redux slice handle it
    };

    // Removed deprecated functions - Redux slice handles all state management automatically

    // Hàm đánh dấu câu hỏi để xem lại sau
    const toggleMarkQuestion = (questionId) => {
        setMarkedQuestions(prev => {
            const newSet = new Set(prev);
            if (newSet.has(questionId)) {
                newSet.delete(questionId);
            } else {
                newSet.add(questionId);
            }
            return newSet;
        });
    };


    const handleExitFullscreen = () => {
        try {
            exitFullscreen();
        } catch (err) {
            // Chỉ ghi log lỗi, không bắt lỗi
            console.warn("Không thể thoát fullscreen:", err);
        }
    };

    const handleFontSizeChange = (e) => {
        setFontSize(Number(e.target.value));
    };

    const handleImageSizeChange = (e) => {
        setImageSize(Number(e.target.value));
    };

    const formatTime = (seconds) => {
        const min = String(Math.floor(seconds / 60)).padStart(2, '0');
        const sec = String(seconds % 60).padStart(2, '0');
        return `${min}:${sec}`;
    };

    const handleFullScreen = async () => {
        try {
            // Sử dụng joinExam action thay vì fetch
            const result = await dispatch(joinExam(examId)).unwrap();

            // Xử lý khi join exam thành công
            const { attemptId, startTime } = result;
            console.log("Đã nhận được thông báo bắt đầu thi từ server:", attemptId);

            setIsAgree(true);
            attemptRef.current = attemptId;
            setAttemptId(attemptId);

            if (examId) {
                dispatch(fetchPublicQuestionsByExamId(examId));
            }
            setStartTime1(startTime);

            if (!exam?.isCheatingCheckEnabled) {
                return;
            }

            try {
                const success = await requestFullscreen();
                if (!success) {
                    console.warn("Không thể vào fullscreen, nhưng vẫn cho phép làm bài");
                }
            } catch (err) {
                console.error("❌ Lỗi khi bật fullscreen:", err);
                alert("Không thể vào fullscreen, nhưng bạn vẫn có thể làm bài.");
            }

        } catch (error) {
            console.error("Lỗi khi tham gia bài thi:", error);
            dispatch(setErrorMessage("Lỗi: " + error.message));
            navigate(`/practice/exam/${examId}`);
        }
    };

    // Removed socket-based exam_started listener - now handled in handleFullScreen

    useEffect(() => {
        if (exam?.testDuration && startTime1) {
            const start = new Date(startTime1);
            const now = new Date();
            const elapsedSeconds = Math.floor((now - start) / 1000);
            const totalSeconds = exam.testDuration * 60;
            const remaining = Math.max(totalSeconds - elapsedSeconds, 0);
            dispatch(setRemainingTime(remaining));

            // Yêu cầu thời gian từ server khi bắt đầu - sử dụng API thay vì socket
            if (attemptId) {
                dispatch(getRemainingTime({ examId, attemptId }))
                    .then((result) => {
                        if (result.payload?.data?.remainingTime !== undefined) {
                            dispatch(setRemainingTime(result.payload.data.remainingTime));
                        }
                    })
                    .catch((error) => {
                        console.error("Lỗi khi lấy thời gian từ server:", error);
                    });
            }
        }
    }, [startTime1, exam, attemptId, examId, dispatch]);

    useEffect(() => {
        if (flag) return
        if (!remainingTime) setFlag(true)
    }, [remainingTime])

    const handleAutoSubmit = async () => {
        if (hasSubmittedRef.current) {
            console.warn("⛔ Đã submit rồi, bỏ qua lần gọi lại.");
            return;
        }
        hasSubmittedRef.current = true; // Đánh dấu đã submit
        console.log("Kiểm tra attemptId:", attemptId);
        if (!attemptId) {
            console.log("Không thể nộp bài: attemptId không tồn tại hoặc đang trong quá trình nộp/tải");
            return;
        }

        console.log("Đang nộp bài với attemptId:", attemptId);

        // Reset both saveQuestions and errorQuestions before submitting
        dispatch(resetQuestionStates());
        setLoadingSubmit(true);

        try {
            // Sử dụng API thay vì socket để nộp bài
            const result = await dispatch(summitExam(attemptId)).unwrap();
            console.log("Nộp bài thành công:", result);

            // Xử lý khi nộp bài thành công
            dispatch(setSuccessMessage("Nộp bài thành công!"));

            // Thoát fullscreen mà không bắt lỗi
            try {
                exitFullscreen();
            } catch (err) {
                // Chỉ ghi log lỗi, không ảnh hưởng đến luồng chính
                console.warn("Không thể thoát fullscreen khi nộp bài:", err);
            }

            const safeAttemptId = attemptRef.current;
            const currentExam = examRef.current;

            if (!safeAttemptId) {
                console.error("Không có attemptId khi navigate!");
                return;
            }

            // Log để debug
            console.log("Current exam state:", currentExam);
            console.log("Attempt ID:", safeAttemptId);

            if (!currentExam || !currentExam.seeCorrectAnswer) {
                console.log("Chuyển về trang danh sách do:", {
                    examNull: !currentExam,
                    cantSeeAnswer: currentExam && !currentExam.seeCorrectAnswer
                });
                navigate(`/practice/exam/${examId}`);
                return;
            }

            navigate(`/practice/exam/attempt/${safeAttemptId}/score`);
        } catch (error) {
            console.error("Lỗi khi nộp bài:", error);
            setLoadingSubmit(false);
            dispatch(setErrorMessage("Lỗi khi nộp bài. Vui lòng thử lại."));
            hasSubmittedRef.current = false; // Reset trạng thái để có thể thử lại

            // Thử nộp lại sau 3 giây nếu lỗi xảy ra
            setTimeout(() => {
                if (!loadingSubmit && attemptRef.current) {
                    console.log("Thử nộp bài lại sau lỗi...");
                    handleAutoSubmit();
                }
            }, 5000);
        }
    };

    // Hàm mới để xử lý việc chuyển đến câu hỏi dựa trên chế độ hiển thị
    const navigateToQuestion = useCallback((questionId) => {
        setSelectedQuestion(questionId);

        // Kiểm tra xem examContentRef có tồn tại và có phương thức goToQuestionById không
        if (examContentRef.current && examContentRef.current.isSingleQuestionMode()) {
            // Nếu đang ở chế độ hiển thị từng câu, sử dụng phương thức goToQuestionById
            examContentRef.current.goToQuestionById(questionId);
        } else {
            // Nếu đang ở chế độ hiển thị tất cả câu hỏi, sử dụng phương thức cuộn đến câu hỏi
            // Tìm phần tử câu hỏi bằng querySelector
            setTimeout(() => {
                // Thử tìm phần tử bằng data-question-id
                const element = document.querySelector(`[data-question-id="${questionId}"]`);

                if (element) {
                    const offset = 80; // chiều cao của header sticky
                    const y = element.getBoundingClientRect().top + window.scrollY - offset;
                    window.scrollTo({ top: y, behavior: "smooth" });
                } else {
                    // Fallback: Sử dụng refs
                    const refElement = questionRefs.current[questionId];

                    if (refElement) {
                        const offset = 80; // chiều cao của header sticky
                        const y = refElement.getBoundingClientRect().top + window.scrollY - offset;
                        window.scrollTo({ top: y, behavior: "smooth" });
                    }
                }
            }, 0);
        }
    }, [questionRefs, examContentRef]);

    // navigateToQuestion is used directly in components

    // Create debounced submit functions with different delays for different question types
    const submitAnswerTNDebounced = useCallback(
        debounce((payload) => {
            // Add to saving state
            setSavingQuestions(prev => new Set(prev).add(payload.questionId));

            dispatch(submitAnswerWithAttempt(payload)).then((result) => {
                // Remove from saving state
                setSavingQuestions(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(payload.questionId);
                    return newSet;
                });

                if (result.type.endsWith('/fulfilled')) {
                    console.log("Đã lưu câu trả lời TN thành công");
                    // API success - questionId will be added to saveQuestions by slice
                    // and removed from errorQuestions automatically
                } else {
                    console.error("Lỗi khi lưu câu trả lời TN:", result.error);
                    // API failed - questionId will be added to errorQuestions by slice
                    // and removed from saveQuestions automatically
                }
            });
        }, 500), // TN: 500ms - shorter delay for multiple choice
        [dispatch, attemptId]
    );

    const submitAnswerDSDebounced = useCallback(
        debounce((payload) => {
            // Add to saving state
            setSavingQuestions(prev => new Set(prev).add(payload.questionId));

            dispatch(submitAnswerWithAttempt(payload)).then((result) => {
                // Remove from saving state
                setSavingQuestions(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(payload.questionId);
                    return newSet;
                });

                if (result.type.endsWith('/fulfilled')) {
                    console.log("Đã lưu câu trả lời DS thành công");
                    // API success - questionId will be added to saveQuestions by slice
                    // and removed from errorQuestions automatically
                } else {
                    console.error("Lỗi khi lưu câu trả lời DS:", result.error);
                    // API failed - questionId will be added to errorQuestions by slice
                    // and removed from saveQuestions automatically
                }
            });
        }, 800), // DS: 800ms - medium delay for true/false
        [dispatch, attemptId]
    );

    const submitAnswerTLNDebounced = useCallback(
        debounce((payload) => {
            // Add to saving state
            setSavingQuestions(prev => new Set(prev).add(payload.questionId));

            dispatch(submitAnswerWithAttempt(payload)).then((result) => {
                // Remove from saving state
                setSavingQuestions(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(payload.questionId);
                    return newSet;
                });

                if (result.type.endsWith('/fulfilled')) {
                    console.log("Đã lưu câu trả lời TLN thành công");
                    // API success - questionId will be added to saveQuestions by slice
                    // and removed from errorQuestions automatically
                } else {
                    console.error("Lỗi khi lưu câu trả lời TLN:", result.error);
                    // API failed - questionId will be added to errorQuestions by slice
                    // and removed from saveQuestions automatically
                }
            });
        }, 1500), // TLN: 1500ms - longer delay for text input
        [dispatch, attemptId]
    );

    // Cleanup debounced functions on unmount
    useEffect(() => {
        return () => {
            submitAnswerTNDebounced.cancel();
            submitAnswerDSDebounced.cancel();
            submitAnswerTLNDebounced.cancel();
        };
    }, [submitAnswerTNDebounced, submitAnswerDSDebounced, submitAnswerTLNDebounced]);

    const handleSelectAnswerTN = (questionId, statementId, type) => {
        // Không cho phép làm bài nếu đã hết thời gian
        if (isTimeUp) {
            dispatch(setErrorMessage("Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!"));
            return;
        }

        const newAnswer = {
            questionId,
            answerContent: statementId,
            typeOfQuestion: type,
        };
        dispatch(setAnswers(newAnswer));

        // Sử dụng debounced API call cho TN
        submitAnswerTNDebounced({
            questionId,
            answerContent: statementId,
            type,
            attemptId
        });
    };

    const handleSelectAnswerDS = (questionId, statementId, selectedAnswer) => {
        // Không cho phép làm bài nếu đã hết thời gian
        if (isTimeUp) {
            dispatch(setErrorMessage("Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!"));
            return;
        }

        const currentAnswers = dsAnswers[questionId] || [];

        const existing = currentAnswers.find(ans => ans.statementId === statementId);

        // 🔁 Nếu đáp án đã giống thì không gửi lại
        if (existing && existing.answer === selectedAnswer) {
            return
        }

        const updatedAnswers = currentAnswers.map(ans =>
            ans.statementId === statementId
                ? { ...ans, answer: selectedAnswer }
                : ans
        );

        // Nếu chưa có statement này
        if (!existing) {
            updatedAnswers.push({ statementId, answer: selectedAnswer });
        }

        dispatch(setAnswers({ questionId, answerContent: JSON.stringify(updatedAnswers), typeOfQuestion: "DS" }));

        // Sử dụng debounced API call cho DS
        submitAnswerDSDebounced({
            questionId,
            answerContent: updatedAnswers,
            type: "DS",
            attemptId
        });
    };


    const handleSelectAnswerTLN = (questionId, answerContent, type) => {
        // Không cho phép làm bài nếu đã hết thời gian
        if (isTimeUp) {
            dispatch(setErrorMessage("Đã hết thời gian làm bài. Không thể thay đổi câu trả lời!"));
            return;
        }

        if (!answerContent || answerContent.trim() === "") {
            return;
        }

        const formattedAnswer = answerContent.trim().replace(",", ".");
        dispatch(setAnswers({ questionId, answerContent, typeOfQuestion: type }));

        // Sử dụng debounced API call cho TLN - đặc biệt hữu ích khi user gõ liên tục
        submitAnswerTLNDebounced({
            questionId,
            answerContent: formattedAnswer,
            type,
            attemptId
        });
    }

    // Tạo một ref để lưu trữ các câu hỏi cần được đánh dấu là đã lưu
    const questionsToMarkAsSaved = useRef(new Set());

    // useEffect để xử lý việc đánh dấu câu hỏi đã lưu
    // useEffect(() => {
    //     if (questionsToMarkAsSaved.current.size > 0) {
    //         questionsToMarkAsSaved.current.forEach(questionId => {
    //             if (!saveQuestions.includes(questionId)) {
    //                 addQuestion(questionId);
    //             }
    //         });
    //         questionsToMarkAsSaved.current.clear();
    //     }
    // }, [saveQuestions, addQuestion]);

    // Thêm một useEffect để kích hoạt việc xử lý sau mỗi lần render
    // useEffect(() => {
    //     // Sử dụng requestAnimationFrame để đảm bảo việc cập nhật state xảy ra sau khi render hoàn tất
    //     const frameId = requestAnimationFrame(() => {
    //         if (questionsToMarkAsSaved.current.size > 0) {
    //             const questionIds = [...questionsToMarkAsSaved.current];
    //             questionsToMarkAsSaved.current.clear();

    //             // Cập nhật state cho tất cả các câu hỏi cần đánh dấu
    //             questionIds.forEach(questionId => {
    //                 if (!saveQuestions.includes(questionId)) {
    //                     addQuestion(questionId);
    //                 }
    //             });
    //         }
    //     });

    //     return () => cancelAnimationFrame(frameId);
    // });

    const isTNSelected = useCallback((questionId, statementId) => {
        const isSelected = answerTN.some(
            (ans) =>
                ans.questionId === questionId &&
                ans.answerContent &&
                String(ans.answerContent) === String(statementId)
        );

        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý
        if (isSelected && !saveQuestions.includes(questionId)) {
            questionsToMarkAsSaved.current.add(questionId);
        }

        return isSelected;
    }, [answerTN, saveQuestions]);

    const isDSChecked = useCallback((questionId, statementId, bool) => {
        const isSelected = dsAnswers[questionId]?.some(
            (a) => a.statementId === statementId && a.answer === bool
        ) || false;

        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý
        if (isSelected && !saveQuestions.includes(questionId) && dsAnswers[questionId]?.length === 4) {
            questionsToMarkAsSaved.current.add(questionId);
        }

        return isSelected;
    }, [dsAnswers, saveQuestions]);

    const getTLNDefaultValue = useCallback((questionId) => {
        const matched = answerTLN.find((ans) => ans.questionId === questionId);
        const content = matched?.answerContent?.replace(/^"|"$/g, "") || "";

        // Thay vì cập nhật state trực tiếp, chúng ta thêm vào danh sách cần xử lý
        if (content && !saveQuestions.includes(questionId)) {
            questionsToMarkAsSaved.current.add(questionId);
        }

        return content;
    }, [answerTLN, saveQuestions]);

    // useEffect(() => {
    //     if (examId) {
    //         dispatch(fetchPublicQuestionsByExamId(examId));
    //     }
    // }, [dispatch, examId]);

    useEffect(() => {
        if (questions) {
            setQuestionTN(questions.filter((question) => question.typeOfQuestion === "TN"));
            setQuestionDS(questions.filter((question) => question.typeOfQuestion === "DS"));
            setQuestionTLN(questions.filter((question) => question.typeOfQuestion === "TLN"));
        }
    }, [questions]);

    useEffect(() => {
        // Kiểm tra answers có phải là mảng không
        if (!Array.isArray(answers) || answers.length === 0) return;

        const tn = [];
        const tln = [];
        const dsMap = {};

        // Sử dụng for...of thay vì forEach để tránh lỗi
        for (const answer of answers) {
            if (answer.typeOfQuestion === "TN") {
                tn.push(answer);
            } else if (answer.typeOfQuestion === "TLN") {
                tln.push(answer);
            } else if (answer.typeOfQuestion === "DS" && answer.answerContent) {
                try {
                    const parsed = JSON.parse(answer.answerContent);
                    dsMap[answer.questionId] = parsed;
                } catch (err) {
                    console.error("Lỗi parse DS answerContent:", err);
                }
            }
        }

        setAnswerTN(tn);
        setAnswerTLN(tln);
        setDsAnswers(dsMap);

        // Note: Score calculation is now handled when submitting exam
        // No need to calculate score in real-time
    }, [answers]);


    useEffect(() => {
        if (attemptId) {
            dispatch(fetchAnswersByAttempt(attemptId));
        }
    }, [dispatch, attemptId]);

    useEffect(() => {
        if (!exam?.testDuration || remainingTime === null || !isAgree) return;

        // Kiểm tra và hiển thị cảnh báo thời gian
        const checkTimeWarnings = (time) => {
            // Cảnh báo khi còn 5 phút
            if (time === 300 && !timeWarningShown.fiveMinutes) {
                setTimeWarningShown(prev => ({ ...prev, fiveMinutes: true }));
                setIsTimeBlinking(true);
                dispatch(setErrorMessage("Còn 5 phút nữa là hết thời gian làm bài!"));

                // Tắt hiệu ứng nhấp nháy sau 10 giây
                setTimeout(() => {
                    setIsTimeBlinking(false);
                }, 10000);
            }

            // Cảnh báo khi còn 1 phút
            if (time === 60 && !timeWarningShown.oneMinute) {
                setTimeWarningShown(prev => ({ ...prev, oneMinute: true }));
                setIsTimeBlinking(true);
                dispatch(setErrorMessage("Còn 1 phút nữa là hết thời gian làm bài!"));

                // Giữ hiệu ứng nhấp nháy cho đến khi hết thời gian
            }
        };

        // Định kỳ yêu cầu thời gian từ server để đồng bộ - sử dụng API
        const syncTimeInterval = setInterval(() => {
            if (attemptId) {
                dispatch(getRemainingTime({ examId, attemptId }))
                    .then((result) => {
                        if (result.payload?.data?.remainingTime !== undefined) {
                            dispatch(setRemainingTime(result.payload.data.remainingTime));
                        }
                    })
                    .catch((error) => {
                        console.error("Lỗi khi đồng bộ thời gian:", error);
                    });
            }
        }, 30000); // Đồng bộ thời gian mỗi 30 giây

        const interval = setInterval(() => {
            dispatch(setRemainingTime((prev) => {
                if (prev <= 1) { // dùng <=1 để đảm bảo không bị âm
                    clearInterval(interval);
                    clearInterval(syncTimeInterval);
                    // Đánh dấu là đã hết thời gian
                    setIsTimeUp(true);
                    setIsTimeBlinking(false);
                    // Thử nộp bài
                    handleAutoSubmit();
                    return 0;
                }

                // Kiểm tra cảnh báo thời gian
                checkTimeWarnings(prev);

                return prev - 1;
            }));
        }, 1000);

        return () => {
            clearInterval(interval);
            clearInterval(syncTimeInterval);
        };
    }, [exam?.testDuration, isAgree, remainingTime, timeWarningShown, dispatch, attemptId, examId]);// Chỉ phụ thuộc vào các giá trị cần thiết

    // Removed socket connection management - using API only

    // frontend
    useEffect(() => {
        if (!attemptId || !user?.id || !examId || attemptId === null || attemptId === undefined) return;
        if (!exam?.isCheatingCheckEnabled) return;
        console.log("Đã bật theo dõi hành vi gian lận");


        const recentLogs = new Set(); // chống log lặp
        const logOnce = (key, payload) => {
            if (!exam?.isCheatingCheckEnabled || recentLogs.has(key)) return;

            recentLogs.add(key);

            // Sử dụng API thay vì socket
            dispatch(logUserActivity({
                examId,
                attemptId,
                activityType: payload.type || 'user_activity',
                details: {
                    ...payload,
                    name: user.lastName + " " + user.firstName
                }
            }));

            setTimeout(() => recentLogs.delete(key), 5000);
        };

        // 📌 Thoát fullscreen
        const handleFullscreenChange = () => {
            if (!document.fullscreenElement &&
                !document.webkitFullscreenElement &&
                !document.mozFullScreenElement &&
                !document.msFullscreenElement) {
                logOnce("exit_fullscreen", {
                    studentId: user.id,
                    attemptId,
                    examId,
                    code: "EF",
                    action: "exit_fullscreen",
                    detail: JSON.stringify({ reason: "User exited fullscreen mode" }),
                });
            }
        };

        // 📌 Chuyển tab hoặc thu nhỏ trình duyệt
        const handleVisibilityChange = () => {
            if (document.visibilityState === "hidden") {
                logOnce("tab_blur", {
                    studentId: user.id,
                    attemptId,
                    examId,
                    code: "TB",
                    action: "tab_blur",
                    detail: JSON.stringify({ message: "User switched tab or minimized window" }),
                });
            }
        };

        // 📌 Copy nội dung
        const handleCopy = () => {
            logOnce("copy_detected", {
                studentId: user.id,
                attemptId,
                examId,
                code: "COP",
                action: "copy_detected",
                detail: JSON.stringify({ message: "User copied content" }),
            });
        };

        // 📌 Phím đáng ngờ
        const handleSuspiciousKey = (e) => {
            const suspiciousKeys = [
                "F12", "PrintScreen", "Alt", "Tab", "Meta", "Control", "Shift"
            ];
            const combo = `${e.ctrlKey ? "Ctrl+" : ""}${e.shiftKey ? "Shift+" : ""}${e.altKey ? "Alt+" : ""}${e.metaKey ? "Meta+" : ""}${e.key}`;

            if (
                suspiciousKeys.includes(e.key) ||
                combo === "Ctrl+Shift+I" ||
                combo === "Ctrl+Shift+C"
            ) {
                logOnce(`key_${combo}`, {
                    studentId: user.id,
                    attemptId,
                    examId,
                    code: "SK",
                    action: "suspicious_key",
                    detail: JSON.stringify({ key: e.key, code: e.code, combo }),
                });
            }
        };

        document.addEventListener("fullscreenchange", handleFullscreenChange);
        document.addEventListener("visibilitychange", handleVisibilityChange);
        document.addEventListener("copy", handleCopy);
        document.addEventListener("keydown", handleSuspiciousKey);

        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
            document.removeEventListener("visibilitychange", handleVisibilityChange);
            document.removeEventListener("copy", handleCopy);
            document.removeEventListener("keydown", handleSuspiciousKey);
        };
    }, [user.id, examId, attemptId]);


    useEffect(() => {
        // Removed all socket event listeners - using API responses instead
        // Answer save/error status is now handled in submitAnswerWithAttempt action responses
        // Timer updates are handled via getRemainingTime API calls
        // Auto-submit is handled via client-side timer logic
    }, [exam, examId, navigate, dispatch]);

    useEffect(() => {
        localStorage.setItem("isDarkMode", JSON.stringify(isDarkMode));
    }, [isDarkMode]);

    // Hàm xử lý chuyển đổi câu hỏi
    const handleKeyDown = useCallback((e) => {
        // Ngăn chặn hành vi mặc định của phím mũi tên để không ảnh hưởng đến radio buttons
        if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
            // Ngăn chặn hành vi mặc định của trình duyệt (như di chuyển giữa các radio button)
            e.preventDefault();

            // Nếu không có câu hỏi, thoát khỏi hàm
            if (!questions || questions.length === 0) return;

            const allQuestions = [...questionTN, ...questionDS, ...questionTLN];
            const currentIndex = allQuestions.findIndex(q => q.id === selectedQuestion);

            if ((e.key === "ArrowUp" || e.key === "ArrowLeft") && currentIndex > 0) {
                const prevQuestionId = allQuestions[currentIndex - 1].id;
                console.log("ArrowUp/Left pressed, navigating to question:", prevQuestionId);
                navigateToQuestion(prevQuestionId);
            } else if ((e.key === "ArrowDown" || e.key === "ArrowRight") && currentIndex < allQuestions.length - 1) {
                const nextQuestionId = allQuestions[currentIndex + 1].id;
                console.log("ArrowDown/Right pressed, navigating to question:", nextQuestionId);
                navigateToQuestion(nextQuestionId);
            }
        }
    }, [questions, questionTN, questionDS, questionTLN, selectedQuestion, navigateToQuestion]);
    // Lắng nghe sự kiện bàn phím
    useEffect(() => {
        document.addEventListener("keydown", handleKeyDown);
        return () => {
            document.removeEventListener("keydown", handleKeyDown);
        };
    }, [handleKeyDown]);

    return (
        <div className={`flex flex-col h-full ${isDarkMode ? 'bg-slate-900 text-white' : 'bg-gray-50 text-black'}`}>
            <HeaderDoExamPage nameExam={exam?.name} onExitFullscreen={handleExitFullscreen} isDarkMode={!isDarkMode} />
            {isAgree ? (
                <div className="flex flex-col h-full lg:flex-row flex-1 w-full gap-4 px-4 pb-4 mt-5">
                    {/* Main Content */}
                    <ExamContent
                        ref={examContentRef}
                        loading1={loadingJoin}
                        isDarkMode={isDarkMode}
                        questionTN={questionTN}
                        questionDS={questionDS}
                        questionTLN={questionTLN}
                        handlers={{
                            handleSelectAnswerTN,
                            handleSelectAnswerDS,
                            handleSelectAnswerTLN,
                            isTNSelected,
                            isDSChecked,
                            getTLNDefaultValue,
                            setQuestionRef: (id, el) => (questionRefs.current[id] = el),
                            setSelectedQuestion: (id) => setSelectedQuestion(id)
                        }}
                        settings={{
                            selectedQuestion,
                            isDarkMode,
                            fontSize,
                            imageSize,
                            prefixStatementTN,
                            prefixStatementDS,
                            isTimeUp,
                            markedQuestions,
                            toggleMarkQuestion
                        }}
                        isTimeUp={isTimeUp}
                        // Để undefined để component tự quyết định dựa trên thiết bị
                        initialSingleMode={undefined}
                        handleAutoSubmit={handleAutoSubmit}
                        loadingSubmit={loadingSubmit}
                    />


                    {/* Button toggle cho mobile */}
                    <div className="fixed bottom-4 right-4 z-50 lg:hidden">
                        <button
                            className={`p-2 rounded-full shadow-md ${isDarkMode ? "bg-gray-800 text-white" : "bg-white text-black"}`}
                            onClick={() => setIsSidebarOpen(prev => !prev)}
                        >
                            <Menu />
                        </button>
                    </div>

                    {/* Sidebar chính */}
                    <AnimatePresence>
                        {(isSidebarOpen || window.innerWidth > 1024) && (
                            <ExamSidebar
                                isDarkMode={isDarkMode}
                                setIsDarkMode={setIsDarkMode}
                                fontSize={fontSize}
                                handleFontSizeChange={handleFontSizeChange}
                                imageSize={imageSize}
                                handleImageSizeChange={handleImageSizeChange}
                                questionTN={questionTN}
                                questionDS={questionDS}
                                questionTLN={questionTLN}
                                scrollToQuestion={navigateToQuestion}
                                selectedQuestion={selectedQuestion}
                                markedQuestions={markedQuestions}
                                toggleMarkQuestion={toggleMarkQuestion}
                                handleAutoSubmit={handleAutoSubmit}
                                loadingSubmit={loadingSubmit}
                                loadingLoadExam={loadingJoin}
                                exam={exam}
                                remainingTime={remainingTime}
                                formatTime={formatTime}
                                questions={questions}
                                savingQuestions={savingQuestions}
                                singleQuestionMode={examContentRef.current?.isSingleQuestionMode() || false}
                                setSingleQuestionMode={(value) => {
                                    if (examContentRef.current) {
                                        // Khi chuyển sang chế độ hiển thị từng câu, đảm bảo câu hỏi đang được chọn sẽ được hiển thị
                                        examContentRef.current.setSingleQuestionMode(value);
                                    }
                                }}
                            />
                        )}
                    </AnimatePresence>

                </div>
            ) : (
                <div className="flex items-center justify-center">
                    <ExamRegulationModal
                        onClose={() => {
                            // Sử dụng API để leave exam nếu có attemptId
                            if (attemptId) {
                                dispatch(leaveExam({ examId, attemptId }));
                            }
                            navigate(`/practice/exam/${examId}`);
                        }}
                        isOpen={!isAgree}
                        onStartExam={handleFullScreen}
                    />
                </div>
            )}

            {exam?.testDuration && isAgree && (
                <div className={`fixed bottom-2 rounded-md left-2 px-4 py-2
                    ${isTimeBlinking
                        ? 'bg-red-600 animate-pulse'
                        : 'bg-slate-700 bg-opacity-80'}
                    text-white z-50 transition-colors duration-300`}>
                    <div className="flex items-center gap-2">
                        <div className="text-sm font-bold">{formatTime(remainingTime)} phút</div>
                    </div>
                </div>
            )}

        </div>
    );
};

export default DoExamPage;
