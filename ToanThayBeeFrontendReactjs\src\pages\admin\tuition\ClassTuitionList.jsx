import React, { useEffect, useState, useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  fetchClassTuitions,
  deleteClassTuition,
  createBatchClassTuition,
  fetchTuitionStatistics
} from "src/features/tuition/tuitionSlice";
import { setCurrentPage, setSearch, resetFilters } from "src/features/filter/filterSlice";
import { formatCurrency } from "src/utils/formatters";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
import Pagination from "src/components/Pagination";
import ConfirmModal from "src/components/modal/ConfirmDeleteModal";
import { Edit, Trash, Plus, Eye, FileText, Calendar, Users, X, AlertCircle, BarChart2, List, Search } from "lucide-react";
import AdminLayout from "src/layouts/AdminLayout";
import FunctionBarAdmin from "src/components/bar/FunctionBarAdmin";
import Chart from 'chart.js/auto';

const ClassTuitionList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { classTuitions, loading } = useSelector((state) => state.tuition);
  const { search, currentPage, limit, totalItems, sortOrder, totalPages } = useSelector(state => state.filter);

  const [inputValue, setInputValue] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [tuitionToDelete, setTuitionToDelete] = useState(null);
  const [didInit, setDidInit] = useState(false); // 👉 Thêm cờ kiểm soát mount đầu tiên
  const [showRightPanel, setShowRightPanel] = useState(false);
  const [rightPanelType, setRightPanelType] = useState(""); // "batch"

  // State cho form tạo học phí hàng loạt
  const [batchMonth, setBatchMonth] = useState("");
  const [batchAmount, setBatchAmount] = useState("");
  const [batchClass, setBatchClass] = useState("");
  const [batchNote, setBatchNote] = useState("");
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  useEffect(() => {
    if (!didInit) {
      dispatch(resetFilters());
      setDidInit(true);
    }
  }, [dispatch, didInit]);

  useEffect(() => {
    if (didInit) {
      // console.log("currentPage", currentPage);
      dispatch(
        fetchClassTuitions({
          search: inputValue,
          currentPage,
          limit,
          sortOrder: "DESC",
        })
      );
    }

  }, [dispatch, inputValue, currentPage, limit, didInit]);

  const handleEdit = (id) => {
    navigate(`/admin/class-tuition/edit/${id}`);
  };

  const handleView = (id) => {
    navigate(`/admin/class-tuition/view/${id}`);
  };

  const handleAdd = () => {
    navigate("/admin/class-tuition/add");
  };

  const handleBatchAdd = () => {
    setRightPanelType("batch");
    setShowRightPanel(true);
  };

  const closeRightPanel = () => {
    setShowRightPanel(false);
    setRightPanelType("");
    // Reset form state
    setBatchMonth("");
    setBatchAmount("");
    setBatchClass("");
    setBatchNote("");
    setFormErrors({});
    setIsSubmitting(false);
  };

  // Validate form data
  const validateBatchTuitionForm = () => {
    const errors = {};

    // Validate month (required and format YYYY-MM)
    if (!batchMonth) {
      errors.batchMonth = "Tháng không được để trống";
    } else if (!/^\d{4}-\d{2}$/.test(batchMonth)) {
      errors.batchMonth = "Định dạng tháng không hợp lệ";
    }

    // Validate amount (required and positive number)
    if (!batchAmount) {
      errors.batchAmount = "Số tiền không được để trống";
    } else if (isNaN(batchAmount) || Number(batchAmount) < 0) {
      errors.batchAmount = "Số tiền phải là số dương";
    }

    // Validate class selection (required)
    if (!batchClass) {
      errors.batchClass = "Vui lòng chọn lớp";
    }

    return errors;
  };

  // Handle batch tuition form submission
  const handleBatchTuitionSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const errors = validateBatchTuitionForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for API call
      const batchData = {
        month: batchMonth,
        amount: Number(batchAmount),
        note: batchNote,
        classAttribute: batchClass
      };

      // Call API to create batch tuition payments
      await dispatch(createBatchClassTuition(batchData));

      // Close panel and refresh data
      closeRightPanel();
      dispatch(
        fetchClassTuitions({
          search: inputValue,
          currentPage,
          limit,
          sortOrder: "DESC",
        })
      );
    } catch (error) {
      console.error("Error creating batch class tuitions:", error);
      setFormErrors({ submit: "Có lỗi xảy ra khi tạo học phí hàng loạt" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = (id) => {
    setTuitionToDelete(id);
    setShowConfirmModal(true);
  };

  const confirmDelete = async () => {
    dispatch(deleteClassTuition(tuitionToDelete));
    setShowConfirmModal(false);
    dispatch(
      fetchClassTuitions({
        search: inputValue,
        currentPage,
        limit,
        sortOrder: "DESC",
      })
    );
  };

  const cancelDelete = () => {
    setShowConfirmModal(false);
    setTuitionToDelete(null);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  const ButtonFunctionBarAdmin = ({ icon, text, onClick }) => {
    return (
      <button
        onClick={onClick}
        className="flex items-center gap-[0.5rem] px-[1rem] py-[0.5rem] border border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam"
      >
        {icon}
        <span>{text}</span>
      </button>
    );
  };

  const iconAdd = (
    <div data-svg-wrapper className="relative">
      <Plus size={16} />
    </div>
  );

  const iconBatch = (
    <div data-svg-wrapper className="relative">
      <FileText size={16} />
    </div>
  );

  return (
    <AdminLayout>
      <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9 mb-4">
        Quản lý học phí lớp học
      </div>

      <div className="flex justify-between items-center border-b border-[#E7E7ED] pb-4 mb-6">
        <div className="flex gap-[0.875rem] h-full items-center">
          <div className="flex items-center h-full gap-[0.5rem]">
            <div className="w-[15rem] h-full relative">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                className="absolute left-[1rem] top-1/2 transform -translate-y-1/2"
              >
                <path
                  d="M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z"
                  stroke="#131214"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <input
                type="text"
                placeholder="Tìm kiếm theo tên lớp học..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full h-full pl-[2.5rem] pr-[1rem] border py-[0.5rem] border-[#CDCFD0] rounded-[2rem] text-[#090a0a] text-[0.875rem] font-bevietnam"
              />
            </div>
            <ButtonFunctionBarAdmin icon={iconAdd} text={'Thêm học phí'} onClick={handleAdd} />
            <ButtonFunctionBarAdmin icon={iconBatch} text={'Tạo học phí hàng loạt'} onClick={handleBatchAdd} />
          </div>
        </div>
      </div>

      {classTuitions.length === 0 ? (
        <div className="bg-white shadow-md rounded-lg p-6 text-center">
          <p className="text-gray-500">Không có dữ liệu học phí nào.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
            <thead className="bg-gray-100">
              <tr>
                <th className="py-3 px-4 text-left">STT</th>
                <th className="py-3 px-4 text-left">Lớp học</th>
                <th className="py-3 px-4 text-left">Học phí</th>
                <th className="py-3 px-4 text-left">Tháng áp dụng</th>
                <th className="py-3 px-4 text-left">note</th>
                <th className="py-3 px-4 text-left">Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {classTuitions.map((tuition, index) => (
                <tr
                  key={tuition.id}
                  className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}
                >
                  <td className="py-3 px-4">
                    {(currentPage - 1) * limit + index + 1}
                  </td>
                  <td className="py-3 px-4">{tuition.class?.name || "N/A"}</td>
                  <td className="py-3 px-4">
                    {formatCurrency(tuition.amount)}
                  </td>
                  <td className="py-3 px-4">{tuition.month}</td>
                  <td className="py-3 px-4">
                    {tuition.note}
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(tuition.id)}
                        className="text-blue-500 hover:text-blue-700"
                        title="Xem chi tiết"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEdit(tuition.id)}
                        className="text-yellow-500 hover:text-yellow-700"
                        title="Chỉnh sửa"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(tuition.id)}
                        className="text-red-500 hover:text-red-700"
                        title="Xóa"
                      >
                        <Trash size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-6">
        <Pagination
          currentPage={currentPage}
          onPageChange={(page) => dispatch(setCurrentPage(page))}
          totalItems={totalItems}
          limit={limit}
        />
      </div>

      <ConfirmModal
        isOpen={showConfirmModal}
        onConfirm={confirmDelete}
        text="Bạn có chắc chắn muốn xóa học phí này?"
        onClose={cancelDelete}
      />
        

      {/* Right Panel for Batch Operations */}
      {showRightPanel && (
        <div className="fixed inset-y-0 right-0 w-96 bg-white shadow-lg z-50 overflow-y-auto">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-semibold">
              {rightPanelType === "batch" && "Tạo học phí hàng loạt"}
            </h2>
            <button
              onClick={closeRightPanel}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
          </div>

          <div className="p-4">
            {rightPanelType === "batch" && (
              <div>
                <p className="mb-4">Tạo học phí hàng loạt cho các lớp học theo khối lớp (10, 11, 12).</p>
                <form className="space-y-4" onSubmit={(e) => handleBatchTuitionSubmit(e)}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Tháng <span className="text-red-500">*</span></label>
                    <input
                      type="month"
                      className={`w-full px-3 py-2 border ${formErrors.batchMonth ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchMonth}
                      onChange={(e) => setBatchMonth(e.target.value)}
                      required
                    />
                    {formErrors.batchMonth && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchMonth}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Số tiền học phí <span className="text-red-500">*</span></label>
                    <input
                      type="number"
                      className={`w-full px-3 py-2 border ${formErrors.batchAmount ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      placeholder="Nhập số tiền"
                      value={batchAmount}
                      onChange={(e) => setBatchAmount(e.target.value)}
                      required
                      min="0"
                    />
                    {formErrors.batchAmount && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchAmount}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Khối lớp <span className="text-red-500">*</span></label>
                    <select
                      className={`w-full px-3 py-2 border ${formErrors.batchClass ? 'border-red-500' : 'border-gray-300'} rounded-md`}
                      value={batchClass}
                      onChange={(e) => setBatchClass(e.target.value)}
                      required
                    >
                      <option value="">Chọn khối lớp</option>
                      <option value="10">Lớp 10</option>
                      <option value="11">Lớp 11</option>
                      <option value="12">Lớp 12</option>
                    </select>
                    {formErrors.batchClass && (
                      <p className="mt-1 text-sm text-red-500 flex items-center">
                        <AlertCircle size={14} className="mr-1" /> {formErrors.batchClass}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      rows="3"
                      placeholder="Nhập ghi chú (nếu có)"
                      value={batchNote}
                      onChange={(e) => setBatchNote(e.target.value)}
                    ></textarea>
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Đang xử lý..." : "Tạo học phí hàng loạt"}
                  </button>
                  {formErrors.submit && (
                    <p className="mt-1 text-sm text-red-500 flex items-center">
                      <AlertCircle size={14} className="mr-1" /> {formErrors.submit}
                    </p>
                  )}
                </form>
              </div>
            )}
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default ClassTuitionList;
