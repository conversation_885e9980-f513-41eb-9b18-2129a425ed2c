'use strict';

import { Model } from 'sequelize';

export default (sequelize, DataTypes) => {
  class ClassTuition extends Model {
    static associate(models) {
      // M<PERSON>i quan hệ với bảng Class
      ClassTuition.belongsTo(models.Class, {
        foreignKey: 'classId',
        as: 'class'
      });
    }
  }

  ClassTuition.init({
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    classId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'class',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    month: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Tháng áp dụng học phí (định dạng YYYY-MM)'
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: 'Số tiền học phí'
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '<PERSON><PERSON> chú về học phí'
    },
    createdAt: {
      allowNull: false,
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      allowNull: false,
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'ClassTuition',
    tableName: 'classTuition',
    timestamps: true
  });

  return ClassTuition;
};
